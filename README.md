# 巴基斯坦支付集成项目

本项目提供了JazzCash和Easypaisa两个巴基斯坦主流支付渠道的集成示例，支持Android和iOS平台。

## 项目结构

```
├── PaymentAndroid/       # Android支付集成项目
└── PaymentiOS/           # iOS支付集成项目
```

## 支付渠道

本项目支持以下支付渠道：

1. **JazzCash** - 巴基斯坦领先的数字金融服务提供商
2. **Easypaisa** - 巴基斯坦最大的移动支付平台

## 支付方式

每个支付渠道支持两种支付方式：

1. **页面重定向方式** - 支持银行卡支付，通过WebView加载支付网关页面
2. **API方式** - 仅支持钱包支付，直接调用支付API

## 技术架构

### Android项目

#### 主要类

- `MainActivity`: 主界面，用于选择支付渠道、输入支付信息和选择支付方式
- `PaymentUtils`: 支付工具类，包含JazzCash和Easypaisa的支付工具方法
- `PaymentActivity`: 处理JazzCash页面重定向支付
- `ApiPaymentActivity`: 处理JazzCash API支付
- `EasypaisaActivity`: 处理Easypaisa页面重定向支付
- `EasypaisaApiActivity`: 处理Easypaisa API支付

#### 支付流程

1. **用户输入**
   - 选择支付渠道（JazzCash或Easypaisa）
   - 输入支付金额和描述
   - 选择支付方式（页面重定向或API）

2. **JazzCash页面重定向支付**
   - 生成交易参考号和交易时间
   - 准备支付请求数据并计算安全哈希
   - 通过WebView加载JazzCash支付页面
   - 处理支付回调并显示结果

3. **JazzCash API支付**
   - 准备API支付请求数据
   - 发送支付请求到JazzCash API
   - 处理API响应并显示结果

4. **Easypaisa页面重定向支付**
   - 生成订单ID
   - 准备支付请求数据并计算安全哈希
   - 通过WebView加载Easypaisa支付页面
   - 处理支付回调并显示结果

5. **Easypaisa API支付**
   - 输入用户Easypaisa钱包手机号码
   - 准备API支付请求数据
   - 发送支付请求到Easypaisa API
   - 处理API响应并显示结果

### iOS项目

#### 主要类

- `MainViewController`: 主界面，用于选择支付渠道、输入支付信息和选择支付方式
- `PaymentUtils`: 支付工具类，包含JazzCash和Easypaisa的支付工具方法
- `PaymentViewController`: 处理JazzCash页面重定向支付
- `ApiPaymentViewController`: 处理JazzCash API支付
- `EasypaisaViewController`: 处理Easypaisa页面重定向支付

#### 支付流程

1. **用户输入**
   - 选择支付渠道（JazzCash或Easypaisa）
   - 输入支付金额和描述
   - 选择支付方式（页面重定向或API）

2. **JazzCash页面重定向支付**
   - 生成交易参考号和交易时间
   - 准备支付请求数据并计算安全哈希
   - 通过WKWebView加载JazzCash支付页面
   - 处理支付回调并通过通知机制显示结果

3. **JazzCash API支付**
   - 准备API支付请求数据
   - 发送支付请求到JazzCash API
   - 处理API响应并通过通知机制显示结果

4. **Easypaisa页面重定向支付**
   - 生成订单ID
   - 准备支付请求数据并计算安全哈希
   - 通过WKWebView加载Easypaisa支付页面
   - 处理支付回调并通过通知机制显示结果

## 实现细节

### 安全哈希生成

两个支付渠道都需要生成安全哈希以验证请求的完整性：

1. **JazzCash**
   - 按键排序请求参数
   - 将值用&连接
   - 添加完整性盐值
   - 使用HMAC-SHA256算法生成哈希

2. **Easypaisa**
   - 连接特定字段值
   - 使用HMAC-SHA256算法和商户密钥生成哈希

### 支付回调处理

1. **JazzCash**
   - 监听包含`jazzcash/callback`的URL
   - 解析响应参数
   - 检查响应码（000表示成功）

2. **Easypaisa**
   - 监听包含`easypaisa/callback`的URL
   - 解析响应参数
   - 检查响应码（0000表示成功）

## 配置说明

### JazzCash配置

需要从JazzCash获取以下参数：

```
INTEGRITY_SALT = "123456789" // 替换为实际盐值
MERCHANT_ID = "MC12345"      // 替换为实际商户ID
PASSWORD = "password123"     // 替换为实际密码
RETURN_URL = "http://your-callback-url/jazzcash/callback" // 替换为实际回调URL
```

### Easypaisa配置

需要从Easypaisa获取以下参数：

```
MERCHANT_ID = "EP12345"          // 替换为实际商户ID
MERCHANT_KEY = "easypaisa_key_123" // 替换为实际商户密钥
RETURN_URL = "http://your-callback-url/easypaisa/callback" // 替换为实际回调URL
```

## 测试环境

- JazzCash测试网关: `https://sandbox.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform`
- Easypaisa测试网关: `https://easypaisa.sandbox.com/payment`
- Easypaisa API测试端点: `https://easypaisa.sandbox.com/api/payment`

## 生产环境

- JazzCash生产网关: `https://payments.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform`
- Easypaisa生产网关: `https://easypaisa.com.pk/payment`
- Easypaisa API生产端点: `https://easypaisa.com.pk/api/payment`

## 注意事项

1. 测试前请确保已配置正确的商户信息
2. 回调URL必须可以从互联网访问
3. 对于开发测试，可以使用ngrok等工具创建临时公网URL
4. 生产环境必须使用HTTPS协议
5. 请勿在生产环境中使用示例中的测试凭据

## 错误处理

常见错误及解决方案：

1. **支付网关无法加载**
   - 检查网络连接
   - 验证支付网关URL是否正确

2. **支付失败**
   - 检查商户凭据是否正确
   - 验证安全哈希计算是否正确
   - 确认回调URL是否可访问

3. **回调未触发**
   - 检查回调URL是否正确配置
   - 确认WebView是否正确处理URL拦截

4. **API请求失败**
   - 检查请求参数格式是否正确
   - 验证网络连接是否正常
   - 确认API端点URL是否正确

## 如何贡献

欢迎提交问题报告和改进建议。如需贡献代码，请遵循以下步骤：

1. Fork本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开Pull Request

## 许可证

本项目采用MIT许可证 - 详见LICENSE文件
