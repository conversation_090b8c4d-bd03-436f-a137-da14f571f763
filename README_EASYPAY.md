# Easypay 支付集成指南

本文档介绍如何在Android应用中集成Easypay支付功能。

## 集成概述

Easypay提供两种集成方式：
1. **插件集成（Plugin Integration）**：通过网页重定向实现，用户会在应用内被重定向到Easypay支付页面。
2. **开放API集成（Open API）**：通过API直接与Easypay服务器通信，无需重定向用户。

本项目已实现两种方式：
- `EasypaisaActivity.java` - 插件集成
- `EasypaisaApiActivity.kt` - 开放API集成

## 配置参数

在使用Easypay支付前，您需要从Easypay获取以下参数：

1. **商店ID（Store ID）**：您的Easypay商店唯一标识符
2. **商户哈希密钥（Merchant Hash Key）**：用于生成安全哈希

您需要在两个位置更新这些参数：
- `PaymentUtils.kt` 中的 `Easypaisa` 对象
- `EasypaisaActivity.java` 中的常量

## 插件集成流程

插件集成的流程如下：

1. 准备支付请求数据（金额、订单参考号等）
2. 通过WebView加载Easypay支付页面
3. 用户完成支付操作
4. 接收回调和处理支付结果

### 使用示例

```java
// 创建支付意图
Intent intent = new Intent(this, EasypaisaActivity.class);
intent.putExtra("amount", "100"); // 金额（巴基斯坦卢比）
intent.putExtra("description", "购买商品"); // 交易描述
intent.putExtra("mobileNumber", "03XXXXXXXXX"); // 用户手机号（可选）
intent.putExtra("email", "<EMAIL>"); // 用户邮箱（可选）
startActivityForResult(intent, PAYMENT_REQUEST_CODE);

// 在onActivityResult中处理支付结果
@Override
protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == PAYMENT_REQUEST_CODE) {
        if (resultCode == RESULT_OK) {
            // 支付成功
            String txnRefNo = data.getStringExtra("txnRefNo");
            String message = data.getStringExtra("message");
            // 处理成功逻辑
        } else {
            // 支付失败或取消
            String message = data.getStringExtra("message");
            // 处理失败逻辑
        }
    }
}
```

## 开放API集成流程

开放API集成的流程如下：

1. 准备API请求参数
2. 发送请求到Easypay服务器
3. 处理API响应

### 使用示例

```java
// 创建支付意图
Intent intent = new Intent(this, EasypaisaApiActivity.class);
intent.putExtra("amount", "100"); // 金额（巴基斯坦卢比）
intent.putExtra("description", "购买商品"); // 交易描述
intent.putExtra("email", "<EMAIL>"); // 用户邮箱（可选）
startActivityForResult(intent, API_PAYMENT_REQUEST_CODE);

// 在onActivityResult中处理支付结果
@Override
protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == API_PAYMENT_REQUEST_CODE) {
        if (resultCode == RESULT_OK) {
            // 支付成功
            String txnRefNo = data.getStringExtra("txnRefNo");
            String message = data.getStringExtra("message");
            String transactionId = data.getStringExtra("transactionId");
            // 处理成功逻辑
        } else {
            // 支付失败或取消
            String message = data.getStringExtra("message");
            // 处理失败逻辑
        }
    }
}
```

## 测试与生产环境

Easypay提供测试环境和生产环境：

- **测试环境URL**：https://easypaystg.easypaisa.com.pk/easypay/Index.jsf
- **生产环境URL**：https://easypay.easypaisa.com.pk/easypay/Index.jsf

在`EasypaisaActivity.java`中，您可以通过`isTestMode`变量切换环境：

```java
private boolean isTestMode = true; // 设置为false使用生产环境
```

## 支付方式

Easypay支持多种支付方式，可通过`paymentMethod`参数设置：

- `OTC_PAYMENT_METHOD` - 柜台支付
- `MA_PAYMENT_METHOD` - 移动账户支付
- `CC_PAYMENT_METHOD` - 信用卡支付
- `QR_PAYMENT_METHOD` - 二维码支付

## 安全哈希生成

安全哈希用于验证请求的完整性。生成步骤：

1. 将所有参数按字母顺序排序
2. 构建格式为`key1=value1&key2=value2`的字符串
3. 使用AES/ECB/PKCS5Padding算法和商户密钥加密
4. 将加密结果转换为Base64编码

## 常见问题

### 1. 支付失败

可能的原因：
- 无效的商店ID
- 网络连接问题
- 参数格式错误

解决方案：
- 检查商店ID是否正确
- 确认网络连接稳定
- 确保所有必填参数格式正确

### 2. 回调处理错误

可能的原因：
- 回调URL配置错误
- WebView配置问题

解决方案：
- 确认回调URL正确
- 检查WebView的JavaScript和Cookie设置

## 更多资源

- 完整的Easypay集成文档请参考官方文档
- 如有问题，请联系Easypay技术支持 