1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.paymentapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:6:5-67
11-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:7:5-79
12-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:7:22-76
13
14    <permission
14-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
15        android:name="com.example.paymentapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.paymentapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
19
20    <application
20-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:9:5-38:19
21        android:allowBackup="true"
21-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:10:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
23        android:debuggable="true"
24        android:icon="@mipmap/ic_launcher"
24-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:11:9-43
25        android:label="@string/app_name"
25-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:12:9-41
26        android:networkSecurityConfig="@xml/network_security_config"
26-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:13:9-69
27        android:roundIcon="@mipmap/ic_launcher"
27-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:14:9-48
28        android:supportsRtl="true"
28-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:15:9-35
29        android:theme="@style/Theme.PaymentApp" >
29-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:16:9-48
30        <activity
30-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:18:9-25:20
31            android:name="com.example.paymentapp.MainActivity"
31-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:19:13-41
32            android:exported="true" >
32-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:20:13-36
33            <intent-filter>
33-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:21:13-24:29
34                <action android:name="android.intent.action.MAIN" />
34-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:22:17-69
34-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:22:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:23:17-77
36-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:23:27-74
37            </intent-filter>
38        </activity>
39        <activity
39-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:26:9-28:40
40            android:name="com.example.paymentapp.JazzCashActivity"
40-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:27:13-45
41            android:exported="false" />
41-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:28:13-37
42        <activity
42-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:29:9-31:40
43            android:name="com.example.paymentapp.JazzCashApiActivity"
43-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:30:13-48
44            android:exported="false" />
44-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:31:13-37
45        <activity
45-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:32:9-34:40
46            android:name="com.example.paymentapp.EasypaisaActivity"
46-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:33:13-46
47            android:exported="false" />
47-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:34:13-37
48        <activity
48-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:35:9-37:40
49            android:name="com.example.paymentapp.EasypaisaApiActivity"
49-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:36:13-49
50            android:exported="false" />
50-->/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:37:13-37
51
52        <provider
52-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
54            android:authorities="com.example.paymentapp.androidx-startup"
54-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
62        </provider>
63    </application>
64
65</manifest>
