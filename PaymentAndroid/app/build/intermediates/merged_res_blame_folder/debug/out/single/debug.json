[{"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/layout_dialog_debug_info.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/layout/dialog_debug_info.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/drawable_rounded_button.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/drawable/rounded_button.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/layout_activity_easypaisa.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/layout/activity_easypaisa.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/xml_backup_rules.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/xml/backup_rules.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/xml_network_security_config.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/xml/network_security_config.xml"}, {"merged": "com.example.paymentapp-merged_res-29:/layout_activity_jazzcash.xml.flat", "source": "com.example.paymentapp-main-31:/layout/activity_jazzcash.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/layout_activity_main.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/layout/activity_main.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/xml_data_extraction_rules.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/xml/data_extraction_rules.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/layout_activity_jazzcash.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/layout/activity_jazzcash.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/layout_activity_jazzcash_api.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/layout/activity_jazzcash_api.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-merged_res-29:/layout_activity_easypaisa_api.xml.flat", "source": "/Users/<USER>/.gradle/daemon/7.5/com.example.paymentapp-main-31:/layout/activity_easypaisa_api.xml"}]