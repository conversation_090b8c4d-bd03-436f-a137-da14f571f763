{"logs": [{"outputFile": "com.example.paymentapp-mergeDebugResources-27:/values-lt/values-lt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1163,1262,1340,1405,1515,1578,1650,1709,1783,1844,1898,2022,2083,2145,2199,2277,2411,2499,2583,2724,2803,2887,2984,3040,3094,3160,3235,3314,3402,3478,3556,3629,3706,3793,3874,3964,4056,4128,4209,4301,4356,4422,4507,4594,4656,4720,4783,4894,5009,5110,5224,5284,5342", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,96,55,53,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,59,57,81", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1158,1257,1335,1400,1510,1573,1645,1704,1778,1839,1893,2017,2078,2140,2194,2272,2406,2494,2578,2719,2798,2882,2979,3035,3089,3155,3230,3309,3397,3473,3551,3624,3701,3788,3869,3959,4051,4123,4204,4296,4351,4417,4502,4589,4651,4715,4778,4889,5004,5105,5219,5279,5337,5419"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3194,3273,3351,3434,3528,4369,4465,4583,4667,4733,4832,4910,4975,5085,5148,5220,5279,5353,5414,5468,5592,5653,5715,5769,5847,5981,6069,6153,6294,6373,6457,6554,6610,6664,6730,6805,6884,6972,7048,7126,7199,7276,7363,7444,7534,7626,7698,7779,7871,7926,7992,8077,8164,8226,8290,8353,8464,8579,8680,8794,8854,8912", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,96,55,53,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,59,57,81", "endOffsets": "420,3268,3346,3429,3523,3613,4460,4578,4662,4728,4827,4905,4970,5080,5143,5215,5274,5348,5409,5463,5587,5648,5710,5764,5842,5976,6064,6148,6289,6368,6452,6549,6605,6659,6725,6800,6879,6967,7043,7121,7194,7271,7358,7439,7529,7621,7693,7774,7866,7921,7987,8072,8159,8221,8285,8348,8459,8574,8675,8789,8849,8907,8989"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/3c82f0ccdbe9412677623492bdd8999d/transformed/appcompat-1.6.1/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,8994", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,9073"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "40,41,42,43,44,45,46,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3618,3716,3826,3925,4028,4139,4249,9078", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3711,3821,3920,4023,4134,4244,4364,9174"}}]}]}