{"logs": [{"outputFile": "com.example.paymentapp-mergeDebugResources-27:/values-ml/values-ml.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/res/values-ml/values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1087,1186,1252,1312,1414,1476,1552,1610,1688,1753,1807,1924,1988,2052,2106,2186,2320,2406,2495,2631,2716,2804,2899,2957,3009,3075,3154,3236,3327,3403,3480,3557,3628,3735,3815,3912,4012,4086,4167,4272,4330,4397,4488,4580,4642,4706,4769,4872,4988,5093,5209,5271,5327", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,94,57,51,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,61,55,83", "endOffsets": "266,346,429,516,622,721,815,925,1017,1082,1181,1247,1307,1409,1471,1547,1605,1683,1748,1802,1919,1983,2047,2101,2181,2315,2401,2490,2626,2711,2799,2894,2952,3004,3070,3149,3231,3322,3398,3475,3552,3623,3730,3810,3907,4007,4081,4162,4267,4325,4392,4483,4575,4637,4701,4764,4867,4983,5088,5204,5266,5322,5406"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3150,3233,3320,3426,4262,4356,4466,4558,4623,4722,4788,4848,4950,5012,5088,5146,5224,5289,5343,5460,5524,5588,5642,5722,5856,5942,6031,6167,6252,6340,6435,6493,6545,6611,6690,6772,6863,6939,7016,7093,7164,7271,7351,7448,7548,7622,7703,7808,7866,7933,8024,8116,8178,8242,8305,8408,8524,8629,8745,8807,8863", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,94,57,51,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,61,55,83", "endOffsets": "316,3145,3228,3315,3421,3520,4351,4461,4553,4618,4717,4783,4843,4945,5007,5083,5141,5219,5284,5338,5455,5519,5583,5637,5717,5851,5937,6026,6162,6247,6335,6430,6488,6540,6606,6685,6767,6858,6934,7011,7088,7159,7266,7346,7443,7543,7617,7698,7803,7861,7928,8019,8111,8173,8237,8300,8403,8519,8624,8740,8802,8858,8942"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/3c82f0ccdbe9412677623492bdd8999d/transformed/appcompat-1.6.1/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,8947", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,9025"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3525,3627,3730,3832,3936,4039,4140,9030", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3622,3725,3827,3931,4034,4135,4257,9126"}}]}]}