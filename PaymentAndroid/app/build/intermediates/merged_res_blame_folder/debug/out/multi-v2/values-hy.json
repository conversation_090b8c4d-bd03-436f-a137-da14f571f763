{"logs": [{"outputFile": "com.example.paymentapp-mergeDebugResources-27:/values-hy/values-hy.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1047,1144,1229,1291,1378,1440,1504,1565,1632,1693,1747,1869,1926,1986,2040,2121,2256,2340,2425,2561,2636,2711,2806,2862,2915,2981,3055,3135,3221,3292,3368,3444,3521,3609,3689,3785,3881,3955,4033,4133,4184,4253,4340,4431,4493,4557,4620,4725,4826,4926,5031,5091,5148", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,94,55,52,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,59,56,84", "endOffsets": "260,336,412,492,584,672,767,897,978,1042,1139,1224,1286,1373,1435,1499,1560,1627,1688,1742,1864,1921,1981,2035,2116,2251,2335,2420,2556,2631,2706,2801,2857,2910,2976,3050,3130,3216,3287,3363,3439,3516,3604,3684,3780,3876,3950,4028,4128,4179,4248,4335,4426,4488,4552,4615,4720,4821,4921,5026,5086,5143,5228"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3100,3176,3256,3348,4156,4251,4381,4462,4526,4623,4708,4770,4857,4919,4983,5044,5111,5172,5226,5348,5405,5465,5519,5600,5735,5819,5904,6040,6115,6190,6285,6341,6394,6460,6534,6614,6700,6771,6847,6923,7000,7088,7168,7264,7360,7434,7512,7612,7663,7732,7819,7910,7972,8036,8099,8204,8305,8405,8510,8570,8627", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,94,55,52,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,59,56,84", "endOffsets": "310,3095,3171,3251,3343,3431,4246,4376,4457,4521,4618,4703,4765,4852,4914,4978,5039,5106,5167,5221,5343,5400,5460,5514,5595,5730,5814,5899,6035,6110,6185,6280,6336,6389,6455,6529,6609,6695,6766,6842,6918,6995,7083,7163,7259,7355,7429,7507,7607,7658,7727,7814,7905,7967,8031,8094,8199,8300,8400,8505,8565,8622,8707"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3436,3536,3641,3739,3838,3943,4045,8795", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3531,3636,3734,3833,3938,4040,4151,8891"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/3c82f0ccdbe9412677623492bdd8999d/transformed/appcompat-1.6.1/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,8712", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,8790"}}]}]}