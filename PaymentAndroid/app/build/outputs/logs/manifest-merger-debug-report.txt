-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
MERGED from [com.google.android.material:material:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/ea8d42e5b73c49accdfb7df40a64309d/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/3c82f0ccdbe9412677623492bdd8999d/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f074803da9dd70383b5fce031034e010/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/560ccd516cd6de1b6638828af3eebb71/transformed/fragment-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/transforms-3/22018c301c123d1567467bed2c83f6f5/transformed/jetified-activity-1.6.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/915ec4a69ca816f171cf931e1025c8d7/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/e38ee089648600abdf2b1b34f81c65de/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/41f5bc421223c690c51c8e734819e8da/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/61bcdcb940fbb590286c186b857db46a/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/61017ffc1d01ff413e7d898b997b69c2/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2deddba07ed6cbf9631edbc4999ddc1a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/9c163b3c0f223d55e9867cb5b985a609/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1b0dec12bf907a0ce71737e1bcb2c2d1/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07dd9cf1e5e9d3a0834cbd1953c0cf54/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f0cd2140268ad88a6efad3ac05386ab8/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/74257452692e5c2c34a27252223c312c/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/711082ff04b262488c989f0a20113488/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/205605b76180136cc8f42c99fdbd78ad/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/be71a61c958298f1afb446b70a98dc88/transformed/jetified-lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/ef547f10b0af2710fc43ad4b8856e268/transformed/jetified-core-ktx-1.10.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b62b73b8253958177840ed34229d35c/transformed/jetified-savedstate-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/7b46957b0e799c37f86e9aaafaf1a9d1/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0fd9d8751b56a2d26f0d29d3b3e9f257/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/f0e7d6d799d6dcb9d78b59a41c74ca7d/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/bf7e18ef8301d1d7f6bc63773afcfb9c/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/0ccee948c0b3b2adcf6a193892947f3d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d311093c955cf60ed9075fc4028f84d1/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/8a642dd31dddf0535e9c303cc38f8bf5/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/603b40d55b283007ec925a6b3494c797/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-3/4ee6b80c7f1b747b930e882ed6e406f7/transformed/core-runtime-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/14cf0f12229a19f9854ef132476f78e1/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f9b79652572637781f542df77a91d7cc/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/43551e7787504844afc94f958065b3d6/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0ef88f649878603ab3d2e983b9bc7b1/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
	package
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:4:5-37
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:1-40:12
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:6:5-67
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:7:5-79
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:7:22-76
application
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:9:5-38:19
MERGED from [com.google.android.material:material:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/0ccee948c0b3b2adcf6a193892947f3d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/0ccee948c0b3b2adcf6a193892947f3d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:15:9-35
	android:label
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:12:9-41
	android:roundIcon
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:14:9-48
	tools:targetApi
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:17:9-29
	android:icon
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:11:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:10:9-35
	android:theme
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:16:9-48
	android:networkSecurityConfig
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:13:9-69
activity#com.example.paymentapp.MainActivity
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:18:9-25:20
	android:exported
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:20:13-36
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:19:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:21:13-24:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:22:17-69
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:23:17-77
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:23:27-74
activity#com.example.paymentapp.JazzCashActivity
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:26:9-28:40
	android:exported
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:28:13-37
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:27:13-45
activity#com.example.paymentapp.JazzCashApiActivity
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:29:9-31:40
	android:exported
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:31:13-37
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:30:13-48
activity#com.example.paymentapp.EasypaisaActivity
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:34:13-37
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:33:13-46
activity#com.example.paymentapp.EasypaisaApiActivity
ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:35:9-37:40
	android:exported
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:37:13-37
	android:name
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml:36:13-49
uses-sdk
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
MERGED from [com.google.android.material:material:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] /Users/<USER>/.gradle/caches/transforms-3/8c486cec99d4fccb7893779ad5d22534/transformed/material-1.9.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-3/0048526f799978d53c8838b69626f826/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/ea8d42e5b73c49accdfb7df40a64309d/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/ea8d42e5b73c49accdfb7df40a64309d/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/3c82f0ccdbe9412677623492bdd8999d/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/transforms-3/3c82f0ccdbe9412677623492bdd8999d/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f074803da9dd70383b5fce031034e010/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f074803da9dd70383b5fce031034e010/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/560ccd516cd6de1b6638828af3eebb71/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/transforms-3/560ccd516cd6de1b6638828af3eebb71/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/transforms-3/22018c301c123d1567467bed2c83f6f5/transformed/jetified-activity-1.6.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/transforms-3/22018c301c123d1567467bed2c83f6f5/transformed/jetified-activity-1.6.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/915ec4a69ca816f171cf931e1025c8d7/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/915ec4a69ca816f171cf931e1025c8d7/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/e38ee089648600abdf2b1b34f81c65de/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/e38ee089648600abdf2b1b34f81c65de/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/41f5bc421223c690c51c8e734819e8da/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/41f5bc421223c690c51c8e734819e8da/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/61bcdcb940fbb590286c186b857db46a/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/61bcdcb940fbb590286c186b857db46a/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/61017ffc1d01ff413e7d898b997b69c2/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/61017ffc1d01ff413e7d898b997b69c2/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2deddba07ed6cbf9631edbc4999ddc1a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/2deddba07ed6cbf9631edbc4999ddc1a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/9c163b3c0f223d55e9867cb5b985a609/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/9c163b3c0f223d55e9867cb5b985a609/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1b0dec12bf907a0ce71737e1bcb2c2d1/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/1b0dec12bf907a0ce71737e1bcb2c2d1/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07dd9cf1e5e9d3a0834cbd1953c0cf54/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07dd9cf1e5e9d3a0834cbd1953c0cf54/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f0cd2140268ad88a6efad3ac05386ab8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f0cd2140268ad88a6efad3ac05386ab8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/74257452692e5c2c34a27252223c312c/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/74257452692e5c2c34a27252223c312c/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/711082ff04b262488c989f0a20113488/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/711082ff04b262488c989f0a20113488/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/205605b76180136cc8f42c99fdbd78ad/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/205605b76180136cc8f42c99fdbd78ad/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/be71a61c958298f1afb446b70a98dc88/transformed/jetified-lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/be71a61c958298f1afb446b70a98dc88/transformed/jetified-lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/ef547f10b0af2710fc43ad4b8856e268/transformed/jetified-core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/ef547f10b0af2710fc43ad4b8856e268/transformed/jetified-core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b62b73b8253958177840ed34229d35c/transformed/jetified-savedstate-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b62b73b8253958177840ed34229d35c/transformed/jetified-savedstate-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/7b46957b0e799c37f86e9aaafaf1a9d1/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/7b46957b0e799c37f86e9aaafaf1a9d1/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0fd9d8751b56a2d26f0d29d3b3e9f257/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0fd9d8751b56a2d26f0d29d3b3e9f257/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/f0e7d6d799d6dcb9d78b59a41c74ca7d/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/f0e7d6d799d6dcb9d78b59a41c74ca7d/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/bf7e18ef8301d1d7f6bc63773afcfb9c/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/bf7e18ef8301d1d7f6bc63773afcfb9c/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/0ccee948c0b3b2adcf6a193892947f3d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/0ccee948c0b3b2adcf6a193892947f3d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d311093c955cf60ed9075fc4028f84d1/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d311093c955cf60ed9075fc4028f84d1/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/b2a96e02639f78061d3b89549351b289/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/8a642dd31dddf0535e9c303cc38f8bf5/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/8a642dd31dddf0535e9c303cc38f8bf5/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/603b40d55b283007ec925a6b3494c797/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/transforms-3/603b40d55b283007ec925a6b3494c797/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-3/4ee6b80c7f1b747b930e882ed6e406f7/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-3/4ee6b80c7f1b747b930e882ed6e406f7/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/14cf0f12229a19f9854ef132476f78e1/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/14cf0f12229a19f9854ef132476f78e1/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f9b79652572637781f542df77a91d7cc/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f9b79652572637781f542df77a91d7cc/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/43551e7787504844afc94f958065b3d6/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/43551e7787504844afc94f958065b3d6/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0ef88f649878603ab3d2e983b9bc7b1/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a0ef88f649878603ab3d2e983b9bc7b1/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/PaymentAndroid/app/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/0ccee948c0b3b2adcf6a193892947f3d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/0ccee948c0b3b2adcf6a193892947f3d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/63c607b08e989b504881fb3b6da34bd7/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
permission#com.example.paymentapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.paymentapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/6fe3e64a80094b4316c7006f0bcdcf47/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/dc702536bfec8aa0fbca20ee53515ddb/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
