<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="#F5F5F5"
    tools:context=".EasypaisaApiActivity">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardUseCompatPadding="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Easypaisa API支付"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="#3F51B5"
                android:gravity="center"
                android:layout_marginBottom="24dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/tvDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="请输入您的Easypaisa钱包手机号码完成支付"
                android:textSize="16sp"
                android:textColor="#555555"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toBottomOf="@id/tvTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilMobileNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:hint="手机号码（例如：03XXXXXXXXX）"
                app:boxStrokeColor="#3F51B5"
                app:hintTextColor="#3F51B5"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:layout_constraintTop_toBottomOf="@id/tvDescription"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <EditText
                    android:id="@+id/etMobileNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:maxLength="11" />
            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/btnPay"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="确认支付"
                android:textSize="18sp"
                android:textColor="#FFFFFF"
                android:background="@drawable/rounded_button"
                android:layout_marginTop="32dp"
                app:layout_constraintTop_toBottomOf="@id/tilMobileNumber"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:indeterminateTint="#3F51B5"
                android:layout_marginTop="24dp"
                app:layout_constraintTop_toBottomOf="@id/btnPay"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="请输入手机号码并点击确认支付按钮"
                android:textSize="16sp"
                android:textColor="#555555"
                android:gravity="center"
                android:layout_marginTop="24dp"
                app:layout_constraintTop_toBottomOf="@id/progressBar"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 