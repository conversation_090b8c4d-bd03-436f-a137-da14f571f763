<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="#F5F5F5"
    tools:context=".MainActivity">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardUseCompatPadding="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="巴基斯坦支付集成演示"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="#3F51B5"
                android:gravity="center"
                android:layout_marginBottom="24dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/tvPaymentChannel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="选择支付渠道"
                android:textSize="18sp"
                android:textColor="#3F51B5"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toBottomOf="@id/tvTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <Spinner
                android:id="@+id/spPaymentChannel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@android:drawable/btn_dropdown"
                android:spinnerMode="dropdown"
                app:layout_constraintTop_toBottomOf="@id/tvPaymentChannel"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/tvPaymentMethod"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="支付方式"
                android:textSize="18sp"
                android:textColor="#3F51B5"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toBottomOf="@id/spPaymentChannel"
                app:layout_constraintStart_toStartOf="parent" />

            <RadioGroup
                android:id="@+id/rgPaymentMethod"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toBottomOf="@id/tvPaymentMethod"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <RadioButton
                    android:id="@+id/rbRedirect"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="页面重定向"
                    android:checked="true" />

                <RadioButton
                    android:id="@+id/rbApi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="API" />
            </RadioGroup>

            <TextView
                android:id="@+id/tvTxnType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="交易类型"
                android:textSize="18sp"
                android:textColor="#3F51B5"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                app:layout_constraintTop_toBottomOf="@id/rgPaymentMethod"
                app:layout_constraintStart_toStartOf="parent" />

            <RadioGroup
                android:id="@+id/rgTxnType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toBottomOf="@id/tvTxnType"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <RadioButton
                    android:id="@+id/rbMPAY"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="MPAY"
                    android:checked="true" />

                <RadioButton
                    android:id="@+id/rbMWALLET"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="MWALLET" />
                
                <RadioButton
                    android:id="@+id/rbMIGS"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="MIGS" />
            </RadioGroup>

            <TextView
                android:id="@+id/tvPaymentDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="支付详情"
                android:textSize="18sp"
                android:textColor="#3F51B5"
                android:layout_marginTop="24dp"
                app:layout_constraintTop_toBottomOf="@id/rgTxnType"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:hint="支付金额（PKR）"
                app:boxStrokeColor="#3F51B5"
                app:hintTextColor="#3F51B5"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:layout_constraintTop_toBottomOf="@id/tvPaymentDetails"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <EditText
                    android:id="@+id/etAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal"
                    android:text="100" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="交易描述"
                app:boxStrokeColor="#3F51B5"
                app:hintTextColor="#3F51B5"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                app:layout_constraintTop_toBottomOf="@id/tilAmount"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <EditText
                    android:id="@+id/etDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:text="测试交易" />
            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/btnPay"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="开始支付"
                android:textSize="18sp"
                android:textColor="#FFFFFF"
                android:background="@drawable/rounded_button"
                android:layout_marginTop="24dp"
                app:layout_constraintTop_toBottomOf="@id/tilDescription"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 