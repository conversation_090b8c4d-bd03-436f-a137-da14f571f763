package com.example.paymentapp

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.Executors

/**
 * API支付活动
 * 
 * 该活动负责：
 * 1. 收集用户手机号和身份证号
 * 2. 将支付请求发送到后端服务器
 * 3. 显示支付结果
 */
class JazzCashApiActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "ApiPaymentActivity"
        
        // 服务器API地址
        private const val SERVER_API_URL = "http://************:8080/jazzcash/api/create-payment"
        private const val SERVER_STATUS_URL = "http://************:8080/jazzcash/api/transaction/"
    }
    
    private lateinit var etMobileNumber: EditText
    private lateinit var etCnic: EditText
    private lateinit var etAmount: EditText
    private lateinit var etDescription: EditText
    private lateinit var btnSubmit: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var tvStatus: TextView
    
    // 支付金额和描述（从主活动传入）
    private var amount: String = ""
    private var description: String = ""
    private var txnRefNo: String? = null
    
    // 线程池，用于执行网络请求
    private val executor = Executors.newSingleThreadExecutor()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_jazzcash_api)
        
        Log.d(TAG, "ApiPaymentActivity 已创建")
        
        // 初始化视图
        etMobileNumber = findViewById(R.id.etMobileNumber)
        etCnic = findViewById(R.id.etCnic)
        etAmount = findViewById(R.id.etAmount)
        etDescription = findViewById(R.id.etDescription)
        btnSubmit = findViewById(R.id.btnSubmit)
        progressBar = findViewById(R.id.progressBar)
        tvStatus = findViewById(R.id.tvStatus)
        
        // 从Intent获取支付金额和描述
        amount = intent.getStringExtra("amount") ?: ""
        description = intent.getStringExtra("description") ?: ""
        
        // 预填充金额和描述
        etAmount.setText(amount)
        etDescription.setText(description)
        
        // 设置提交按钮点击事件
        btnSubmit.setOnClickListener {
            if (validateInputs()) {
                submitPayment()
            }
        }
    }
    
    /**
     * 验证输入
     */
    private fun validateInputs(): Boolean {
        val mobileNumber = etMobileNumber.text.toString().trim()
        val cnic = etCnic.text.toString().trim()
        val amount = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        
        if (TextUtils.isEmpty(mobileNumber)) {
            etMobileNumber.error = "请输入手机号"
            return false
        }
        
        if (mobileNumber.length != 11) {
            etMobileNumber.error = "手机号必须为11位"
            return false
        }
        
        if (TextUtils.isEmpty(cnic)) {
            etCnic.error = "请输入身份证号"
            return false
        }
        
//        if (cnic.length != 13) {
//            etCnic.error = "身份证号必须为13位"
//            return false
//        }
        
        if (TextUtils.isEmpty(amount)) {
            etAmount.error = "请输入金额"
            return false
        }
        
        try {
            val amountValue = amount.toDouble()
            if (amountValue <= 0) {
                etAmount.error = "金额必须大于0"
                return false
            }
        } catch (e: NumberFormatException) {
            etAmount.error = "请输入有效金额"
            return false
        }
        
        if (TextUtils.isEmpty(description)) {
            etDescription.error = "请输入描述"
            return false
        }
        
        return true
    }
    
    /**
     * 提交支付请求
     */
    private fun submitPayment() {
        // 显示进度条，禁用提交按钮
        progressBar.visibility = View.VISIBLE
        btnSubmit.isEnabled = false
        tvStatus.text = "正在处理支付请求..."
        
        val mobileNumber = etMobileNumber.text.toString().trim()
        val cnic = etCnic.text.toString().trim()
        val amount = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        
        // 在后台线程中执行网络请求
        executor.execute {
            try {
                // 准备JSON数据
                val jsonObject = JSONObject()
                jsonObject.put("mobileNumber", mobileNumber)
                jsonObject.put("cnic", cnic)
                jsonObject.put("amount", amount)
                jsonObject.put("description", description)
                
                // 发送HTTP POST请求
                val url = URL(SERVER_API_URL)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.setRequestProperty("Accept", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                
                // 写入请求体
                val writer = OutputStreamWriter(connection.outputStream)
                writer.write(jsonObject.toString())
                writer.flush()
                writer.close()
                
                // 获取响应
                val responseCode = connection.responseCode
                Log.d(TAG, "响应码: $responseCode")
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 读取响应
                    val reader = BufferedReader(InputStreamReader(connection.inputStream))
                    val response = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }
                    reader.close()
                    
                    val responseJson = response.toString()
                    Log.d(TAG, "响应: $responseJson")
                    
                    // 解析响应
                    val jsonResponse = JSONObject(responseJson)
                    val success = jsonResponse.optBoolean("success", false)
                    txnRefNo = jsonResponse.optString("txnRefNo")
                    
                    if (success) {
                        // 支付请求已发送到JazzCash，通知用户查看手机
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            showPaymentInstructionsDialog(txnRefNo)
                        }
                    } else {
                        // 支付请求失败，优先使用服务器提供的用户友好错误信息
                        val userMessage = jsonResponse.optString("userMessage", "")
                        val errorMessage = if (userMessage.isNotEmpty()) {
                            userMessage
                        } else {
                            jsonResponse.optString("error", "未知错误")
                        }
                        
                        // 记录原始错误信息用于调试
                        val originalErrorCode = jsonResponse.optString("pp_ResponseCode", "")
                        val originalErrorMessage = jsonResponse.optString("pp_ResponseMessage", "")
                        Log.e(TAG, "原始错误信息 - 代码: $originalErrorCode, 消息: $originalErrorMessage")
                        
                        // 在UI线程中显示错误
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            btnSubmit.isEnabled = true
                            tvStatus.text = "支付请求失败: $errorMessage"
                            showError("支付请求失败", errorMessage)
                        }
                    }
                } else {
                    // HTTP错误
                    val errorStream = connection.errorStream
                    val errorReader = BufferedReader(InputStreamReader(errorStream))
                    val errorResponse = StringBuilder()
                    var line: String?
                    while (errorReader.readLine().also { line = it } != null) {
                        errorResponse.append(line)
                    }
                    errorReader.close()
                    
                    Log.e(TAG, "HTTP错误: $responseCode, 响应: $errorResponse")
                    
                    // 在UI线程中显示错误
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        btnSubmit.isEnabled = true
                        tvStatus.text = "请求失败，HTTP错误码: $responseCode"
                        showError("请求失败", "HTTP错误码: $responseCode")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "发送支付请求时出错", e)
                
                // 在UI线程中显示错误
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    btnSubmit.isEnabled = true
                    tvStatus.text = "请求失败: ${e.message}"
                    showError("请求失败", e.message ?: "未知错误")
                }
            }
        }
    }
    
    /**
     * 显示支付指示对话框
     */
    private fun showPaymentInstructionsDialog(txnRefNo: String?) {
        val alertDialog = AlertDialog.Builder(this)
            .setTitle("支付请求已发送")
            .setMessage("请查看您的手机，按照JazzCash应用的提示完成支付。\n\n交易参考号: $txnRefNo")
            .setCancelable(false)
            .setPositiveButton("支付已完成") { _, _ ->
                // 用户点击支付已完成按钮，检查支付状态
                checkTransactionStatus(txnRefNo)
            }
            .setNegativeButton("取消支付") { _, _ ->
                // 用户点击取消支付按钮，返回结果
                val intent = Intent()
                intent.putExtra("success", false)
                intent.putExtra("message", "用户取消支付")
                intent.putExtra("txnRefNo", txnRefNo)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
            .create()
        
        alertDialog.show()
    }
    
    /**
     * 检查交易状态
     */
    private fun checkTransactionStatus(txnRefNo: String?) {
        if (txnRefNo.isNullOrEmpty()) {
            showError("状态查询失败", "交易参考号为空")
            return
        }
        
        progressBar.visibility = View.VISIBLE
        tvStatus.text = "正在查询交易状态..."
        
        executor.execute {
            try {
                // 发送HTTP GET请求查询交易状态
                val url = URL(SERVER_STATUS_URL + txnRefNo)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                
                val responseCode = connection.responseCode
                Log.d(TAG, "状态查询响应码: $responseCode")
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 读取响应
                    val reader = BufferedReader(InputStreamReader(connection.inputStream))
                    val response = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }
                    reader.close()
                    
                    val responseJson = response.toString()
                    Log.d(TAG, "状态查询响应: $responseJson")
                    
                    // 解析响应
                    val jsonResponse = JSONObject(responseJson)
                    val success = jsonResponse.optBoolean("success", false)
                    val status = jsonResponse.optString("status", "PENDING")
                    val message = jsonResponse.optString("message", "")
                    
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        
                        if (success || status == "COMPLETED") {
                            // 支付成功
                            showPaymentResult(true, message.ifEmpty { "支付成功" })
                        } else if (status == "FAILED") {
                            // 支付失败
                            showPaymentResult(false, message.ifEmpty { "支付失败" })
                        } else if (status == "NOT_FOUND") {
                            // 找不到交易，可能还没有收到回调
                            showConfirmationDialog(txnRefNo)
                        } else {
                            // 其他状态，询问用户
                            showConfirmationDialog(txnRefNo)
                        }
                    }
                } else {
                    // HTTP错误
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        showConfirmationDialog(txnRefNo)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "查询交易状态时出错", e)
                
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    showConfirmationDialog(txnRefNo)
                }
            }
        }
    }
    
    /**
     * 显示确认对话框，询问用户支付是否成功
     */
    private fun showConfirmationDialog(txnRefNo: String?) {
        AlertDialog.Builder(this)
            .setTitle("确认支付状态")
            .setMessage("我们无法确认您的支付状态，您的支付是否成功？\n\n交易参考号: $txnRefNo")
            .setPositiveButton("支付成功") { _, _ ->
                showPaymentResult(true, "用户确认支付成功")
            }
            .setNegativeButton("支付失败") { _, _ ->
                showPaymentResult(false, "用户确认支付失败")
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 显示支付结果
     */
    private fun showPaymentResult(success: Boolean, message: String) {
        val title = if (success) "支付成功" else "支付失败"
        
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("确定") { _, _ ->
                // 返回结果到主活动
                val intent = Intent()
                intent.putExtra("success", success)
                intent.putExtra("message", message)
                intent.putExtra("txnRefNo", txnRefNo)
                setResult(if (success) Activity.RESULT_OK else Activity.RESULT_CANCELED, intent)
                finish()
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 显示错误对话框
     */
    private fun showError(title: String, message: String) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("确定", null)
            .show()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 关闭线程池
        executor.shutdown()
    }
} 