package com.example.paymentapp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.TimeUnit
import javax.net.ssl.HttpsURLConnection

/**
 * Easypay API支付活动
 * 
 * 该活动负责：
 * 1. 接收支付参数
 * 2. 准备API支付请求
 * 3. 发送API请求到Easypay服务器
 * 4. 处理API响应并返回结果
 */
class EasypaisaApiActivity : AppCompatActivity() {
    
    private val TAG = "EasypaisaApiActivity"
    
    // Easypay Open API相关配置
    private val API_USERNAME = "pg-systems" // 由Easypay提供
    private val API_PASSWORD = "your_encrypted_password" // 由Easypay提供，已加密
    private val API_BASE_URL = "https://easypay.easypaisa.com.pk/easypay-service/PartnerBusinessService"
    
    // 支付API端点
    private val INITIATE_TRANSACTION_ENDPOINT = "$API_BASE_URL/initiateTransaction"
    private val INQUIRE_TRANSACTION_ENDPOINT = "$API_BASE_URL/inquireTransaction"
    private val INITIATE_CC_TRANSACTION_ENDPOINT = "$API_BASE_URL/initiateCCTransaction"
    
    // UI元素
    private lateinit var etMobileNumber: EditText
    private lateinit var btnPay: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var tvStatus: TextView
    
    // 支付参数
    private var amount: String = ""
    private var description: String = ""
    private var email: String = ""
    private var txnRefNo: String = ""
    private var storeId: String = "43" // 从Easypay获取的商店ID
    private var accountNum: String = "your_account_number" // 从Easypay获取的账户号
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_easypaisa_api)
        
        Log.d(TAG, "EasypaisaApiActivity已创建")
        
        // 初始化视图
        etMobileNumber = findViewById(R.id.etMobileNumber)
        btnPay = findViewById(R.id.btnPay)
        progressBar = findViewById(R.id.progressBar)
        tvStatus = findViewById(R.id.tvStatus)
        
        // 获取支付参数
        intent.extras?.let { bundle ->
            amount = bundle.getString("amount", "")
            description = bundle.getString("description", "")
            email = bundle.getString("email", "<EMAIL>")
        }
        
        // 生成交易参考号
        txnRefNo = "EP" + System.currentTimeMillis()
        
        // 设置支付按钮点击事件
        btnPay.setOnClickListener {
            if (validateInputs()) {
                initiatePayment()
            }
        }
    }
    
    /**
     * 验证输入
     */
    private fun validateInputs(): Boolean {
        val mobileNumber = etMobileNumber.text.toString().trim()
        
        if (mobileNumber.isEmpty()) {
            etMobileNumber.error = "请输入手机号码"
            return false
        }
        
        if (!mobileNumber.matches(Regex("^03[0-9]{9}$"))) {
            etMobileNumber.error = "请输入有效的巴基斯坦手机号码（格式：03XXXXXXXXX）"
            return false
        }
        
        return true
    }
    
    /**
     * 发起支付
     */
    private fun initiatePayment() {
        val mobileNumber = etMobileNumber.text.toString().trim()
        
        // 显示进度条
        progressBar.visibility = View.VISIBLE
        btnPay.isEnabled = false
        tvStatus.text = "正在处理支付请求..."
        
        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 发送API请求
                val response = initiateTransactionRequest(mobileNumber, email)
                
                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    handleInitiateTransactionResponse(response)
                }
            } catch (e: Exception) {
                Log.e(TAG, "支付请求失败", e)
                
                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    progressBar.visibility = View.GONE
                    btnPay.isEnabled = true
                    tvStatus.text = "支付请求失败: ${e.message}"
                    
                    // 显示错误对话框
                    showErrorDialog("支付失败", "发送支付请求时出错: ${e.message}")
                    
                    // 返回失败结果
                    val resultIntent = Intent()
                    resultIntent.putExtra("txnRefNo", txnRefNo)
                    resultIntent.putExtra("message", "支付请求失败: ${e.message}")
                    setResult(Activity.RESULT_CANCELED, resultIntent)
                }
            }
        }
    }
    
    /**
     * 发送初始化交易请求
     * 使用Easypay Open API的initiateTransaction接口
     */
    private suspend fun initiateTransactionRequest(mobileNumber: String, email: String): String {
        Log.d(TAG, "发送初始化交易请求")
        
        // 构建SOAP请求体
        val soapBody = """
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" 
                xmlns:dto="http://dto.transaction.partner.pg.systems.com/" 
                xmlns:dto1="http://dto.common.pg.systems.com/">
                <soapenv:Header/>
                <soapenv:Body>
                    <dto:initiateTransactionRequestType>
                        <dto1:username>${API_USERNAME}</dto1:username>
                        <dto1:password>${API_PASSWORD}</dto1:password>
                        <channel>Internet</channel>
                        <orderId>${txnRefNo}</orderId>
                        <storeId>${storeId}</storeId>
                        <transactionAmount>${amount}.00</transactionAmount>
                        <transactionType>MA</transactionType>
                        <msisdn>${mobileNumber}</msisdn>
                        <mobileAccountNo>${mobileNumber}</mobileAccountNo>
                        <emailAddress>${email}</emailAddress>
                    </dto:initiateTransactionRequestType>
                </soapenv:Body>
            </soapenv:Envelope>
        """.trimIndent()
        
        // 发送SOAP请求
        return sendSoapRequest(INITIATE_TRANSACTION_ENDPOINT, soapBody)
    }
    
    /**
     * 发送查询交易请求
     * 使用Easypay Open API的inquireTransaction接口
     */
    private suspend fun inquireTransactionRequest(orderId: String): String {
        Log.d(TAG, "发送查询交易请求 - 订单ID: $orderId")
        
        // 构建SOAP请求体
        val soapBody = """
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" 
                xmlns:dto="http://dto.transaction.partner.pg.systems.com/" 
                xmlns:dto1="http://dto.common.pg.systems.com/">
                <soapenv:Header/>
                <soapenv:Body>
                    <dto:inquireTransactionRequestType>
                        <dto1:username>${API_USERNAME}</dto1:username>
                        <dto1:password>${API_PASSWORD}</dto1:password>
                        <orderId>${orderId}</orderId>
                        <accountNum>${accountNum}</accountNum>
                    </dto:inquireTransactionRequestType>
                </soapenv:Body>
            </soapenv:Envelope>
        """.trimIndent()
        
        // 发送SOAP请求
        return sendSoapRequest(INQUIRE_TRANSACTION_ENDPOINT, soapBody)
    }
    
    /**
     * 发送初始化信用卡交易请求
     * 使用Easypay Open API的initiateCCTransaction接口
     */
    private suspend fun initiateCCTransactionRequest(
        mobileNumber: String, 
        email: String,
        cardType: String,
        pan: String,
        expiryMonth: String,
        expiryYear: String,
        cvv2: String
    ): String {
        Log.d(TAG, "发送初始化信用卡交易请求")
        
        // 构建SOAP请求体
        val soapBody = """
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" 
                xmlns:dto="http://dto.transaction.partner.pg.systems.com/" 
                xmlns:dto1="http://dto.common.pg.systems.com/">
                <soapenv:Header/>
                <soapenv:Body>
                    <dto:initiateCCTransactionRequestType>
                        <dto1:username>${API_USERNAME}</dto1:username>
                        <dto1:password>${API_PASSWORD}</dto1:password>
                        <orderId>${txnRefNo}</orderId>
                        <storeId>${storeId}</storeId>
                        <transactionAmount>${amount}.00</transactionAmount>
                        <transactionType>CC</transactionType>
                        <msisdn>${mobileNumber}</msisdn>
                        <emailAddress>${email}</emailAddress>
                        <cardType>${cardType}</cardType>
                        <pan>${pan}</pan>
                        <expiryYear>${expiryYear}</expiryYear>
                        <expiryMonth>${expiryMonth}</expiryMonth>
                        <cvv2>${cvv2}</cvv2>
                    </dto:initiateCCTransactionRequestType>
                </soapenv:Body>
            </soapenv:Envelope>
        """.trimIndent()
        
        // 发送SOAP请求
        return sendSoapRequest(INITIATE_CC_TRANSACTION_ENDPOINT, soapBody)
    }
    
    /**
     * 发送SOAP请求
     */
    private suspend fun sendSoapRequest(endpoint: String, soapBody: String): String {
        // 详细记录请求信息
        Log.d(TAG, "开始发送SOAP请求到端点: $endpoint")
        Log.d(TAG, "SOAP请求体:\n$soapBody")
        
        // 创建HTTP连接
        val url = URL(endpoint)
        val connection = url.openConnection() as HttpsURLConnection
        connection.requestMethod = "POST"
        connection.setRequestProperty("Content-Type", "text/xml;charset=UTF-8")
        connection.setRequestProperty("SOAPAction", "")
        connection.doOutput = true
        connection.connectTimeout = TimeUnit.SECONDS.toMillis(30).toInt()
        connection.readTimeout = TimeUnit.SECONDS.toMillis(30).toInt()
        
        try {
            // 记录请求头
            val requestHeaders = connection.requestProperties
            Log.d(TAG, "请求头:")
            requestHeaders.forEach { (key, value) ->
                Log.d(TAG, "  $key: $value")
            }
            
            // 发送请求数据
            val outputWriter = OutputStreamWriter(connection.outputStream)
            outputWriter.write(soapBody)
            outputWriter.flush()
            
            // 读取响应
            val responseCode = connection.responseCode
            Log.d(TAG, "响应码: $responseCode")
            
            // 记录响应头
            val responseHeaders = connection.headerFields
            Log.d(TAG, "响应头:")
            responseHeaders.forEach { (key, value) ->
                Log.d(TAG, "  $key: $value")
            }
            
            val inputStream = if (responseCode >= 400) {
                Log.e(TAG, "HTTP错误: $responseCode ${connection.responseMessage}")
                connection.errorStream
            } else {
                connection.inputStream
            }
            
            val reader = BufferedReader(InputStreamReader(inputStream))
            val response = StringBuilder()
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                response.append(line).append("\n")
            }
            
            val responseStr = response.toString()
            
            // 由于响应可能很长，分块输出
            val maxLogSize = 1000
            Log.d(TAG, "SOAP响应 (长度: ${responseStr.length}):")
            for (i in 0..responseStr.length / maxLogSize) {
                val start = i * maxLogSize
                val end = (i + 1) * maxLogSize
                val chunk = responseStr.substring(start, end.coerceAtMost(responseStr.length))
                Log.d(TAG, "响应块 $i: $chunk")
            }
            
            return responseStr
        } catch (e: Exception) {
            Log.e(TAG, "SOAP请求失败", e)
            e.printStackTrace()
            throw e
        } finally {
            connection.disconnect()
            Log.d(TAG, "SOAP请求连接已关闭")
        }
    }
    
    /**
     * 处理初始化交易响应
     */
    private fun handleInitiateTransactionResponse(response: String) {
        progressBar.visibility = View.GONE
        btnPay.isEnabled = true
        
        try {
            // 解析SOAP响应
            val responseCode = extractValueFromSoapResponse(response, "responseCode")
            val orderId = extractValueFromSoapResponse(response, "orderId")
            val paymentToken = extractValueFromSoapResponse(response, "paymentToken")
            val transactionId = extractValueFromSoapResponse(response, "transactionId")
            
            if (responseCode == "0000") {
                // 交易已初始化成功
                tvStatus.text = "交易已初始化，正在查询交易状态..."
                
                // 如果是MA（移动账户）交易，可能需要查询交易状态
                if (transactionId != null && transactionId.isNotEmpty()) {
                    // MA交易返回了交易ID，可以直接处理
                    processSuccessfulTransaction(orderId ?: "", transactionId)
                } else if (paymentToken != null && paymentToken.isNotEmpty()) {
                    // OTC交易返回了支付令牌，显示给用户
                    showPaymentTokenDialog(paymentToken, orderId ?: "")
                } else {
                    // 启动轮询查询交易状态
                    pollTransactionStatus(orderId ?: "")
                }
            } else {
                // 初始化交易失败
                val errorDesc = PaymentUtils.Easypaisa.getErrorDescription(responseCode)
                tvStatus.text = "交易初始化失败: $errorDesc"
                
                // 显示失败对话框
                showErrorDialog("交易失败", "错误码: $responseCode\n描述: $errorDesc")
                
                // 返回失败结果
                val resultIntent = Intent()
                resultIntent.putExtra("txnRefNo", txnRefNo)
                resultIntent.putExtra("message", "交易失败: $errorDesc")
                setResult(Activity.RESULT_CANCELED, resultIntent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析交易响应时出错", e)
            tvStatus.text = "解析响应时出错: ${e.message}"
            
            // 显示错误对话框
            showErrorDialog("处理错误", "解析交易响应时出错: ${e.message}")
            
            // 返回失败结果
            val resultIntent = Intent()
            resultIntent.putExtra("txnRefNo", txnRefNo)
            resultIntent.putExtra("message", "解析响应时出错: ${e.message}")
            setResult(Activity.RESULT_CANCELED, resultIntent)
        }
    }
    
    /**
     * 从SOAP响应中提取值
     */
    private fun extractValueFromSoapResponse(soapResponse: String, tagName: String): String? {
        Log.d(TAG, "尝试从SOAP响应中提取: $tagName")
        
        val startTag = "<ns2:$tagName>"
        val endTag = "</ns2:$tagName>"
        val alternativeStartTag = "<$tagName>"
        val alternativeEndTag = "</$tagName>"
        
        return try {
            if (soapResponse.contains(startTag) && soapResponse.contains(endTag)) {
                val startIndex = soapResponse.indexOf(startTag) + startTag.length
                val endIndex = soapResponse.indexOf(endTag)
                val value = soapResponse.substring(startIndex, endIndex)
                Log.d(TAG, "使用标准标签提取到 $tagName: $value")
                value
            } else if (soapResponse.contains(alternativeStartTag) && soapResponse.contains(alternativeEndTag)) {
                val startIndex = soapResponse.indexOf(alternativeStartTag) + alternativeStartTag.length
                val endIndex = soapResponse.indexOf(alternativeEndTag)
                val value = soapResponse.substring(startIndex, endIndex)
                Log.d(TAG, "使用替代标签提取到 $tagName: $value")
                value
            } else {
                // 输出标签模式以进行调试
                Log.w(TAG, "未找到标签 $tagName，标准标签存在: ${soapResponse.contains(startTag)}，结束标签存在: ${soapResponse.contains(endTag)}")
                Log.w(TAG, "替代标签存在: ${soapResponse.contains(alternativeStartTag)}，替代结束标签存在: ${soapResponse.contains(alternativeEndTag)}")
                
                // 尝试找出可能的标签
                val regex = "<[^>]*$tagName[^>]*>".toRegex()
                val matches = regex.findAll(soapResponse)
                if (matches.count() > 0) {
                    Log.d(TAG, "可能的标签匹配:")
                    matches.forEach { 
                        Log.d(TAG, "  可能的标签: ${it.value}")
                    }
                }
                
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取SOAP响应值时出错: $tagName", e)
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 轮询交易状态
     */
    private fun pollTransactionStatus(orderId: String) {
        tvStatus.text = "正在查询交易状态..."
        
        // 使用协程在后台线程执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                var attempts = 0
                var transactionStatus: String? = null
                
                // 最多尝试10次，每次间隔3秒
                while (attempts < 10 && (transactionStatus == null || transactionStatus != "PAID")) {
                    attempts++
                    
                    // 查询交易状态
                    val response = inquireTransactionRequest(orderId)
                    
                    // 解析响应
                    val responseCode = extractValueFromSoapResponse(response, "responseCode")
                    transactionStatus = extractValueFromSoapResponse(response, "transactionStatus")
                    val transactionId = extractValueFromSoapResponse(response, "transactionId")
                    
                    // 在主线程更新UI
                    withContext(Dispatchers.Main) {
                        tvStatus.text = "查询次数: $attempts, 状态: $transactionStatus"
                    }
                    
                    if (responseCode == "0000" && transactionStatus == "PAID") {
                        // 交易已支付
                        withContext(Dispatchers.Main) {
                            processSuccessfulTransaction(orderId, transactionId ?: "")
                        }
                        break
                    } else if (responseCode != "0000") {
                        // 查询出错
                        val errorDesc = PaymentUtils.Easypaisa.getErrorDescription(responseCode)
                        withContext(Dispatchers.Main) {
                            tvStatus.text = "查询交易状态失败: $errorDesc"
                            showErrorDialog("查询失败", "错误码: $responseCode\n描述: $errorDesc")
                            
                            // 返回失败结果
                            val resultIntent = Intent()
                            resultIntent.putExtra("txnRefNo", txnRefNo)
                            resultIntent.putExtra("message", "查询交易状态失败: $errorDesc")
                            setResult(Activity.RESULT_CANCELED, resultIntent)
                        }
                        break
                    }
                    
                    // 等待3秒后再次查询
                    kotlinx.coroutines.delay(3000)
                }
                
                // 如果轮询结束后仍未支付
                if (transactionStatus != "PAID") {
                    withContext(Dispatchers.Main) {
                        tvStatus.text = "交易未完成，请稍后检查"
                        showErrorDialog("交易未完成", "在规定时间内未收到支付确认，请稍后检查交易状态")
                        
                        // 返回未确定结果
                        val resultIntent = Intent()
                        resultIntent.putExtra("txnRefNo", txnRefNo)
                        resultIntent.putExtra("message", "交易未在规定时间内完成")
                        setResult(Activity.RESULT_CANCELED, resultIntent)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "轮询交易状态时出错", e)
                
                // 在主线程更新UI
                withContext(Dispatchers.Main) {
                    tvStatus.text = "查询交易状态时出错: ${e.message}"
                    
                    // 显示错误对话框
                    showErrorDialog("查询错误", "查询交易状态时出错: ${e.message}")
                    
                    // 返回失败结果
                    val resultIntent = Intent()
                    resultIntent.putExtra("txnRefNo", txnRefNo)
                    resultIntent.putExtra("message", "查询交易状态时出错: ${e.message}")
                    setResult(Activity.RESULT_CANCELED, resultIntent)
                }
            }
        }
    }
    
    /**
     * 处理成功交易
     */
    private fun processSuccessfulTransaction(orderId: String, transactionId: String) {
        tvStatus.text = "支付成功！交易ID: $transactionId"
        
        // 显示成功对话框
        AlertDialog.Builder(this)
            .setTitle("支付成功")
            .setMessage("交易已成功处理。\n交易ID: $transactionId\n订单ID: $orderId")
            .setPositiveButton("确定") { _, _ ->
                // 返回成功结果
                val resultIntent = Intent()
                resultIntent.putExtra("txnRefNo", txnRefNo)
                resultIntent.putExtra("orderId", orderId)
                resultIntent.putExtra("transactionId", transactionId)
                resultIntent.putExtra("message", "支付成功")
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 显示支付令牌对话框
     */
    private fun showPaymentTokenDialog(paymentToken: String, orderId: String) {
        AlertDialog.Builder(this)
            .setTitle("支付令牌")
            .setMessage("请使用以下令牌在授权商店支付：\n\n$paymentToken\n\n令牌有效期为24小时。")
            .setPositiveButton("查询状态") { _, _ ->
                // 开始轮询查询交易状态
                pollTransactionStatus(orderId)
            }
            .setNegativeButton("取消") { _, _ ->
                // 返回未完成结果
                val resultIntent = Intent()
                resultIntent.putExtra("txnRefNo", txnRefNo)
                resultIntent.putExtra("orderId", orderId)
                resultIntent.putExtra("paymentToken", paymentToken)
                resultIntent.putExtra("message", "用户取消了OTC支付流程")
                setResult(Activity.RESULT_CANCELED, resultIntent)
                finish()
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 显示错误对话框
     */
    private fun showErrorDialog(title: String, message: String) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("确定") { _, _ ->
                // 什么都不做
            }
            .show()
    }
    
    /**
     * 显示消息提示
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    override fun onBackPressed() {
        // 用户取消支付
        Log.d(TAG, "用户取消支付")
        
        val resultIntent = Intent()
        resultIntent.putExtra("txnRefNo", txnRefNo)
        resultIntent.putExtra("message", "用户取消支付")
        setResult(Activity.RESULT_CANCELED, resultIntent)
        
        super.onBackPressed()
    }
} 