package com.example.paymentapp

import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import org.apache.commons.codec.binary.Hex
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

/**
 * 支付工具类
 * 李伟
 * 包含 JazzCash 和 Easypaisa 支付的工具方法
 */
object PaymentUtils {
    private const val TAG = "PaymentUtils"
    
    /**
     * JazzCash 支付工具
     */
    object JazzCash {
        // JazzCash 集成参数
        private const val INTEGRITY_SALT = "8zzx29y512" // 从 JazzCash 获取的测试盐值
        private const val MERCHANT_ID = "MC158532" // 从 JazzCash 获取的测试商户 ID
        private const val PASSWORD = "0b64sxf81t" // 从 JazzCash 获取的测试密码
        private const val RETURN_URL = "https://app-api.eswap.com/swap/pay/jazzcash/notify" // 你的回调 URL

        /**
         * 获取巴基斯坦当前时间
         * @return 巴基斯坦时间（格式化为 YYYYMMDDHHMMSS）
         */
        @RequiresApi(Build.VERSION_CODES.O)
        private fun getPakistanCurrentTime(): String {
            val pakistanZoneId = ZoneId.of("Asia/Karachi") // 巴基斯坦时区
            val now = LocalDateTime.now(pakistanZoneId)
            val formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
            return now.format(formatter)
        }

        /**
         * 准备支付请求数据
         * @param amount 金额（以分为单位）
         * @param billReference 账单参考号
         * @param description 交易描述
         * @param txnExpiryDateTime 交易过期时间
         * @param txnRefNo 交易参考号
         * @param txnType 交易类型 (MPAY, MWALLET, MIGS)
         * @return 格式化的请求数据字符串
         */
        @RequiresApi(Build.VERSION_CODES.O)
        fun preparePostData(
            amount: String,
            billReference: String,
            description: String,
            txnDateTime: String,
            txnExpiryDateTime: String,
            txnRefNo: String,
            txnType: String = "MPAY",

        ): String {
            Log.d(TAG, "准备JazzCash支付请求数据 - 开始")
            Log.d(TAG, "金额: $amount, 账单参考号: $billReference, 描述: $description")
            Log.d(TAG, "交易过期时间: $txnExpiryDateTime, 参考号: $txnRefNo")
            Log.d(TAG, "使用回调 URL: $RETURN_URL")
            Log.d(TAG, "交易类型: $txnType")

            // 获取巴基斯坦当前时间
            val txnDateTime = getPakistanCurrentTime()
            Log.d(TAG, "巴基斯坦当前时间: $txnDateTime")

            // 处理描述中的中文字符，使用英文描述
            val safeDescription = "Product test description"

            // 准备支付请求数据
            val requestData = mutableMapOf(
                "pp_Version" to "1.1", // API 版本
                "pp_TxnType" to txnType, // 交易类型
                "pp_Language" to "EN", // 语言设置
                "pp_MerchantID" to MERCHANT_ID, // 商户 ID
                "pp_Password" to PASSWORD, // 商户密码
                "pp_TxnRefNo" to txnRefNo, // 交易参考号，必须唯一
                "pp_Amount" to amount, // 交易金额
                "pp_TxnCurrency" to "PKR", // 交易货币，PKR 为巴基斯坦卢比
                "pp_TxnDateTime" to txnDateTime, // 交易时间（巴基斯坦时间）
                "pp_BillReference" to billReference, // 账单参考号
                "pp_Description" to safeDescription, // 交易描述，使用英文描述
                "pp_TxnExpiryDateTime" to txnExpiryDateTime, // 交易过期时间
                "pp_ReturnURL" to RETURN_URL // 支付完成后的回调 URL
            )

            // 可选的自定义字段，如果需要可以添加
            requestData["ppmpf_1"] = "1"
            requestData["ppmpf_2"] = "2"
            requestData["ppmpf_3"] = "3"
            requestData["ppmpf_4"] = "4"
            requestData["ppmpf_5"] = "5"

            // 生成安全哈希
            val secureHash = generateSecureHash(requestData)
            requestData["pp_SecureHash"] = secureHash
            Log.d(TAG, "生成的安全哈希: $secureHash")

            // 将 Map 转换为请求字符串
            val postData = requestData.entries.joinToString("&") { (key, value) -> "$key=$value" }
            Log.d(TAG, "最终的请求数据: $postData")
            Log.d(TAG, "准备JazzCash支付请求数据 - 完成")

            return postData
        }

        /**
         * 生成安全哈希
         * 
         * 安全哈希是通过对排序后的请求参数值进行 HMAC-SHA256 哈希计算得到的。
         * 这个哈希值用于验证请求的完整性和真实性。
         * 
         * @param data 要哈希的数据
         * @return 安全哈希字符串
         */
        private fun generateSecureHash(data: Map<String, String>): String {
            try {
                Log.d(TAG, "生成安全哈希 - 开始")

                // 创建一个新的Map，不包含pp_SecureHash字段
                val dataForHash = data.filter { it.key != "pp_SecureHash" }

                // 按照字母顺序排序参数
                val sortedKeys = dataForHash.keys.sorted()

                // 构建要哈希的字符串
                val hashStringBuilder = StringBuilder(INTEGRITY_SALT)
                for (key in sortedKeys) {
                    val value = dataForHash[key] ?: continue // 跳过空值
                    if (value.isNotEmpty()) {
                        hashStringBuilder.append("&").append(value)
                    }
                }

                Log.d(TAG, "要哈希的字符串: $hashStringBuilder")

                // 使用 HMAC-SHA256 进行哈希
                val hmacSha256 = Mac.getInstance("HmacSHA256")
                val secretKey = SecretKeySpec(INTEGRITY_SALT.toByteArray(StandardCharsets.UTF_8), "HmacSHA256")
                hmacSha256.init(secretKey)
                val hash = hmacSha256.doFinal(hashStringBuilder.toString().toByteArray(StandardCharsets.UTF_8))

                // 将哈希转换为十六进制字符串
                val hashHex = String(Hex.encodeHex(hash))
                Log.d(TAG, "生成的哈希: $hashHex")
                Log.d(TAG, "生成安全哈希 - 完成")

                return hashHex
            } catch (e: Exception) {
                Log.e(TAG, "生成安全哈希时出错", e)
                e.printStackTrace()
                return ""
            }
        }

        /**
         * 解析支付响应
         * 
         * @param response 支付响应字符串
         * @return 解析后的响应数据 Map
         */
        fun parsePaymentResponse(response: String?): Map<String, String> {
            val resultMap = mutableMapOf<String, String>()
            
            if (response.isNullOrEmpty()) {
                Log.e(TAG, "支付响应为空")
                return resultMap
            }
            
            Log.d(TAG, "解析JazzCash支付响应: $response")
            
            try {
                // 尝试解析为JSON格式
                if (response.trim().startsWith("{") && response.trim().endsWith("}")) {
                    try {
                        val jsonObject = org.json.JSONObject(response)
                        val keys = jsonObject.keys()
                        
                        // 处理嵌套的JSON结构
                        var hasNestedData = false
                        
                        while (keys.hasNext()) {
                            val key = keys.next()
                            
                            // 检查是否有嵌套的data字段
                            if (key == "data" && !jsonObject.isNull("data")) {
                                hasNestedData = true
                                try {
                                    val dataObj = jsonObject.getJSONObject("data")
                                    val dataKeys = dataObj.keys()
                                    
                                    while (dataKeys.hasNext()) {
                                        val dataKey = dataKeys.next()
                                        val dataValue = dataObj.optString(dataKey, "")
                                        
                                        // 将data中的字段映射到pp_前缀字段
                                        if (dataKey == "ppResponseCode") {
                                            resultMap["pp_ResponseCode"] = dataValue
                                            Log.d(TAG, "从嵌套data中提取: pp_ResponseCode = $dataValue")
                                        } else if (dataKey == "ppResponseMessage") {
                                            resultMap["pp_ResponseMessage"] = dataValue
                                            Log.d(TAG, "从嵌套data中提取: pp_ResponseMessage = $dataValue")
                                        } else if (dataKey == "requestCode") {
                                            // 如果pp_ResponseCode不存在，使用requestCode
                                            if (!resultMap.containsKey("pp_ResponseCode")) {
                                                resultMap["pp_ResponseCode"] = dataValue
                                                Log.d(TAG, "从嵌套data中提取: pp_ResponseCode(requestCode) = $dataValue")
                                            }
                                        } else if (dataKey == "requestMessage") {
                                            // 如果pp_ResponseMessage不存在，使用requestMessage
                                            if (!resultMap.containsKey("pp_ResponseMessage")) {
                                                resultMap["pp_ResponseMessage"] = dataValue
                                                Log.d(TAG, "从嵌套data中提取: pp_ResponseMessage(requestMessage) = $dataValue")
                                            }
                                        } else {
                                            // 保存其他字段
                                            resultMap[dataKey] = dataValue
                                            Log.d(TAG, "从嵌套data中提取: $dataKey = $dataValue")
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "解析嵌套data字段失败", e)
                                }
                            } else {
                                // 处理普通字段
                                val value = jsonObject.optString(key, "")
                                resultMap[key] = value
                                Log.d(TAG, "JSON响应参数: $key = $value")
                            }
                        }
                        
                        // 如果有嵌套的data字段，但没有找到响应码，尝试从顶层字段提取
                        if (hasNestedData && !resultMap.containsKey("pp_ResponseCode")) {
                            val code = jsonObject.optString("code", "")
                            if (code == "200") {
                                resultMap["pp_ResponseCode"] = "000"
                                Log.d(TAG, "从顶层code字段提取成功状态: pp_ResponseCode = 000")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "解析JSON响应失败", e)
                    }
                } else {
                    // 尝试解析为URL编码格式 (key=value&key2=value2)
                    val responseParams = response.split("&")
                    for (param in responseParams) {
                        if ("=" in param) {
                            val keyValue = param.split("=")
                            if (keyValue.size == 2) {
                                val key = keyValue[0]
                                val value = keyValue[1]
                                resultMap[key] = value
                                Log.d(TAG, "URL编码响应参数: $key = $value")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "解析支付响应时出错", e)
            }
            
            // 检查支付状态
            val responseCode = resultMap["pp_ResponseCode"]
            if (responseCode == "000") {
                Log.d(TAG, "JazzCash支付成功！交易 ID: ${resultMap["pp_TxnRefNo"]}")
            } else {
                Log.e(TAG, "JazzCash支付失败，错误码: $responseCode, 消息: ${resultMap["pp_ResponseMessage"]}")
            }
            
            return resultMap
        }
        
        /**
         * 获取错误描述
         * 
         * @param responseCode 响应码
         * @return 错误描述
         */
        fun getErrorDescription(responseCode: String?): String {
            return when (responseCode) {
                "000" -> "交易成功"
                "FE34" -> "银行卡支付参数错误，请检查支付配置"
                "110" -> "商户信息验证失败，请联系客服"
                "111" -> "商户账户状态异常，请联系客服"
                "112" -> "商户余额不足，请联系客服"
                "113" -> "商户权限不足，请联系客服"
                "114" -> "商户IP受限，请联系客服"
                "115" -> "交易类型不支持，请尝试其他支付方式"
                "116" -> "交易币种不支持，请尝试其他币种"
                "117" -> "交易金额超限，请减少金额或联系客服"
                "118" -> "交易已过期，请重新发起交易"
                "119" -> "交易已完成，请勿重复支付"
                "120" -> "手机号码格式错误，请检查后重试"
                "121" -> "身份证号码格式错误，请检查后重试"
                "122" -> "账户余额不足，请充值后重试"
                "123" -> "交易金额超出限额，请联系您的银行"
                "124" -> "银行卡信息有误，请检查后重试"
                "125" -> "银行服务暂时不可用，请稍后再试"
                "126" -> "网络连接不稳定，请检查网络后重试"
                "127" -> "交易失败，您的手机上未确认交易"
                "128" -> "交易失败，您的银行拒绝了此交易"
                "129" -> "支付渠道暂时不可用，请稍后再试"
                else -> "支付请求失败，请稍后再试 [$responseCode]"
            }
        }
    }
    
    /**
     * Easypaisa 支付工具
     */
    object Easypaisa {
        private const val TAG = "EasypaisaUtils"
        
        // Easypay 集成参数
        private const val STORE_ID = "26768" // 从 Easypay 获取的商店 ID
        private const val MERCHANT_HASH_KEY = "SK740SITKNYD4U26" // 从 Easypay 获取的加密密钥
        private const val RETURN_URL = "https://app-api.eswap.com/swap/pay/easypay/notify" // 你的回调 URL
        
        /**
         * 准备 Easypay 支付请求数据
         * 
         * @param amount 金额
         * @param description 交易描述
         * @param mobileNumber 用户手机号码
         * @param email 用户电子邮件
         * @return 支付请求数据 Map
         */
        @JvmStatic
        fun preparePaymentData(
            amount: String,
            description: String,
            mobileNumber: String,
            email: String
        ): Map<String, String> {
            Log.d(TAG, "准备Easypay支付请求数据 - 开始")
            
            // 生成唯一订单号
            val orderRefNum = "EP" + System.currentTimeMillis()
            
            // 设置交易过期时间，格式为YYYYMMDD HHMMSS
            val expiryDate = "20301231 235959" // 示例：2030年12月31日23点59分59秒
            
            // 准备支付请求数据
            val requestData = mutableMapOf(
                "amount" to amount,
                "storeId" to STORE_ID,
                "postBackURL" to formatAndValidatePostbackUrl(RETURN_URL),
                "orderRefNum" to orderRefNum,
                "expiryDate" to expiryDate,
                "autoRedirect" to "1"
            )
            
            // 添加可选参数 - 确保手机号码格式正确（巴基斯坦格式：03XXXXXXXXX）
            if (mobileNumber.isNotEmpty()) {
                // 标准化手机号码格式
                var formattedMobile = mobileNumber
                if (!formattedMobile.startsWith("03") && formattedMobile.startsWith("+92")) {
                    formattedMobile = "0" + formattedMobile.substring(3)
                } else if (!formattedMobile.startsWith("03") && formattedMobile.startsWith("92")) {
                    formattedMobile = "0" + formattedMobile.substring(2)
                }
                
                // 确保只有数字
                formattedMobile = formattedMobile.replace("[^0-9]".toRegex(), "")
                
                if (formattedMobile.length == 11 && formattedMobile.startsWith("03")) {
                    requestData["mobileNum"] = formattedMobile
                    Log.d(TAG, "已添加格式化的手机号码: $formattedMobile")
                } else {
                    Log.w(TAG, "手机号码格式不正确，跳过添加: $mobileNumber -> $formattedMobile")
                }
            }
            
            // 添加电子邮件 - 确保格式正确
            if (email.isNotEmpty() && android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                requestData["emailAddr"] = email
                Log.d(TAG, "已添加电子邮件: $email")
            } else if (email.isNotEmpty()) {
                Log.w(TAG, "电子邮件格式不正确，跳过添加: $email")
            }
            
            // 设置支付方式（OTC_PAYMENT_METHOD, MA_PAYMENT_METHOD, CC_PAYMENT_METHOD, QR_PAYMENT_METHOD）
            requestData["paymentMethod"] = "MA_PAYMENT_METHOD"
            
            // 验证必填参数
            val requiredParams = listOf("amount", "storeId", "postBackURL", "orderRefNum")
            for (param in requiredParams) {
                if (!requestData.containsKey(param) || requestData[param].isNullOrEmpty()) {
                    Log.e(TAG, "缺少必填参数: $param")
                }
            }
            
            // 特别检查storeId的格式
            val storeId = requestData["storeId"]
            if (storeId != null) {
                try {
                    val storeIdInt = storeId.toInt()
                    Log.d(TAG, "商店ID有效: $storeIdInt")
                } catch (e: NumberFormatException) {
                    Log.e(TAG, "商店ID格式不正确，应为数字: $storeId")
                }
            }
            
            // 生成安全哈希
            try {
                val merchantHashedReq = generateSecureHash(requestData)
                requestData["merchantHashedReq"] = merchantHashedReq
                Log.d(TAG, "生成的安全哈希: $merchantHashedReq")
            } catch (e: Exception) {
                Log.e(TAG, "生成安全哈希时出错", e)
            }
            
            Log.d(TAG, "Easypay支付请求数据准备完成: $requestData")
            return requestData
        }
        
        /**
         * 验证并格式化回调URL
         * 
         * @param postBackURL 原始回调URL
         * @return 格式化后的回调URL
         */
        @JvmStatic
        fun formatAndValidatePostbackUrl(postBackURL: String): String {
            Log.d(TAG, "验证并格式化回调URL: $postBackURL")
            
            try {
                // 解析URL
                val url = java.net.URL(postBackURL)
                
                // 检查协议
                val protocol = url.protocol
                if (protocol != "https") {
                    Log.e(TAG, "回调URL必须使用HTTPS协议: $postBackURL")
                    // 使用默认回调URL
                    return RETURN_URL
                }
                
                // 检查是否包含查询参数
                if (!url.query.isNullOrEmpty()) {
                    Log.e(TAG, "回调URL不应包含查询参数: $postBackURL")
                    // 移除查询参数
                    val baseUrl = postBackURL.split("?")[0]
                    Log.d(TAG, "移除查询参数后的URL: $baseUrl")
                    return baseUrl
                }
                
                // 检查URL末尾是否有斜杠
                if (postBackURL.endsWith("/")) {
                    Log.e(TAG, "回调URL末尾不应有斜杠: $postBackURL")
                    // 移除末尾斜杠
                    val trimmedUrl = postBackURL.trimEnd('/')
                    Log.d(TAG, "移除末尾斜杠后的URL: $trimmedUrl")
                    return trimmedUrl
                }
                
                // 检查端口号
                val port = url.port
                if (port != -1 && port != 443) {
                    Log.e(TAG, "回调URL应使用默认HTTPS端口(443): $postBackURL")
                    // 重建URL，使用默认端口
                    val host = url.host
                    val path = url.path
                    val newUrl = "https://$host$path"
                    Log.d(TAG, "重建后的URL(使用默认端口): $newUrl")
                    return newUrl
                }
                
                // 检查是否包含特殊字符
                val path = url.path
                if (path.contains(" ") || path.contains("#") || path.contains(";")) {
                    Log.e(TAG, "回调URL路径包含特殊字符: $path")
                    // 尝试编码路径
                    val encodedPath = java.net.URLEncoder.encode(path, "UTF-8")
                        .replace("+", "%20")
                        .replace("%2F", "/")
                    val host = url.host
                    val newUrl = "https://$host$encodedPath"
                    Log.d(TAG, "编码路径后的URL: $newUrl")
                    return newUrl
                }
                
                // URL格式正确
                return postBackURL
                
            } catch (e: Exception) {
                Log.e(TAG, "验证回调URL时出错: ${e.message}")
                // 出错时使用默认回调URL
                return RETURN_URL
            }
        }
        
        /**
         * 解析 Easypay 支付响应
         * 
         * @param response 支付响应字符串
         * @return 解析后的响应数据 Map
         */
        @JvmStatic
        fun parsePaymentResponse(response: String?): Map<String, String> {
            val resultMap = mutableMapOf<String, String>()
            
            if (response.isNullOrEmpty()) {
                Log.e(TAG, "Easypay支付响应为空")
                return resultMap
            }
            
            Log.d(TAG, "解析Easypay支付响应: $response")
            
            try {
                // 解析URL参数
                val params = response.split("&")
                for (param in params) {
                    val keyValue = param.split("=")
                    if (keyValue.size == 2) {
                        val key = keyValue[0]
                        val value = keyValue[1]
                        resultMap[key] = value
                        Log.d(TAG, "响应参数: $key = $value")
                    }
                }
                
                // 检查支付状态
                val status = resultMap["status"]
                if (status == "Success") {
                    Log.d(TAG, "Easypay支付成功！订单参考号: ${resultMap["orderRefNumber"]}")
                } else {
                    Log.e(TAG, "Easypay支付失败，描述: ${resultMap["desc"]}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "解析Easypay响应时出错", e)
            }
            
            return resultMap
        }
        
        /**
         * 生成 Easypay 安全哈希
         * 
         * 根据Easypay文档中的加密算法实现
         * 
         * @param data 要哈希的数据
         * @return 安全哈希字符串
         */
        private fun generateSecureHash(data: Map<String, String>): String {
            try {
                Log.d(TAG, "生成Easypay安全哈希 - 开始")
                
                // Easypay要求按照特定顺序添加字段
                val fieldOrder = listOf(
                    "amount", "bankIdentifier", "billerAccount", "billRef", "customerEmail",
                    "customerMobile", "customerName", "emailAddr", "expiryDate", "mobileNum",
                    "orderRefNum", "paymentMethod", "postBackURL", "storeId", "tokenExpiry",
                    "paymentToken", "merchantHashedReq"
                )
                
                // 创建待加密的字符串 - 遵循Easypay指定的格式
                val valueBuilder = StringBuilder()
                
                // 首先添加按Easypay规定顺序的已存在字段
                for (key in fieldOrder) {
                    if (data.containsKey(key) && key != "merchantHashedReq") {
                        val value = data[key] ?: continue
                        if (valueBuilder.isNotEmpty()) {
                            valueBuilder.append("&")
                        }
                        valueBuilder.append("$key=$value")
                    }
                }
                
                // 然后添加其他未在指定顺序中的字段（按字母顺序）
                val remainingFields = data.keys.filter { 
                    it != "merchantHashedReq" && !fieldOrder.contains(it) 
                }.sorted()
                
                for (key in remainingFields) {
                    val value = data[key] ?: continue
                    if (valueBuilder.isNotEmpty()) {
                        valueBuilder.append("&")
                    }
                    valueBuilder.append("$key=$value")
                }
                
                val valueString = valueBuilder.toString()
                Log.d(TAG, "待加密字符串: $valueString")
                
                // 使用Easypay指定的加密算法
                val cipher = javax.crypto.Cipher.getInstance("AES/ECB/PKCS5Padding")
                val secretKey = javax.crypto.spec.SecretKeySpec(MERCHANT_HASH_KEY.toByteArray(java.nio.charset.StandardCharsets.UTF_8), "AES")
                cipher.init(javax.crypto.Cipher.ENCRYPT_MODE, secretKey)
                val encryptedBytes = cipher.doFinal(valueString.toByteArray(java.nio.charset.StandardCharsets.UTF_8))
                
                // 将加密结果转换为Base64编码的字符串
                val encryptedBase64 = android.util.Base64.encodeToString(encryptedBytes, android.util.Base64.NO_WRAP)
                Log.d(TAG, "生成的Easypay哈希: $encryptedBase64")
                
                return encryptedBase64
            } catch (e: Exception) {
                Log.e(TAG, "生成Easypay安全哈希时出错", e)
                e.printStackTrace()
                return ""
            }
        }
        
        /**
         * 获取错误描述
         * 
         * @param responseCode 响应码
         * @return 错误描述
         */
        @JvmStatic
        fun getErrorDescription(responseCode: String?): String {
            return when (responseCode) {
                "0000" -> "交易成功"
                "0001" -> "系统错误"
                "0002" -> "必填字段缺失"
                "0003" -> "无效的订单ID"
                "0004" -> "商户账户不存在"
                "0005" -> "商户账户未激活"
                "0006" -> "商店不存在"
                "0007" -> "商店未激活"
                "0008" -> "参数认证失败"
                "0009" -> "无效的商店ID"
                "0010" -> "无效的支付方式"
                "0011" -> "回调URL格式错误"
                "0012" -> "交易金额超出限制"
                "0013" -> "无效的手机号码格式"
                "0014" -> "无效的邮箱格式"
                "0015" -> "服务器暂时不可用"
                "Parameter Authentication failed" -> "参数认证失败，请检查storeId和其他必填参数"
                else -> "未知错误 [$responseCode]"
            }
        }
        
        /**
         * 处理AuthError错误页面
         * 
         * @param errorMessage 错误消息
         * @return 结构化错误信息
         */
        @JvmStatic
        fun handleAuthError(errorMessage: String): Map<String, String> {
            val resultMap = mutableMapOf<String, String>()
            
            Log.e(TAG, "Easypay认证错误: $errorMessage")
            
            // 设置错误状态和消息
            resultMap["status"] = "Failed"
            resultMap["desc"] = errorMessage
            
            // 检查具体错误类型
            if (errorMessage.contains("Parameter Authentication failed")) {
                resultMap["errorCode"] = "0008"
                resultMap["errorDetail"] = "请检查storeId和必填参数配置是否正确"
                Log.e(TAG, "参数认证失败，商店ID可能错误: ${STORE_ID}")
            } else if (errorMessage.contains("Invalid")) {
                resultMap["errorCode"] = "0009"
                resultMap["errorDetail"] = "提供的参数值无效"
            } else if (errorMessage.contains("Invalid Request") || errorMessage.contains("exceeded the time")) {
                resultMap["errorCode"] = "0010"
                resultMap["errorDetail"] = "无效的请求或会话已过期"
                Log.e(TAG, "无效的请求或会话已过期")
            } else {
                resultMap["errorCode"] = "0001"
                resultMap["errorDetail"] = "未知的Easypay认证错误"
            }
            
            return resultMap
        }
        
        /**
         * 检测和修复可能导致"Invalid Request"错误的问题
         * 
         * @param paymentData 支付数据
         * @return 修复后的支付数据
         */
        @JvmStatic
        fun fixInvalidRequestIssues(paymentData: Map<String, String>): Map<String, String> {
            Log.d(TAG, "检测并修复可能导致Invalid Request错误的问题")
            
            val fixedData = paymentData.toMutableMap()
            
            // 1. 检查并修复回调URL格式
            val postBackURL = fixedData["postBackURL"]
            if (postBackURL != null) {
                fixedData["postBackURL"] = formatAndValidatePostbackUrl(postBackURL)
            }
            
            // 2. 检查并修复storeId格式
            val storeId = fixedData["storeId"]
            if (storeId != null) {
                try {
                    val storeIdInt = storeId.toInt()
                    Log.d(TAG, "商店ID有效: $storeIdInt")
                } catch (e: NumberFormatException) {
                    Log.e(TAG, "商店ID格式不正确，使用默认值: $STORE_ID")
                    fixedData["storeId"] = STORE_ID
                }
            }
            
            // 3. 检查并修复订单编号格式
            val orderRefNum = fixedData["orderRefNum"]
            if (orderRefNum != null && (orderRefNum.length < 10 || orderRefNum.contains(" "))) {
                Log.e(TAG, "订单编号格式不正确: $orderRefNum")
                fixedData["orderRefNum"] = "EP" + System.currentTimeMillis()
                Log.d(TAG, "已修复订单编号: ${fixedData["orderRefNum"]}")
            }
            
            // 4. 检查并修复金额格式
            val amount = fixedData["amount"]
            if (amount != null) {
                try {
                    val amountDouble = amount.toDouble()
                    if (amountDouble <= 0) {
                        Log.e(TAG, "金额必须大于0: $amount")
                        fixedData["amount"] = "100"
                        Log.d(TAG, "已修复金额: ${fixedData["amount"]}")
                    }
                } catch (e: NumberFormatException) {
                    Log.e(TAG, "金额格式不正确: $amount")
                    fixedData["amount"] = "100"
                    Log.d(TAG, "已修复金额: ${fixedData["amount"]}")
                }
            }
            
            // 5. 检查并修复过期日期格式
            val expiryDate = fixedData["expiryDate"]
            if (expiryDate != null && !expiryDate.matches(Regex("\\d{8} \\d{6}"))) {
                Log.e(TAG, "过期日期格式不正确: $expiryDate")
                fixedData["expiryDate"] = "20301231 235959"
                Log.d(TAG, "已修复过期日期: ${fixedData["expiryDate"]}")
            }
            
            // 6. 检查并修复手机号码格式
            val mobileNum = fixedData["mobileNum"]
            if (mobileNum != null && (!mobileNum.startsWith("03") || mobileNum.length != 11)) {
                Log.e(TAG, "手机号码格式不正确: $mobileNum")
                fixedData["mobileNum"] = "03123456789"
                Log.d(TAG, "已修复手机号码: ${fixedData["mobileNum"]}")
            }
            
            // 7. 检查并修复电子邮件格式
            val emailAddr = fixedData["emailAddr"]
            if (emailAddr != null && !android.util.Patterns.EMAIL_ADDRESS.matcher(emailAddr).matches()) {
                Log.e(TAG, "电子邮件格式不正确: $emailAddr")
                fixedData["emailAddr"] = "<EMAIL>"
                Log.d(TAG, "已修复电子邮件: ${fixedData["emailAddr"]}")
            }
            
            // 8. 检查并修复支付方式
            val paymentMethod = fixedData["paymentMethod"]
            if (paymentMethod != null && paymentMethod != "MA_PAYMENT_METHOD" && 
                paymentMethod != "OTC_PAYMENT_METHOD" && paymentMethod != "CC_PAYMENT_METHOD" && 
                paymentMethod != "QR_PAYMENT_METHOD") {
                Log.e(TAG, "支付方式不正确: $paymentMethod")
                fixedData["paymentMethod"] = "MA_PAYMENT_METHOD"
                Log.d(TAG, "已修复支付方式: ${fixedData["paymentMethod"]}")
            }
            
            // 9. 检查并修复autoRedirect参数
            val autoRedirect = fixedData["autoRedirect"]
            if (autoRedirect != null && autoRedirect != "1" && autoRedirect != "0") {
                Log.e(TAG, "autoRedirect参数不正确: $autoRedirect")
                fixedData["autoRedirect"] = "1"
                Log.d(TAG, "已修复autoRedirect参数: ${fixedData["autoRedirect"]}")
            }
            
            // 10. 移除任何空值参数
            val keysToRemove = mutableListOf<String>()
            for ((key, value) in fixedData) {
                if (value.isEmpty()) {
                    Log.e(TAG, "参数值为空: $key")
                    keysToRemove.add(key)
                }
            }
            for (key in keysToRemove) {
                fixedData.remove(key)
                Log.d(TAG, "已移除空值参数: $key")
            }
            
            // 重新生成安全哈希
            try {
                val merchantHashedReq = generateSecureHash(fixedData)
                fixedData["merchantHashedReq"] = merchantHashedReq
                Log.d(TAG, "重新生成的安全哈希: $merchantHashedReq")
            } catch (e: Exception) {
                Log.e(TAG, "重新生成安全哈希时出错", e)
            }
            
            Log.d(TAG, "修复后的支付数据: $fixedData")
            return fixedData
        }
    }
} 