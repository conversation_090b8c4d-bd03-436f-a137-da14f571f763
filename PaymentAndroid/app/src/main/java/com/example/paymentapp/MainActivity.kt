package com.example.paymentapp

import android.app.AlertDialog
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.Spinner
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.Random

/**
 * 主活动
 * 
 * 该活动负责：
 * 1. 选择支付渠道（JazzCash或Easypaisa）
 * 2. 收集支付金额和描述
 * 3. 选择支付方式（页面重定向或API）
 * 4. 启动相应的支付活动
 * 5. 处理支付结果
 */
class MainActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "MainActivity"
        private const val JAZZCASH_PAYMENT_REQUEST_CODE = 1001
        private const val JAZZCASH_API_PAYMENT_REQUEST_CODE = 1002
        private const val EASYPAISA_PAYMENT_REQUEST_CODE = 1003
        private const val EASYPAISA_API_PAYMENT_REQUEST_CODE = 1004
    }

    private lateinit var etAmount: EditText
    private lateinit var etDescription: EditText
    private lateinit var btnPay: Button
    private lateinit var rgPaymentMethod: RadioGroup
    private lateinit var rbRedirect: RadioButton
    private lateinit var rbApi: RadioButton
    private lateinit var spPaymentChannel: Spinner
    private lateinit var rgTxnType: RadioGroup
    private lateinit var rbMPAY: RadioButton
    private lateinit var rbMWALLET: RadioButton
    private lateinit var rbMIGS: RadioButton

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        Log.d(TAG, "MainActivity 已创建")
        
        // 初始化视图
        etAmount = findViewById(R.id.etAmount)
        etDescription = findViewById(R.id.etDescription)
        btnPay = findViewById(R.id.btnPay)
        rgPaymentMethod = findViewById(R.id.rgPaymentMethod)
        rbRedirect = findViewById(R.id.rbRedirect)
        rbApi = findViewById(R.id.rbApi)
        spPaymentChannel = findViewById(R.id.spPaymentChannel)
        
        // 初始化交易类型选择
        rgTxnType = findViewById(R.id.rgTxnType)
        rbMPAY = findViewById(R.id.rbMPAY)
        rbMWALLET = findViewById(R.id.rbMWALLET)
        rbMIGS = findViewById(R.id.rbMIGS)
        
        // 默认选择 MPAY
        rbMPAY.isChecked = true
        
        // 设置支付渠道下拉菜单
        val paymentChannels = arrayOf("JazzCash", "Easypaisa")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, paymentChannels)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spPaymentChannel.adapter = adapter
        
        // 设置支付按钮点击事件
        btnPay.setOnClickListener {
            if (validateInputs()) {
                val selectedChannel = spPaymentChannel.selectedItem.toString()
                
                when (rgPaymentMethod.checkedRadioButtonId) {
                    R.id.rbRedirect -> {
                        when (selectedChannel) {
                            "JazzCash" -> initiateJazzCashRedirectPayment()
                            "Easypaisa" -> initiateEasypaisaRedirectPayment()
                        }
                    }
                    R.id.rbApi -> {
                        when (selectedChannel) {
                            "JazzCash" -> initiateJazzCashApiPayment()
                            "Easypaisa" -> initiateEasypaisaApiPayment()
                        }
                    }
                    else -> {
                        showMessage("请选择支付方式")
                    }
                }
            }
        }
    }

    /**
     * 验证输入
     */
    private fun validateInputs(): Boolean {
        val amount = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        
        if (TextUtils.isEmpty(amount)) {
            etAmount.error = "请输入金额"
            return false
        }
        
        try {
            val amountValue = amount.toDouble()
            if (amountValue <= 0) {
                etAmount.error = "金额必须大于0"
                return false
            }
        } catch (e: NumberFormatException) {
            etAmount.error = "请输入有效金额"
            return false
        }
        
        if (TextUtils.isEmpty(description)) {
            etDescription.error = "请输入描述"
            return false
        }
        
        return true
    }
    
    /**
     * 启动JazzCash页面重定向支付
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun initiateJazzCashRedirectPayment() {
        val amount = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        
        // 显示进度对话框
        val progressDialog = AlertDialog.Builder(this)
            .setTitle("准备支付")
            .setMessage("正在准备支付请求...")
            .setCancelable(false)
            .create()
        progressDialog.show()
        
        // 生成交易参考号（格式：JC + 时间戳 + 4位随机数）
        val txnRefNo = "JC" + System.currentTimeMillis() + String.format("%04d", Random().nextInt(10000))
        Log.d(TAG, "生成的JazzCash交易参考号: $txnRefNo")
        
        // 生成交易时间（格式：yyyyMMddHHmmss）
        val dateFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
        val txnDateTime = dateFormat.format(Date())
        Log.d(TAG, "生成的交易时间: $txnDateTime")
        
        // 生成交易过期时间（当前时间 + 1小时）
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.HOUR, 1)
        val txnExpiryDateTime = dateFormat.format(calendar.time)
        Log.d(TAG, "生成的交易过期时间: $txnExpiryDateTime")
        
        // 将金额转换为分（乘以100）
        val amountInCents = (amount.toDouble() * 100).toInt().toString()
        Log.d(TAG, "金额（分）: $amountInCents")
        
        // 生成账单参考号
        val billReference = "billRef" + System.currentTimeMillis()
        
        // 获取选择的交易类型
        val txnType = when (rgTxnType.checkedRadioButtonId) {
            R.id.rbMPAY -> "MPAY"       // 移动支付
            R.id.rbMWALLET -> "MWALLET" // 钱包支付
            R.id.rbMIGS -> "MIGS"       // 信用卡支付
            else -> "MPAY"              // 默认为移动支付
        }
        Log.d(TAG, "选择的交易类型: $txnType")
        
        // 准备支付请求数据
        val postData = PaymentUtils.JazzCash.preparePostData(
            amountInCents,
            billReference,
            description,
            txnDateTime,
            txnExpiryDateTime,
            txnRefNo,
            txnType
        )
        
        // 关闭进度对话框
        progressDialog.dismiss()
        
        // 启动支付活动
        val intent = Intent(this, JazzCashActivity::class.java)
        intent.putExtra("postData", postData)
        intent.putExtra("txnRefNo", txnRefNo)
        intent.putExtra("amount", amount)
        intent.putExtra("description", description)
        startActivityForResult(intent, JAZZCASH_PAYMENT_REQUEST_CODE)
        
        Log.d(TAG, "启动JazzCash支付活动，交易参考号: $txnRefNo")
    }
    
    /**
     * 启动JazzCash API支付
     */
    private fun initiateJazzCashApiPayment() {
        val amount = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        
        // 启动API支付活动
        val intent = Intent(this, JazzCashApiActivity::class.java)
        intent.putExtra("amount", amount)
        intent.putExtra("description", description)
        startActivityForResult(intent, JAZZCASH_API_PAYMENT_REQUEST_CODE)
        
        Log.d(TAG, "启动JazzCash API支付活动")
    }
    
    /**
     * 启动Easypaisa页面重定向支付
     */
    private fun initiateEasypaisaRedirectPayment() {
        val amount = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        val mobileNumber = "03123456789" // 在实际应用中，应该从用户输入获取
        val email = "<EMAIL>" // 在实际应用中，应该从用户输入获取
        
        // 启动Easypaisa支付活动
        val intent = Intent(this, EasypaisaActivity::class.java)
        intent.putExtra("amount", amount)
        intent.putExtra("description", description)
        intent.putExtra("mobileNumber", mobileNumber)
        intent.putExtra("email", email)
        startActivityForResult(intent, EASYPAISA_PAYMENT_REQUEST_CODE)
        
        Log.d(TAG, "启动Easypaisa支付活动")
    }
    
    /**
     * 启动Easypaisa API支付
     */
    private fun initiateEasypaisaApiPayment() {
        val amount = etAmount.text.toString().trim()
        val description = etDescription.text.toString().trim()
        val email = "<EMAIL>" // 在实际应用中，应该从用户输入获取
        
        // 启动Easypaisa API支付活动
        val intent = Intent(this, EasypaisaApiActivity::class.java)
        intent.putExtra("amount", amount)
        intent.putExtra("description", description)
        intent.putExtra("email", email)
        startActivityForResult(intent, EASYPAISA_API_PAYMENT_REQUEST_CODE)
        
        Log.d(TAG, "启动Easypaisa API支付活动")
    }

    /**
     * 显示消息
     */
    private fun showMessage(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 处理活动结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        Log.d(TAG, "收到活动结果: requestCode=$requestCode, resultCode=$resultCode")
        
        when (requestCode) {
            JAZZCASH_PAYMENT_REQUEST_CODE -> {
                if (data != null) {
                    val success = data.getBooleanExtra("success", false)
                    val message = data.getStringExtra("message") ?: "未知错误"
                    val transactionId = data.getStringExtra("transactionId") ?: ""
                    val responseCode = data.getStringExtra("responseCode") ?: ""
                    val responseMessage = data.getStringExtra("responseMessage") ?: ""
                    Log.d(TAG, "JazzCash支付结果: success=$success, message=$message, transactionId=$transactionId, responseCode=$responseCode")
                    
                    // 显示结果对话框
                    val builder = AlertDialog.Builder(this)
                    builder.setTitle(message)
                    
                    val resultMessage = StringBuilder()
                    resultMessage.append(responseMessage)
                    resultMessage.append("\n\n交易ID: $transactionId")
                    resultMessage.append("\n响应码: $responseCode")
                    
                    builder.setMessage(resultMessage.toString())
                    builder.setPositiveButton("确定", null)
                    builder.show()
                } else {
                    showMessage("支付已取消")
                }
            }
            
            JAZZCASH_API_PAYMENT_REQUEST_CODE -> {
                if (data != null) {
                    val success = data.getBooleanExtra("success", false)
                    val message = data.getStringExtra("message") ?: "未知错误"
                    val transactionId = data.getStringExtra("transactionId") ?: ""
                    
                    Log.d(TAG, "JazzCash API支付结果: success=$success, message=$message, transactionId=$transactionId")
                    
                    // 显示结果对话框
                    AlertDialog.Builder(this)
                        .setTitle(if (success) "支付成功" else "支付失败")
                        .setMessage("$message\n\n交易ID: $transactionId")
                        .setPositiveButton("确定", null)
                        .show()
                } else {
                    showMessage("支付已取消")
                }
            }
            
            EASYPAISA_PAYMENT_REQUEST_CODE -> {
                // 处理Easypaisa支付结果
                if (data != null) {
                    val success = data.getBooleanExtra("success", false)
                    val message = data.getStringExtra("message") ?: "未知错误"
                    val transactionId = data.getStringExtra("transactionId") ?: ""
                    
                    Log.d(TAG, "Easypaisa支付结果: success=$success, message=$message, transactionId=$transactionId")
                    
                    // 显示结果对话框
                    AlertDialog.Builder(this)
                        .setTitle(if (success) "支付成功" else "支付失败")
                        .setMessage("$message\n\n交易ID: $transactionId")
                        .setPositiveButton("确定", null)
                        .show()
                } else {
                    showMessage("支付已取消")
                }
            }
            
            EASYPAISA_API_PAYMENT_REQUEST_CODE -> {
                // 处理Easypaisa API支付结果
                if (data != null) {
                    val success = data.getBooleanExtra("success", false)
                    val message = data.getStringExtra("message") ?: "未知错误"
                    val transactionId = data.getStringExtra("transactionId") ?: ""
                    
                    Log.d(TAG, "Easypaisa API支付结果: success=$success, message=$message, transactionId=$transactionId")
                    
                    // 显示结果对话框
                    AlertDialog.Builder(this)
                        .setTitle(if (success) "支付成功" else "支付失败")
                        .setMessage("$message\n\n交易ID: $transactionId")
                        .setPositiveButton("确定", null)
                        .show()
                } else {
                    showMessage("支付已取消")
                }
            }
        }
    }
} 