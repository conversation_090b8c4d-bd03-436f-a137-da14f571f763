# Easypay 支付集成

本项目实现了Easypay支付网关的两种集成方式：插件集成和Open API集成。

## 已完成的修改

1. **EasypaisaActivity.java**
   - 更新为最新的Easypay插件集成流程
   - 添加对两步验证流程的支持（包括auth_token处理）
   - 更新支付网关URL和参数

2. **PaymentUtils.kt**
   - 实现Easypay安全哈希生成算法
   - 更新支付参数格式
   - 添加错误代码处理

3. **EasypaisaApiActivity.kt**
   - 实现Easypay Open API集成
   - 支持SOAP请求发送
   - 添加交易状态轮询功能
   - 支持三种API调用：
     - initiateTransaction
     - inquireTransaction
     - initiateCCTransaction

## 使用方法

详细使用说明请参考 [README_EASYPAY.md](./README_EASYPAY.md)。

## 注意事项

在生产环境中使用前，请确保：

1. 更新为您的实际商店ID和API凭据
2. 配置正确的回调URL
3. 在EasypaisaActivity.java中将isTestMode设置为false
4. 检查并更新API端点URL 