package com.example.paymentapp

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import java.nio.charset.StandardCharsets

/**
 * JazzCash支付活动类
 * 
 * 该类负责：
 * 1. 发送支付请求到JazzCash支付网关
 * 2. 监听支付过程
 * 3. 接收支付响应并返回结果到主活动
 */
class JazzCashActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "JazzCashPayment"
        
        // JazzCash 支付网关 URL
        private const val PAYMENT_URL = "https://sandbox.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform"
        
        // JazzCash 支付完成后的回调 URL
        private const val PAYMENT_RETURN_URL = "https://app-api.eswap.com/swap/pay/jazzcash/notify"
        
        // 回调 URL 前缀，用于识别回调
        private const val CALLBACK_URL_PREFIX = "jazzcash/notify"
    }

    private lateinit var mWebView: WebView
    private lateinit var progressBar: ProgressBar
    private lateinit var errorLayout: LinearLayout
    private lateinit var errorText: TextView
    private lateinit var retryButton: TextView
    
    private var txnRefNo: String? = null
    private var postData: String = ""
    private var manuallyHandlingCallback = false
    private var loadAttempts = 0
    private val MAX_LOAD_ATTEMPTS = 3

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_jazzcash)
        
        // 初始化视图
        mWebView = findViewById(R.id.webview)
        progressBar = findViewById(R.id.progressBar)
        errorLayout = findViewById(R.id.errorLayout)
        errorText = findViewById(R.id.errorText)
        retryButton = findViewById(R.id.retryButton)
        
        // 设置重试按钮点击事件
        retryButton.setOnClickListener {
            retryLoading()
        }

        // 从Intent获取postData和txnRefNo
        postData = intent.getStringExtra("postData") ?: ""
        txnRefNo = intent.getStringExtra("txnRefNo")
        
        Log.d(TAG, "收到的交易参考号: $txnRefNo")

        // 配置WebView设置
        setupWebView()

        // 加载支付页面
        loadPaymentPage()
    }
    
    /**
     * 配置WebView设置
     */
    private fun setupWebView() {
        val webSettings = mWebView.settings
        webSettings.javaScriptEnabled = true  // 启用JavaScript
        webSettings.domStorageEnabled = true  // 启用DOM存储
        webSettings.allowFileAccess = true    // 允许文件访问
        webSettings.cacheMode = WebSettings.LOAD_NO_CACHE // 不使用缓存
        webSettings.loadsImagesAutomatically = true // 自动加载图片
        webSettings.setSupportMultipleWindows(true) // 支持多窗口
        webSettings.javaScriptCanOpenWindowsAutomatically = true // 允许JS打开窗口
        
        // 允许混合内容（HTTP在HTTPS页面中）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }
        
        // 允许通用第三方Cookie
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            android.webkit.CookieManager.getInstance().setAcceptThirdPartyCookies(mWebView, true)
        }

        // 设置WebViewClient来处理页面加载事件
        mWebView.webViewClient = object : WebViewClient() {
            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                Log.d(TAG, "页面开始加载: $url")
                progressBar.visibility = View.VISIBLE
                super.onPageStarted(view, url, favicon)
            }

            // 添加拦截请求方法
            override fun shouldInterceptRequest(view: WebView, request: WebResourceRequest): WebResourceResponse? {
                val url = request.url.toString()
                val method = request.method
                
                // 如果已经处理过支付结果，则不执行额外的拦截
                if (manuallyHandlingCallback) {
                    return null
                }
                
                // 特别关注回调URL的请求
                if (url.contains(PAYMENT_RETURN_URL) || url.contains(CALLBACK_URL_PREFIX)) {
                    Handler(Looper.getMainLooper()).post {
                        Log.d(TAG, "拦截到回调URL请求: $method $url")
                        
                        // 如果是POST请求，尝试在JavaScript中捕获表单提交
                        if (method.equals("POST", ignoreCase = true)) {
                            Log.d(TAG, "拦截到表单提交请求: $url")
                            
                            // 记录这是一个我们正在处理的回调请求
                            manuallyHandlingCallback = true
                            
                            // 注入JavaScript来捕获表单提交
                            injectFormSubmitInterceptor(view)
                        }
                    }
                }
                
                return null
            }

            override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
                val url = request.url.toString()
                
                // 检查是否是回调URL
                if (url.contains(CALLBACK_URL_PREFIX)) {
                    Log.d(TAG, "检测到回调URL: $url")
                    return false // 让WebView继续加载，我们会通过表单提交拦截器捕获数据
                }
                
                // 对于所有其他URL，让WebView继续加载
                return false
            }

            override fun onPageFinished(view: WebView, url: String) {
                Log.d(TAG, "页面加载完成: $url")
                progressBar.visibility = View.GONE
                
                // 如果已经处理过支付结果，则不执行后续操作
                if (manuallyHandlingCallback) {
                    Log.d(TAG, "已经处理过支付结果，跳过页面处理")
                    super.onPageFinished(view, url)
                    return
                }
                

                
                super.onPageFinished(view, url)
            }
            
            @RequiresApi(Build.VERSION_CODES.M)
            override fun onReceivedError(view: WebView, request: WebResourceRequest, error: WebResourceError) {
                Log.e(TAG, "WebView加载错误: ${error.description}")
                
                if (request.isForMainFrame) {
                    showError("加载支付页面时出错: ${error.description}")
                }
                
                super.onReceivedError(view, request, error)
            }
        }
        
        // 添加JavaScript接口
        mWebView.addJavascriptInterface(object {
            @JavascriptInterface
            fun processPaymentResponse(response: String) {
                Log.d(TAG, "从JS接收到支付响应: $response")
                
                // 在主线程中处理响应
                Handler(Looper.getMainLooper()).post {
                    handlePaymentResponse(response)
                }
            }
        }, "AndroidPaymentHandler")
    }
    
    /**
     * 加载支付页面
     */
    private fun loadPaymentPage() {
        try {
            Log.d(TAG, "开始加载支付页面")
            
            // 显示进度条
            progressBar.visibility = View.VISIBLE
            
            // 隐藏错误布局
            errorLayout.visibility = View.GONE
            
            // 直接使用postUrl发送POST请求
            mWebView.postUrl(PAYMENT_URL, postData.toByteArray(StandardCharsets.UTF_8))
            
            Log.d(TAG, "支付请求已发送")
            
        } catch (e: Exception) {
            Log.e(TAG, "加载支付页面时出错", e)
            showError("加载支付页面时出错: ${e.message}")
        }
    }


    /**
     * 注入表单提交拦截器
     */
    private fun injectFormSubmitInterceptor(webView: WebView) {
        val script = """
            (function() {
                console.log("注入表单提交拦截器");
                
                // 查找所有表单
                const forms = document.forms;
                console.log("页面中表单数量:", forms.length);
                
                for (let i = 0; i < forms.length; i++) {
                    const form = forms[i];
                    console.log("表单", i, "action:", form.action);
                    
                    // 收集表单数据
                    const formData = new FormData(form);
                    let formDataObj = {};
                    for (let [key, value] of formData.entries()) {
                        formDataObj[key] = value;
                    }
                    
                    console.log("表单", i, "数据:", formDataObj);
                    
                    try {
                        AndroidPaymentHandler.processPaymentResponse(JSON.stringify(formDataObj));
                    } catch(e) {
                        console.error("调用Android接口出错:", e);
                    }
                }
                
                // 查找所有输入字段
                const inputs = document.querySelectorAll('input');
                console.log("页面中输入字段数量:", inputs.length);
                
                let inputData = {};
                for (let i = 0; i < inputs.length; i++) {
                    const input = inputs[i];
                    if (input.name) {
                        inputData[input.name] = input.value;
                    }
                }
                
                console.log("所有输入字段数据:", inputData);
                
                try {
                    AndroidPaymentHandler.processPaymentResponse(JSON.stringify(inputData));
                } catch(e) {
                    console.error("调用Android接口出错:", e);
                }
                
                return JSON.stringify({
                    forms: forms.length,
                    inputs: inputs.length
                });
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(script) { result ->
            Log.d(TAG, "表单提交拦截器注入结果: $result")
        }
    }
    
    /**
     * 处理JazzCash响应
     */
    private fun handleJazzCashResponse(responseCode: String, responseMessage: String, txnRefNo: String) {
        // 获取错误描述
        val errorDescription = PaymentUtils.JazzCash.getErrorDescription(responseCode)
        
        // 创建结果Intent
        val resultIntent = Intent()
        resultIntent.putExtra("success", responseCode == "000")
        resultIntent.putExtra("message", if (responseCode == "000") "支付成功" else "$errorDescription ($responseCode)")
        resultIntent.putExtra("transactionId", txnRefNo)
        resultIntent.putExtra("responseCode", responseCode)
        resultIntent.putExtra("responseMessage", responseMessage)
        
        // 设置结果
        setResult(if (responseCode == "000") Activity.RESULT_OK else Activity.RESULT_CANCELED, resultIntent)
        
        finish()
    }
    
    /**
     * 处理支付响应
     */
    private fun handlePaymentResponse(responseParams: Map<String, String>) {
        Log.d(TAG, "处理支付响应: $responseParams")
        
        // 提取关键参数
        val responseCode = responseParams["pp_ResponseCode"]
        val responseMessage = responseParams["pp_ResponseMessage"]
        val transactionRefNo = responseParams["pp_TxnRefNo"]
        
        // 检查支付状态
        val isSuccess = responseCode == "000"
        
        // 创建结果Intent
        val resultIntent = Intent()
        resultIntent.putExtra("success", isSuccess)
        resultIntent.putExtra("message", if (isSuccess) "支付成功" else "支付失败")
        resultIntent.putExtra("transactionId", transactionRefNo)
        resultIntent.putExtra("responseCode", responseCode)
        resultIntent.putExtra("responseMessage", responseMessage)
        
        // 将所有响应参数添加到Intent
        for ((key, value) in responseParams) {
            resultIntent.putExtra(key, value)
        }
        
        // 设置结果并关闭活动
        setResult(if (isSuccess) Activity.RESULT_OK else Activity.RESULT_CANCELED, resultIntent)
        finish()
        
        // 标记已经处理了回调，避免重复处理
        manuallyHandlingCallback = true
    }
    
    /**
     * 处理支付响应 (字符串版本)
     */
    private fun handlePaymentResponse(responseString: String) {
        Log.d(TAG, "处理支付响应字符串: $responseString")
        
        try {
            // 解析响应字符串
            val responseParams = PaymentUtils.JazzCash.parsePaymentResponse(responseString)
            
            // 处理解析后的响应
            handlePaymentResponse(responseParams)
            
        } catch (e: Exception) {
            Log.e(TAG, "解析支付响应字符串时出错", e)
            showError("解析支付响应时出错: ${e.message}")
        }
    }
    
    /**
     * 重试加载支付页面
     */
    private fun retryLoading() {
        loadAttempts++
        Log.d(TAG, "重试加载支付页面，尝试次数: $loadAttempts")
        
        if (loadAttempts < MAX_LOAD_ATTEMPTS) {
            loadPaymentPage()
        } else {
            // 超过最大尝试次数，显示错误信息
            showError("已达到最大尝试次数，请返回重新发起支付")
            // 3秒后自动返回
            mWebView.postDelayed({
                setFailResult("已达到最大尝试次数，请重新发起支付")
            }, 3000)
        }
    }
    
    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        progressBar.visibility = View.GONE
        mWebView.visibility = View.GONE
        errorLayout.visibility = View.VISIBLE
        errorText.text = message
        
        // 如果达到最大尝试次数，隐藏重试按钮
        if (loadAttempts >= MAX_LOAD_ATTEMPTS) {
            retryButton.visibility = View.GONE
        } else {
            retryButton.visibility = View.VISIBLE
        }
    }
    
    /**
     * 设置失败结果并返回
     */
    private fun setFailResult(message: String) {
        val intent = Intent()
        intent.putExtra("error", message)
        intent.putExtra("txnRefNo", txnRefNo)
        setResult(Activity.RESULT_CANCELED, intent)
        finish()
    }
    
    /**
     * 当用户按下返回按钮时调用
     */
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // 如果WebView可以返回，则返回上一页
        if (mWebView.canGoBack()) {
            Log.d(TAG, "WebView返回上一页")
            mWebView.goBack()
        } else {
            // 否则，取消支付并返回主活动
            Log.d(TAG, "用户取消支付")
            val intent = Intent()
            intent.putExtra("txnRefNo", txnRefNo)
            intent.putExtra("error", "用户取消支付")
            setResult(Activity.RESULT_CANCELED, intent)
            super.onBackPressed()
        }
    }
} 