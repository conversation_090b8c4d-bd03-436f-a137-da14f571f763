package com.example.paymentapp;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.AsyncTask;
import android.util.Log;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 网络诊断辅助类
 * 
 * 用于诊断网络连接问题，验证URL可达性等
 */
public class NetworkDiagnosticHelper {
    private static final String TAG = "NetworkDiagnostic";
    
    // 创建一个单线程执行器，避免多个诊断任务同时运行
    private static final Executor executor = Executors.newSingleThreadExecutor();
    
    /**
     * 检查网络连接是否可用
     * 
     * @param context 上下文
     * @return 网络是否可用
     */
    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) 
                context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager == null) {
            Log.e(TAG, "ConnectivityManager不可用");
            return false;
        }
        
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        boolean isAvailable = activeNetworkInfo != null && activeNetworkInfo.isConnected();
        
        Log.d(TAG, "网络可用性: " + isAvailable);
        if (isAvailable) {
            Log.d(TAG, "活动网络类型: " + activeNetworkInfo.getTypeName());
            Log.d(TAG, "活动网络状态: " + activeNetworkInfo.getState().name());
        }
        
        return isAvailable;
    }
    
    /**
     * 网络诊断回调接口
     */
    public interface DiagnosticCallback {
        void onResult(boolean isReachable, int responseCode, String errorMessage);
    }
    
    /**
     * 检查URL是否可达
     * 
     * @param urlString 要检查的URL
     * @param callback 诊断结果回调
     */
    public static void checkUrlReachability(String urlString, DiagnosticCallback callback) {
        Log.d(TAG, "开始检查URL可达性: " + urlString);
        
        // 使用执行器运行在后台线程
        executor.execute(() -> {
            HttpURLConnection connection = null;
            boolean isReachable = false;
            int responseCode = -1;
            String errorMessage = null;
            
            try {
                URL url = new URL(urlString);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("HEAD");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                
                // 发送请求
                responseCode = connection.getResponseCode();
                isReachable = (responseCode == HttpURLConnection.HTTP_OK || 
                               responseCode == HttpURLConnection.HTTP_MOVED_TEMP || 
                               responseCode == HttpURLConnection.HTTP_MOVED_PERM);
                
                Log.d(TAG, "URL检查结果 - 可达: " + isReachable + ", 响应码: " + responseCode);
                
                // 记录响应头
                StringBuilder headerInfo = new StringBuilder();
                for (int i = 0; ; i++) {
                    String headerName = connection.getHeaderFieldKey(i);
                    String headerValue = connection.getHeaderField(i);
                    if (headerName == null && headerValue == null) {
                        break;
                    }
                    headerInfo.append(headerName).append(": ").append(headerValue).append("\n");
                }
                Log.d(TAG, "响应头信息:\n" + headerInfo.toString());
                
            } catch (MalformedURLException e) {
                errorMessage = "URL格式错误: " + e.getMessage();
                Log.e(TAG, errorMessage);
            } catch (IOException e) {
                errorMessage = "连接失败: " + e.getMessage();
                Log.e(TAG, errorMessage);
            } catch (Exception e) {
                errorMessage = "未知错误: " + e.getMessage();
                Log.e(TAG, errorMessage);
            } finally {
                if (connection != null) {
                    connection.disconnect();
                }
            }
            
            // 发送结果回调
            boolean finalIsReachable = isReachable;
            int finalResponseCode = responseCode;
            String finalErrorMessage = errorMessage;
            
            // 如果是在主线程上调用的，则需要在主线程上回调
            if (callback != null) {
                android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
                mainHandler.post(() -> callback.onResult(finalIsReachable, finalResponseCode, finalErrorMessage));
            }
        });
    }
    
    /**
     * 执行全面的网络诊断
     * 
     * @param context 上下文
     * @param urls 要诊断的URL列表
     * @param callback 诊断结果回调
     */
    public static void runFullDiagnostic(Context context, String[] urls, DiagnosticCallback callback) {
        // 检查网络连接
        boolean isNetworkAvailable = isNetworkAvailable(context);
        Log.d(TAG, "网络诊断 - 网络可用: " + isNetworkAvailable);
        
        if (!isNetworkAvailable) {
            if (callback != null) {
                callback.onResult(false, -1, "网络不可用");
            }
            return;
        }
        
        // 检查DNS
        executor.execute(() -> {
            try {
                // 尝试解析 Google 的 DNS
                java.net.InetAddress googleDns = java.net.InetAddress.getByName("*******");
                Log.d(TAG, "DNS检查 - Google DNS: " + googleDns.getHostAddress());
                
                // 尝试解析常用域名
                java.net.InetAddress googleHost = java.net.InetAddress.getByName("www.google.com");
                Log.d(TAG, "DNS检查 - Google: " + googleHost.getHostAddress());
            } catch (Exception e) {
                Log.e(TAG, "DNS解析失败: " + e.getMessage());
            }
            
            // 检查URLs
            for (String url : urls) {
                checkUrlReachability(url, new DiagnosticCallback() {
                    @Override
                    public void onResult(boolean isReachable, int responseCode, String errorMessage) {
                        Log.d(TAG, "URL诊断 - " + url + " - 可达: " + isReachable + 
                               ", 响应码: " + responseCode + 
                               (errorMessage != null ? ", 错误: " + errorMessage : ""));
                        
                        // 仅对最后一个URL回调
                        if (url.equals(urls[urls.length - 1]) && callback != null) {
                            callback.onResult(isReachable, responseCode, errorMessage);
                        }
                    }
                });
                
                // 简单的延迟，避免太快发送请求
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
    }
} 