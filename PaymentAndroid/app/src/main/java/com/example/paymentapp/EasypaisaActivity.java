package com.example.paymentapp;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.JsResult;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONObject;
import org.json.JSONArray;

/**
 * Easypaisa支付活动
 * 
 * 该活动负责：
 * 1. 接收支付参数
 * 2. 准备支付请求
 * 3. 加载Easypaisa支付页面
 * 4. 处理支付回调
 */
public class EasypaisaActivity extends AppCompatActivity {
    private static final String TAG = "EasypaisaActivity";
    
    // Easypay支付网关URL（测试环境）
    private static final String EASYPAY_TEST_URL = "https://easypaystg.easypaisa.com.pk/easypay/Index.jsf";
    // Easypay支付网关URL（生产环境）
    private static final String EASYPAY_PROD_URL = "https://easypay.easypaisa.com.pk/easypay/Index.jsf";
    // Easypay确认URL
    private static final String EASYPAY_CONFIRM_URL = "https://easypay.easypaisa.com.pk/easypay/Confirm.jsf";
    // 回调 URL 前缀，用于识别回调
    private static final String CALLBACK_URL_PREFIX = "https://app-api.eswap.com/swap/pay/easypay/notify";
    
    private WebView webView;
    private ProgressBar progressBar;
    private String txnRefNo;
    private String storeId = "26768"; // 从Easypay获取的商店ID
    private boolean isTestMode = true; // 是否使用测试环境
    private String authToken; // 存储认证令牌
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_easypaisa);
        
        Log.d(TAG, "EasypaisaActivity已创建");
        
        // 初始化视图
        webView = findViewById(R.id.webViewEasypaisa);
        progressBar = findViewById(R.id.progressBarEasypaisa);
        
        // 获取支付参数
        Intent intent = getIntent();
        String amount = intent.getStringExtra("amount");
        String description = intent.getStringExtra("description");
        String mobileNumber = intent.getStringExtra("mobileNumber");
        String email = intent.getStringExtra("email");
        
        Log.d(TAG, "支付参数 - 金额: " + amount + ", 描述: " + description + 
              ", 手机号: " + mobileNumber + ", 邮箱: " + email);
        
        // 生成交易参考号
        txnRefNo = "EP" + System.currentTimeMillis();
        Log.d(TAG, "生成的交易参考号: " + txnRefNo);
        
        // 先执行网络诊断
        runNetworkDiagnostics();
        
        // 配置WebView
        configureWebView();
        
        // 准备支付请求数据
        Map<String, String> paymentData = preparePaymentData(amount, description, mobileNumber, email);
        
        // 加载Easypay支付页面
        loadEasypayPaymentPage(paymentData);
    }
    
    /**
     * 执行网络诊断
     */
    private void runNetworkDiagnostics() {
        Log.d(TAG, "开始执行网络诊断...");
        
        // 检查网络可用性
        boolean networkAvailable = NetworkDiagnosticHelper.isNetworkAvailable(this);
        Log.d(TAG, "网络可用性检查: " + networkAvailable);
        
        if (!networkAvailable) {
            showToast("网络不可用，请检查网络连接");
            return;
        }
        
        // 检查关键URL可达性
        String[] urlsToCheck = {
            isTestMode ? EASYPAY_TEST_URL : EASYPAY_PROD_URL,
            isTestMode ? "https://easypaystg.easypaisa.com.pk/easypay/Confirm.jsf" : EASYPAY_CONFIRM_URL,
            CALLBACK_URL_PREFIX
        };
        
        NetworkDiagnosticHelper.runFullDiagnostic(this, urlsToCheck, new NetworkDiagnosticHelper.DiagnosticCallback() {
            @Override
            public void onResult(boolean isReachable, int responseCode, String errorMessage) {
                if (!isReachable) {
                    Log.e(TAG, "网络诊断失败: " + errorMessage);
                    showToast("无法连接到支付网关，错误: " + errorMessage);
                } else {
                    Log.d(TAG, "网络诊断成功，可以继续支付流程");
                }
            }
        });
    }
    
    /**
     * 准备支付请求数据
     */
    private Map<String, String> preparePaymentData(String amount, String description, String mobileNumber, String email) {
        Map<String, String> paymentData = new HashMap<>();
        
        // 必填参数
        paymentData.put("amount", amount);
        paymentData.put("storeId", storeId);
        paymentData.put("postBackURL", CALLBACK_URL_PREFIX);
        paymentData.put("orderRefNum", txnRefNo);
        
        // 可选参数
        // 设置交易过期时间，格式为YYYYMMDD HHMMSS
        String expiryDate = "20301231 235959"; // 示例：2030年12月31日23点59分59秒
        paymentData.put("expiryDate", expiryDate);
        
        // 是否自动重定向
        paymentData.put("autoRedirect", "1");
        
        // 如果提供了手机号码，则预填充
        if (mobileNumber != null && !mobileNumber.isEmpty()) {
            paymentData.put("mobileNum", mobileNumber);
        }
        
        // 如果提供了电子邮件，则预填充
        if (email != null && !email.isEmpty()) {
            paymentData.put("emailAddr", email);
        }
        
        // 设置支付方式（OTC_PAYMENT_METHOD, MA_PAYMENT_METHOD, CC_PAYMENT_METHOD, QR_PAYMENT_METHOD）
        paymentData.put("paymentMethod", "MA_PAYMENT_METHOD");
        
        // 生成安全哈希（如果需要）
        // String merchantHashedReq = generateSecureHash(paymentData);
        // paymentData.put("merchantHashedReq", merchantHashedReq);
        
        Log.d(TAG, "准备的支付数据: " + paymentData.toString());
        
        return paymentData;
    }
    
    /**
     * 配置WebView
     */
    private void configureWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // 开启WebView调试（仅在API 19及以上支持）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        
        // 设置WebChromeClient以显示加载进度和控制台消息
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                progressBar.setProgress(newProgress);
                if (newProgress == 100) {
                    progressBar.setVisibility(View.GONE);
                } else {
                    progressBar.setVisibility(View.VISIBLE);
                }
            }
            
            // 输出JavaScript控制台消息
            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                Log.d(TAG, "JS控制台: " + consoleMessage.message() + " -- 来自 " + 
                      consoleMessage.sourceId() + ":" + consoleMessage.lineNumber());
                return true;
            }
            
            // 处理JavaScript警告对话框
            @Override
            public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
                Log.d(TAG, "JS警告: " + message + " -- 来自 " + url);
                result.confirm();
                return true;
            }
        });
        
        // 设置WebViewClient以处理URL加载和错误
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                String url = request.getUrl().toString();
                Log.d(TAG, "加载URL: " + url);
                
                // 检查是否包含auth_token参数
                if (url.contains("auth_token=")) {
                    Log.d(TAG, "检测到认证令牌URL: " + url);
                    // 从URL中提取auth_token
                    try {
                        String query = url.substring(url.indexOf("?") + 1);
                        String[] params = query.split("&");
                        for (String param : params) {
                            String[] keyValue = param.split("=");
                            if (keyValue.length == 2 && keyValue[0].equals("auth_token")) {
                                authToken = keyValue[1];
                                Log.d(TAG, "提取的认证令牌: " + authToken);
                                
                                // 发送确认请求
                                sendConfirmationRequest(authToken);
                                return true;
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "提取认证令牌时出错", e);
                    }
                }
                
                // 检查是否是最终回调URL
                if (url.contains("status=")) {
                    Log.d(TAG, "检测到状态回调URL: " + url);
                    handlePaymentCallback(url);
                    return true;
                }
                
                // 检查是否是认证错误页面
                if (url.contains("AuthError.jsf")) {
                    Log.d(TAG, "检测到认证错误页面: " + url);
                    // 提取错误内容
                    view.evaluateJavascript(
                        "(function() { " +
                        "  var errorEl = document.getElementById('errorMessages_body'); " +
                        "  return errorEl ? errorEl.innerText : ''; " +
                        "})();",
                        html -> {
                            // 解码JS字符串
                            String errorMessage = html;
                            if (html != null && html.length() > 2) {
                                errorMessage = html.substring(1, html.length() - 1)
                                                 .replace("\\\"", "\"")
                                                 .replace("\\n", "\n")
                                                 .replace("\\r", "\r")
                                                 .replace("\\t", "\t")
                                                 .replace("\\\\", "\\");
                            }
                            
                            Log.e(TAG, "认证错误信息: " + errorMessage);
                            
                            // 处理认证错误
                            handleAuthError(errorMessage);
                        }
                    );
                    return true;
                }
                
                return false;
            }
            
            // 页面加载完成时获取HTML内容
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "页面加载完成: " + url);
                
                // 提取页面HTML内容进行调试
                view.evaluateJavascript(
                    "(function() { return document.documentElement.outerHTML; })();",
                    html -> {
                        // 解码JS字符串
                        String decodedHtml = html;
                        if (html != null && html.length() > 2) {
                            decodedHtml = html.substring(1, html.length() - 1)
                                             .replace("\\\"", "\"")
                                             .replace("\\n", "\n")
                                             .replace("\\r", "\r")
                                             .replace("\\t", "\t")
                                             .replace("\\\\", "\\");
                        }
                        
                        // 由于HTML可能很长，分块输出
                        int maxLogSize = 1000;
                        for (int i = 0; i <= decodedHtml.length() / maxLogSize; i++) {
                            int start = i * maxLogSize;
                            int end = (i + 1) * maxLogSize;
                            end = end > decodedHtml.length() ? decodedHtml.length() : end;
                            Log.d(TAG, "页面HTML [" + i + "]: " + decodedHtml.substring(start, end));
                        }
                    }
                );
            }
            
            // 处理页面加载错误
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                String url = request.getUrl().toString();
                Log.e(TAG, "页面加载错误: " + error.getErrorCode() + " " + error.getDescription() + " URL: " + url);
                
                if (request.isForMainFrame()) {
                    showToast("页面加载错误: " + error.getDescription());
                }
            }
            
            // 处理HTTP错误
            @Override
            public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
                Log.e(TAG, "HTTP错误: " + errorResponse.getStatusCode() + " URL: " + request.getUrl());
                
                if (request.isForMainFrame()) {
                    showToast("HTTP错误: " + errorResponse.getStatusCode());
                }
            }
        });
    }
    
    /**
     * 加载Easypay支付页面
     * 
     * @param paymentData 支付数据
     */
    private void loadEasypayPaymentPage(Map<String, String> paymentData) {
        Log.d(TAG, "加载Easypay支付页面");
        
        // 确定使用的支付网关URL
        String paymentGatewayUrl = isTestMode ? EASYPAY_TEST_URL : EASYPAY_PROD_URL;
        
        // 执行URL可达性检查
        NetworkDiagnosticHelper.checkUrlReachability(paymentGatewayUrl, new NetworkDiagnosticHelper.DiagnosticCallback() {
            @Override
            public void onResult(boolean isReachable, int responseCode, String errorMessage) {
                if (!isReachable) {
                    Log.e(TAG, "支付网关不可达: " + errorMessage);
                    showToast("无法连接到支付网关: " + (errorMessage != null ? errorMessage : "未知错误"));
                    return;
                }
                
                Log.d(TAG, "支付网关可达，继续加载支付页面");
                
                // 在UI线程中加载表单
                runOnUiThread(() -> {
                    // 方法1：使用POST表单提交（解决跨域问题）
                    loadPaymentPageWithForm(paymentGatewayUrl, paymentData);
                });
            }
        });
    }
    
    /**
     * 使用表单提交加载支付页面
     */
    private void loadPaymentPageWithForm(String paymentGatewayUrl, Map<String, String> paymentData) {
        Log.d(TAG, "使用表单提交加载支付页面");
        
        // 检查回调URL状态
        checkPostbackUrlStatus();
        
        // 修复可能导致Invalid Request错误的问题
        paymentData = PaymentUtils.Easypaisa.fixInvalidRequestIssues(paymentData);
        
        // 构建POST表单 - 使用直接提交方式解决认证问题
        StringBuilder formBuilder = new StringBuilder();
        formBuilder.append("<html><head>");
        formBuilder.append("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
        formBuilder.append("<style>body{font-family:Arial;text-align:center;} .loader{margin:20px auto;border:8px solid #f3f3f3;border-radius:50%;border-top:8px solid #3498db;width:60px;height:60px;animation:spin 2s linear infinite;} @keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}</style>");
        formBuilder.append("</head><body>");
        formBuilder.append("<h2>正在跳转到支付页面...</h2>");
        formBuilder.append("<div class='loader'></div>");
        formBuilder.append("<p>请稍候，您将被自动重定向到支付页面</p>");
        
        // 表单提交目标
        formBuilder.append("<form id='paymentForm' action='").append(paymentGatewayUrl).append("' method='post'>");
        
        // 添加表单字段
        for (Map.Entry<String, String> entry : paymentData.entrySet()) {
            formBuilder.append("<input type='hidden' name='")
                    .append(entry.getKey())
                    .append("' value='")
                    .append(entry.getValue())
                    .append("'>");
        }
        
        formBuilder.append("</form>");
        
        // 添加页面加载完成后的行为 - 使用延迟并提供调试信息
        formBuilder.append("<script>");
        formBuilder.append("document.addEventListener('DOMContentLoaded', function() {");
        // 日志记录表单数据，便于调试
        formBuilder.append("  if (window.console) {");
        formBuilder.append("    console.log('表单准备提交，目标URL: ").append(paymentGatewayUrl).append("');");
        formBuilder.append("    console.log('表单数据:');");
        for (Map.Entry<String, String> entry : paymentData.entrySet()) {
            formBuilder.append("    console.log('").append(entry.getKey()).append(" = ").append(entry.getValue()).append("');");
        }
        formBuilder.append("  }");
        // 使用延迟提交，确保DOM完全加载
        formBuilder.append("  setTimeout(function() {");
        formBuilder.append("    try {");
        formBuilder.append("      var form = document.getElementById('paymentForm');");
        formBuilder.append("      if (form) {");
        formBuilder.append("        if (window.console) { console.log('正在提交表单...'); }");
        formBuilder.append("        form.submit();");
        formBuilder.append("      } else {");
        formBuilder.append("        if (window.console) { console.error('找不到表单元素！'); }");
        formBuilder.append("      }");
        formBuilder.append("    } catch (e) {");
        formBuilder.append("      if (window.console) { console.error('表单提交出错: ' + e.message); }");
        formBuilder.append("    }");
        formBuilder.append("  }, 1000);");
        formBuilder.append("});");
        formBuilder.append("</script>");
        formBuilder.append("</body></html>");
        
        String formHtml = formBuilder.toString();
        
        // 输出完整的表单HTML
        Log.d(TAG, "提交的表单: " + formHtml);
        
        // 使用loadDataWithBaseURL加载表单 - 确保使用正确的baseUrl和encoding
        webView.loadDataWithBaseURL(
            paymentGatewayUrl, // 关键：使用目标URL作为baseUrl
            formHtml,
            "text/html",
            "UTF-8",
            null
        );
        
        // 3秒后检查页面内容，如果仍为空白则提供重试选项
        webView.postDelayed(() -> {
            webView.evaluateJavascript(
                "(function() { return document.body ? document.body.innerHTML.length : 0; })();",
                result -> {
                    try {
                        int contentLength = Integer.parseInt(result);
                        Log.d(TAG, "页面内容长度: " + contentLength);
                        
                        if (contentLength < 10) { // 几乎是空白页面
                            Log.e(TAG, "检测到空白页面，提供重试选项");
                            showRetryDialog();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "检查页面内容时出错", e);
                    }
                }
            );
        }, 3000);
        
        // 启动轮询检测，每2秒检查页面内容变化
        startPollingPageContent();
    }
    
    /**
     * 检查回调URL状态
     */
    private void checkPostbackUrlStatus() {
        Log.d(TAG, "检查回调URL状态");
        
        // 准备测试数据
        final String callbackUrl = CALLBACK_URL_PREFIX;
        
        // 在后台线程中执行网络请求
        new Thread(() -> {
            try {
                // 创建URL
                URL url = new URL(callbackUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                
                // 获取响应
                int responseCode = connection.getResponseCode();
                
                // 读取响应头
                Map<String, List<String>> headers = connection.getHeaderFields();
                StringBuilder headerInfo = new StringBuilder();
                for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                    String key = entry.getKey();
                    if (key != null) {
                        headerInfo.append(key).append(": ");
                        for (String value : entry.getValue()) {
                            headerInfo.append(value).append("; ");
                        }
                        headerInfo.append("\n");
                    }
                }
                
                // 读取响应内容
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line).append("\n");
                    }
                }
                
                // 输出响应信息
                final String responseMessage = response.toString();
                final String headerInfoStr = headerInfo.toString();
                
                Log.d(TAG, "回调URL状态 - 状态码: " + responseCode);
                Log.d(TAG, "回调URL响应头:\n" + headerInfoStr);
                Log.d(TAG, "回调URL响应内容:\n" + responseMessage);
                
                // 检查是否响应是JSON格式
                if (responseMessage.trim().startsWith("{") && responseMessage.trim().endsWith("}")) {
                    try {
                        JSONObject jsonResponse = new JSONObject(responseMessage);
                        int code = jsonResponse.optInt("code", 0);
                        String msg = jsonResponse.optString("msg", "");
                        
                        Log.d(TAG, "回调URL返回JSON - 代码: " + code + ", 消息: " + msg);
                        
                        // 如果有错误码，显示警告
                        if (code != 200) {
                            runOnUiThread(() -> {
                                Log.w(TAG, "回调URL返回错误码: " + code + ", 消息: " + msg);
                                showToast("警告: 回调URL返回错误: " + msg);
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "解析回调URL JSON响应时出错", e);
                    }
                }
                
                // 检查回调URL是否符合Easypaisa要求
                checkCallbackUrlFormat(callbackUrl);
                
            } catch (Exception e) {
                Log.e(TAG, "检查回调URL状态时出错", e);
            }
        }).start();
    }
    
    /**
     * 检查回调URL格式是否符合Easypaisa要求
     * 
     * @param callbackUrl 回调URL
     */
    private void checkCallbackUrlFormat(String callbackUrl) {
        Log.d(TAG, "检查回调URL格式: " + callbackUrl);
        
        try {
            // 检查URL格式
            URL url = new URL(callbackUrl);
            
            // 检查协议
            String protocol = url.getProtocol();
            if (!protocol.equals("https")) {
                Log.e(TAG, "回调URL必须使用HTTPS协议: " + callbackUrl);
                showToast("警告: 回调URL必须使用HTTPS协议");
            }
            
            // 检查是否包含查询参数
            if (url.getQuery() != null) {
                Log.e(TAG, "回调URL不应包含查询参数: " + callbackUrl);
                showToast("警告: 回调URL不应包含查询参数");
            }
            
            // 检查端口号
            int port = url.getPort();
            if (port != -1 && port != 443) {
                Log.e(TAG, "回调URL应使用默认HTTPS端口(443): " + callbackUrl);
                showToast("警告: 回调URL应使用默认HTTPS端口");
            }
            
            // 检查URL末尾是否有斜杠
            if (callbackUrl.endsWith("/")) {
                Log.e(TAG, "回调URL末尾不应有斜杠: " + callbackUrl);
                showToast("警告: 回调URL末尾不应有斜杠");
            }
            
            // 检查域名格式
            String host = url.getHost();
            if (!host.matches("^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                Log.e(TAG, "回调URL域名格式可能不正确: " + host);
                showToast("警告: 回调URL域名格式可能不正确");
            }
            
            // 检查是否包含特殊字符
            String path = url.getPath();
            if (path.contains(" ") || path.contains("?") || path.contains("#") || path.contains(";")) {
                Log.e(TAG, "回调URL路径包含特殊字符: " + path);
                showToast("警告: 回调URL路径包含特殊字符");
            }
            
            Log.d(TAG, "回调URL格式检查完成");
            
        } catch (Exception e) {
            Log.e(TAG, "检查回调URL格式时出错", e);
            showToast("警告: 回调URL格式无效");
        }
    }
    
    /**
     * 启动页面内容轮询检测
     */
    private void startPollingPageContent() {
        Log.d(TAG, "启动页面内容轮询检测");
        
        final android.os.Handler handler = new android.os.Handler();
        final Runnable pollRunnable = new Runnable() {
            private String lastUrl = "";
            private String lastContent = "";
            private int pollCount = 0;
            private static final int MAX_POLLS = 10; // 最多轮询10次（20秒）
            
            @Override
            public void run() {
                if (pollCount >= MAX_POLLS) {
                    Log.d(TAG, "轮询检测已达最大次数，停止轮询");
                    return;
                }
                
                pollCount++;
                
                // 获取当前URL
                webView.evaluateJavascript(
                    "(function() { return window.location.href; })();",
                    urlResult -> {
                        String currentUrl = urlResult;
                        if (urlResult != null && urlResult.length() > 2) {
                            currentUrl = urlResult.substring(1, urlResult.length() - 1);
                        }
                        
                        // 如果URL变化，记录下来
                        if (!currentUrl.equals(lastUrl)) {
                            Log.d(TAG, "轮询检测 - URL变化: " + lastUrl + " -> " + currentUrl);
                            lastUrl = currentUrl;
                        }
                        
                        // 获取页面内容
                        webView.evaluateJavascript(
                            "(function() { " +
                            "  var content = document.body ? document.body.innerText : ''; " +
                            "  return content.substring(0, 100); " + // 仅获取前100个字符，避免日志过大
                            "})();",
                            contentResult -> {
                                String currentContent = contentResult;
                                if (contentResult != null && contentResult.length() > 2) {
                                    currentContent = contentResult.substring(1, contentResult.length() - 1);
                                }
                                
                                // 如果内容变化，记录下来
                                if (!currentContent.equals(lastContent)) {
                                    Log.d(TAG, "轮询检测 - 内容变化: " + currentContent);
                                    lastContent = currentContent;
                                    
                                    // 检查是否包含错误信息
                                    if (currentContent.contains("Authentication failed") || 
                                        currentContent.contains("Error") || 
                                        currentContent.contains("Failed")) {
                                        Log.e(TAG, "轮询检测 - 检测到错误信息: " + currentContent);
                                        
                                        // 提取完整错误信息
                                        webView.evaluateJavascript(
                                            "(function() { " +
                                            "  var errorEl = document.getElementById('errorMessages_body'); " +
                                            "  return errorEl ? errorEl.innerText : ''; " +
                                            "})();",
                                            errorResult -> {
                                                String errorMsg = errorResult;
                                                if (errorResult != null && errorResult.length() > 2) {
                                                    errorMsg = errorResult.substring(1, errorResult.length() - 1);
                                                }
                                                
                                                if (!errorMsg.isEmpty()) {
                                                    Log.e(TAG, "轮询检测 - 提取到错误信息: " + errorMsg);
                                                    handleAuthError(errorMsg);
                                                }
                                            }
                                        );
                                        return;
                                    }
                                }
                                
                                // 继续轮询
                                handler.postDelayed(this, 2000);
                            }
                        );
                    }
                );
            }
        };
        
        // 开始轮询
        handler.postDelayed(pollRunnable, 2000);
    }
    
    /**
     * 显示重试对话框
     */
    private void showRetryDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("页面加载失败")
            .setMessage("无法加载支付页面。这可能是由于网络问题、参数配置错误或者服务器暂时不可用。")
            .setPositiveButton("重试", (dialog, which) -> {
                // 获取支付参数并重新加载
                Intent intent = getIntent();
                String amount = intent.getStringExtra("amount");
                String description = intent.getStringExtra("description");
                String mobileNumber = intent.getStringExtra("mobileNumber");
                String email = intent.getStringExtra("email");
                
                Map<String, String> paymentData = preparePaymentData(amount, description, mobileNumber, email);
                loadEasypayPaymentPage(paymentData);
            })
            .setNegativeButton("取消", (dialog, which) -> {
                // 用户取消支付
                Intent resultIntent = new Intent();
                resultIntent.putExtra("txnRefNo", txnRefNo);
                resultIntent.putExtra("message", "用户取消支付");
                setResult(Activity.RESULT_CANCELED, resultIntent);
                finish();
            })
            .setNeutralButton("诊断", (dialog, which) -> {
                // 运行网络诊断
                runNetworkDiagnostics();
                
                // 诊断HTTPS证书问题
                checkSSLCertificate();
                
                // 尝试访问认证页面
                testAuthPage();
                
                // 测试直接API调用
                testDirectApiCall();
                
                // 显示一个诊断结果正在生成的提示
                showToast("正在进行网络诊断，请查看日志输出");
            })
            .setCancelable(false)
            .show();
    }
    
    /**
     * 检查SSL证书问题
     */
    private void checkSSLCertificate() {
        Log.d(TAG, "开始检查SSL证书问题");
        
        String url = isTestMode ? EASYPAY_TEST_URL : EASYPAY_PROD_URL;
        
        new Thread(() -> {
            try {
                java.net.URL testUrl = new java.net.URL(url);
                javax.net.ssl.HttpsURLConnection connection = (javax.net.ssl.HttpsURLConnection) testUrl.openConnection();
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                
                // 启用所有主机名验证（仅用于调试）
                connection.setHostnameVerifier((hostname, session) -> true);
                
//                // 获取SSL信息
//                connection.connect();
//                javax.net.ssl.SSLSession session = connection.getSSLSocketFactory().createSocket().getSession();
//
//                Log.d(TAG, "SSL协议: " + session.getProtocol());
//                Log.d(TAG, "加密套件: " + session.getCipherSuite());
//
//                // 获取证书信息
//                java.security.cert.Certificate[] certs = connection.getServerCertificates();
//                if (certs != null && certs.length > 0) {
//                    java.security.cert.X509Certificate cert = (java.security.cert.X509Certificate) certs[0];
//                    Log.d(TAG, "证书颁发者: " + cert.getIssuerX500Principal().getName());
//                    Log.d(TAG, "证书主题: " + cert.getSubjectX500Principal().getName());
//                    Log.d(TAG, "证书有效期: " + cert.getNotBefore() + " 至 " + cert.getNotAfter());
//                }
//
//                connection.disconnect();
                
            } catch (Exception e) {
                Log.e(TAG, "检查SSL证书时出错", e);
            }
        }).start();
    }
    
    /**
     * 测试认证页面
     */
    private void testAuthPage() {
        Log.d(TAG, "测试直接访问认证页面");
        
        // 在主线程中执行WebView操作
        runOnUiThread(() -> {
            // 直接加载认证页面
            String authUrl = isTestMode ? 
                "https://easypaystg.easypaisa.com.pk/easypay/faces/pg/site/Index.jsf" : 
                "https://easypay.easypaisa.com.pk/easypay/faces/pg/site/Index.jsf";
            
            Log.d(TAG, "尝试直接访问: " + authUrl);
            webView.loadUrl(authUrl);
        });
    }
    
    /**
     * 测试直接API调用
     */
    private void testDirectApiCall() {
        Log.d(TAG, "测试直接API调用");
        
        // 在后台线程中执行
        new Thread(() -> {
            try {
                // 准备支付数据
                Intent intent = getIntent();
                String amount = intent.getStringExtra("amount");
                if (amount == null || amount.isEmpty()) {
                    amount = "100"; // 默认金额
                }
                String mobileNumber = intent.getStringExtra("mobileNumber");
                if (mobileNumber == null || mobileNumber.isEmpty()) {
                    mobileNumber = "03123456789"; // 默认手机号
                }
                String email = intent.getStringExtra("email");
                if (email == null || email.isEmpty()) {
                    email = "<EMAIL>"; // 默认邮箱
                }
                
                // 生成唯一订单号
                String orderRefNum = "EP" + System.currentTimeMillis();
                
                // 设置交易过期时间，格式为YYYYMMDD HHMMSS
                String expiryDate = "20301231 235959"; // 示例：2030年12月31日23点59分59秒
                
                // 准备请求参数
                Map<String, String> params = new HashMap<>();
                params.put("amount", amount);
                params.put("storeId", storeId);
                params.put("postBackURL", CALLBACK_URL_PREFIX);
                params.put("orderRefNum", orderRefNum);
                params.put("expiryDate", expiryDate);
                params.put("autoRedirect", "1");
                params.put("mobileNum", mobileNumber);
                params.put("emailAddr", email);
                params.put("paymentMethod", "MA_PAYMENT_METHOD");
                
                // 构建请求URL
                String apiUrl = isTestMode ? EASYPAY_TEST_URL : EASYPAY_PROD_URL;
                
                Log.d(TAG, "API请求URL: " + apiUrl);
                Log.d(TAG, "API请求参数: " + params.toString());
                
                // 创建URL连接
                URL url = new URL(apiUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                connection.setDoOutput(true);
                
                // 构建请求参数字符串
                StringBuilder postData = new StringBuilder();
                for (Map.Entry<String, String> param : params.entrySet()) {
                    if (postData.length() != 0) {
                        postData.append('&');
                    }
                    postData.append(java.net.URLEncoder.encode(param.getKey(), "UTF-8"));
                    postData.append('=');
                    postData.append(java.net.URLEncoder.encode(param.getValue(), "UTF-8"));
                }
                String postDataString = postData.toString();
                
                Log.d(TAG, "发送POST数据: " + postDataString);
                
                // 发送请求
                try (java.io.OutputStreamWriter writer = new java.io.OutputStreamWriter(connection.getOutputStream())) {
                    writer.write(postDataString);
                    writer.flush();
                }
                
                // 获取响应
                int responseCode = connection.getResponseCode();
                Log.d(TAG, "API响应码: " + responseCode);
                
                // 获取响应头
                Map<String, List<String>> headers = connection.getHeaderFields();
                StringBuilder headerInfo = new StringBuilder();
                for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                    String key = entry.getKey();
                    if (key != null) {
                        headerInfo.append(key).append(": ");
                        for (String value : entry.getValue()) {
                            headerInfo.append(value).append("; ");
                        }
                        headerInfo.append("\n");
                    }
                }
                Log.d(TAG, "API响应头:\n" + headerInfo.toString());
                
                // 读取响应内容
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line).append("\n");
                    }
                }
                
                final String responseBody = response.toString();
                Log.d(TAG, "API响应内容:\n" + responseBody);
                
                // 在UI线程中显示结果
                runOnUiThread(() -> {
                    // 显示详细结果对话框
                    new androidx.appcompat.app.AlertDialog.Builder(this)
                        .setTitle("API调用结果")
                        .setMessage("URL: " + apiUrl + "\n\n" +
                                  "状态码: " + responseCode + "\n\n" +
                                  "响应内容长度: " + responseBody.length() + " 字符")
                        .setPositiveButton("确定", null)
                        .show();
                    
                    // 如果响应是HTML，可以在WebView中显示
                    if (responseBody.trim().startsWith("<") && responseBody.contains("</html>")) {
                        webView.loadDataWithBaseURL(
                            apiUrl,
                            responseBody,
                            "text/html",
                            "UTF-8",
                            null
                        );
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "API调用出错", e);
                
                // 在UI线程中显示错误
                runOnUiThread(() -> {
                    showToast("API调用失败: " + e.getMessage());
                });
            }
        }).start();
    }
    
    /**
     * 发送确认请求
     * 
     * @param authToken 认证令牌
     */
    private void sendConfirmationRequest(String authToken) {
        Log.d(TAG, "发送确认请求，认证令牌: " + authToken);
        
        // 确定使用的确认URL
        String confirmUrl = isTestMode ? 
                "https://easypaystg.easypaisa.com.pk/easypay/Confirm.jsf" : 
                EASYPAY_CONFIRM_URL;
        
        // 构建确认表单
        StringBuilder formBuilder = new StringBuilder();
        formBuilder.append("<html><body onload='document.forms[0].submit()'>");
        formBuilder.append("<form action='").append(confirmUrl).append("' method='post'>");
        formBuilder.append("<input type='hidden' name='auth_token' value='").append(authToken).append("'>");
        formBuilder.append("<input type='hidden' name='postBackURL' value='").append(CALLBACK_URL_PREFIX).append("'>");
        formBuilder.append("</form></body></html>");
        
        String confirmFormHtml = formBuilder.toString();
        
        // 输出确认表单
        Log.d(TAG, "提交的确认表单: " + confirmFormHtml);
        
        // 使用loadDataWithBaseURL加载确认表单
        webView.loadDataWithBaseURL(
            confirmUrl,
            confirmFormHtml,
            "text/html",
            "UTF-8",
            null
        );
    }
    
    /**
     * 处理支付回调
     * 
     * @param url 回调URL
     */
    private void handlePaymentCallback(String url) {
        Log.d(TAG, "处理支付回调: " + url);
        
        try {
            // 解析URL参数
            String query = url.substring(url.indexOf("?") + 1);
            Log.d(TAG, "查询字符串: " + query);
            
            // 提取状态和描述
            String status = "";
            String desc = "";
            String orderRefNumber = "";
            
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2) {
                    if (keyValue[0].equals("status")) {
                        status = keyValue[1];
                    } else if (keyValue[0].equals("desc")) {
                        desc = keyValue[1];
                    } else if (keyValue[0].equals("orderRefNumber")) {
                        orderRefNumber = keyValue[1];
                    }
                }
            }
            
            Log.d(TAG, "支付状态: " + status + ", 描述: " + desc + ", 订单参考号: " + orderRefNumber);
            
            // 检查支付状态
            if (status.equals("Success")) {
                // 支付成功
                Log.d(TAG, "支付成功 - 订单参考号: " + orderRefNumber);
                
                // 返回成功结果
                Intent resultIntent = new Intent();
                resultIntent.putExtra("txnRefNo", txnRefNo);
                resultIntent.putExtra("message", "支付成功");
                setResult(Activity.RESULT_OK, resultIntent);
            } else {
                // 支付失败
                Log.e(TAG, "支付失败 - 错误描述: " + desc);
                
                // 返回失败结果
                Intent resultIntent = new Intent();
                resultIntent.putExtra("txnRefNo", txnRefNo);
                resultIntent.putExtra("message", "支付失败: " + desc);
                setResult(Activity.RESULT_CANCELED, resultIntent);
            }
        } catch (Exception e) {
            Log.e(TAG, "处理支付回调时出错", e);
            
            // 返回错误结果
            Intent resultIntent = new Intent();
            resultIntent.putExtra("txnRefNo", txnRefNo);
            resultIntent.putExtra("message", "处理支付响应时出错: " + e.getMessage());
            setResult(Activity.RESULT_CANCELED, resultIntent);
        }
        
        // 关闭活动
        finish();
    }
    
    /**
     * 处理认证错误
     * 
     * @param errorMessage 错误消息
     */
    private void handleAuthError(String errorMessage) {
        Log.e(TAG, "处理认证错误: " + errorMessage);
        
        // 使用PaymentUtils处理错误
        Map<String, String> errorData = PaymentUtils.Easypaisa.handleAuthError(errorMessage);
        
        // 尝试直接从页面提取更多错误信息
        extractAuthErrorData();
        
        // 尝试获取完整的AuthError页面内容
        extractAuthErrorPageContent();
        
        // 显示错误对话框
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("支付认证失败")
            .setMessage("错误详情: " + errorData.get("desc") + "\n\n" + 
                      errorData.get("errorDetail") + "\n\n" +
                      "请检查支付配置并重试。")
            .setPositiveButton("重试", (dialog, which) -> {
                // 获取支付参数并重新加载
                Intent intent = getIntent();
                String amount = intent.getStringExtra("amount");
                String description = intent.getStringExtra("description");
                String mobileNumber = intent.getStringExtra("mobileNumber");
                String email = intent.getStringExtra("email");
                
                Map<String, String> paymentData = preparePaymentData(amount, description, mobileNumber, email);
                loadEasypayPaymentPage(paymentData);
            })
            .setNegativeButton("取消", (dialog, which) -> {
                // 用户取消支付
                Intent resultIntent = new Intent();
                resultIntent.putExtra("txnRefNo", txnRefNo);
                resultIntent.putExtra("message", "认证失败: " + errorMessage);
                setResult(Activity.RESULT_CANCELED, resultIntent);
                finish();
            })
            .setNeutralButton("测试回调", (dialog, which) -> {
                // 直接测试回调URL
                testPostbackUrl();
            })
            .setCancelable(false)
            .show();
    }
    
    /**
     * 提取认证错误数据
     */
    private void extractAuthErrorData() {
        webView.evaluateJavascript(
            "(function() {" +
            "  try {" +
            "    // 尝试提取页面中的JavaScript错误数据" +
            "    if (window.transactionResponse) {" +
            "      return JSON.stringify(window.transactionResponse);" +
            "    }" +
            "    // 尝试提取页面中提到的错误URL和参数" +
            "    var links = document.getElementsByTagName('a');" +
            "    for (var i = 0; i < links.length; i++) {" +
            "      if (links[i].href.indexOf('app-api.eswap.com') > -1) {" +
            "        return links[i].href;" +
            "      }" +
            "    }" +
            "    // 尝试提取表单数据" +
            "    var forms = document.forms;" +
            "    if (forms.length > 0) {" +
            "      var formData = {};" +
            "      for (var i = 0; i < forms[0].elements.length; i++) {" +
            "        var element = forms[0].elements[i];" +
            "        if (element.name) {" +
            "          formData[element.name] = element.value;" +
            "        }" +
            "      }" +
            "      return JSON.stringify(formData);" +
            "    }" +
            "    // 尝试提取脚本中的URL" +
            "    var scripts = document.getElementsByTagName('script');" +
            "    for (var i = 0; i < scripts.length; i++) {" +
            "      var content = scripts[i].innerText;" +
            "      if (content && content.indexOf('app-api.eswap.com') > -1) {" +
            "        return content;" +
            "      }" +
            "    }" +
            "    return '';" +
            "  } catch (e) {" +
            "    return 'Error: ' + e.message;" +
            "  }" +
            "})();",
            result -> {
                if (result != null && !result.equals("null") && !result.equals("\"\"")) {
                    Log.d(TAG, "提取到认证错误数据: " + result);
                    
                    try {
                        // 解码JavaScript字符串
                        String extractedData = result;
                        if (result.length() > 2) {
                            extractedData = result.substring(1, result.length() - 1)
                                            .replace("\\\"", "\"")
                                            .replace("\\n", "\n")
                                            .replace("\\r", "\r")
                                            .replace("\\t", "\t")
                                            .replace("\\\\", "\\");
                        }
                        
                        Log.d(TAG, "认证错误数据(解码): " + extractedData);
                        
                        // 如果提取到了URL，尝试访问
                        if (extractedData.startsWith("http")) {
                            Log.d(TAG, "发现回调URL，尝试访问: " + extractedData);
                            testPostbackWithUrl(extractedData);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "解析认证错误数据时出错", e);
                    }
                } else {
                    Log.d(TAG, "未能提取认证错误数据");
                }
            }
        );
    }
    
    /**
     * 提取AuthError页面的完整内容
     */
    private void extractAuthErrorPageContent() {
        webView.evaluateJavascript(
            "(function() {" +
            "  try {" +
            "    // 获取页面标题" +
            "    var title = document.title || '';" +
            "    " +
            "    // 获取主要错误信息" +
            "    var errorMsgElement = document.getElementById('errorMessages_body');" +
            "    var errorMsg = errorMsgElement ? errorMsgElement.innerText : '';" +
            "    " +
            "    // 检查是否是Invalid Request页面" +
            "    var bodyText = document.body ? document.body.innerText : '';" +
            "    var isInvalidRequest = bodyText.includes('Invalid Request') || bodyText.includes('You have exceeded the time allowed');" +
            "    if (isInvalidRequest) {" +
            "      return JSON.stringify({" +
            "        isInvalidRequest: true," +
            "        bodyText: bodyText" +
            "      });" +
            "    }" +
            "    " +
            "    // 获取页面中的所有文本内容" +
            "    " +
            "    // 获取页面中的所有链接" +
            "    var links = [];" +
            "    var allLinks = document.getElementsByTagName('a');" +
            "    for (var i = 0; i < allLinks.length; i++) {" +
            "      links.push({" +
            "        text: allLinks[i].innerText," +
            "        href: allLinks[i].href" +
            "      });" +
            "    }" +
            "    " +
            "    // 获取页面中的所有表单" +
            "    var forms = [];" +
            "    var allForms = document.forms;" +
            "    for (var i = 0; i < allForms.length; i++) {" +
            "      var formData = {" +
            "        action: allForms[i].action," +
            "        method: allForms[i].method," +
            "        elements: []" +
            "      };" +
            "      " +
            "      for (var j = 0; j < allForms[i].elements.length; j++) {" +
            "        var element = allForms[i].elements[j];" +
            "        formData.elements.push({" +
            "          name: element.name," +
            "          type: element.type," +
            "          value: element.value" +
            "        });" +
            "      }" +
            "      " +
            "      forms.push(formData);" +
            "    }" +
            "    " +
            "    // 获取页面中的所有脚本" +
            "    var scripts = [];" +
            "    var allScripts = document.getElementsByTagName('script');" +
            "    for (var i = 0; i < allScripts.length; i++) {" +
            "      if (allScripts[i].innerText && allScripts[i].innerText.trim() !== '') {" +
            "        scripts.push(allScripts[i].innerText);" +
            "      }" +
            "    }" +
            "    " +
            "    // 返回所有收集的信息" +
            "    return JSON.stringify({" +
            "      title: title," +
            "      errorMsg: errorMsg," +
            "      bodyText: bodyText," +
            "      links: links," +
            "      forms: forms," +
            "      scripts: scripts" +
            "    });" +
            "  } catch (e) {" +
            "    return 'Error: ' + e.message;" +
            "  }" +
            "})();",
            result -> {
                if (result != null && !result.equals("null") && !result.equals("\"\"")) {
                    Log.d(TAG, "提取到AuthError页面内容: " + result);
                    
                    try {
                        // 解码JavaScript字符串
                        String extractedData = result;
                        if (result.length() > 2) {
                            extractedData = result.substring(1, result.length() - 1)
                                            .replace("\\\"", "\"")
                                            .replace("\\n", "\n")
                                            .replace("\\r", "\r")
                                            .replace("\\t", "\t")
                                            .replace("\\\\", "\\");
                        }
                        
                        // 解析JSON数据
                        JSONObject pageData = new JSONObject(extractedData);
                        
                        // 检查是否是Invalid Request页面
                        if (pageData.optBoolean("isInvalidRequest", false)) {
                            Log.e(TAG, "检测到Invalid Request错误页面");
                            String bodyText = pageData.optString("bodyText", "");
                            Log.e(TAG, "Invalid Request页面内容: " + bodyText);
                            
                            // 处理Invalid Request错误
                            handleInvalidRequestError(bodyText);
                            return;
                        }
                        
                        // 记录页面标题
                        String title = pageData.optString("title", "");
                        Log.d(TAG, "AuthError页面标题: " + title);
                        
                        // 记录错误消息
                        String errorMsg = pageData.optString("errorMsg", "");
                        if (!errorMsg.isEmpty()) {
                            Log.e(TAG, "AuthError错误消息: " + errorMsg);
                        }
                        
                        // 记录页面文本
                        String bodyText = pageData.optString("bodyText", "");
                        Log.d(TAG, "AuthError页面文本: " + bodyText);
                        
                        // 记录链接
                        JSONArray links = pageData.optJSONArray("links");
                        if (links != null && links.length() > 0) {
                            Log.d(TAG, "AuthError页面链接:");
                            for (int i = 0; i < links.length(); i++) {
                                JSONObject link = links.getJSONObject(i);
                                String linkText = link.optString("text", "");
                                String linkHref = link.optString("href", "");
                                Log.d(TAG, "  链接 " + i + ": " + linkText + " -> " + linkHref);
                                
                                // 如果链接包含回调URL，尝试访问
                                if (linkHref.contains("app-api.eswap.com")) {
                                    Log.d(TAG, "发现回调URL链接: " + linkHref);
                                    testPostbackWithUrl(linkHref);
                                }
                            }
                        }
                        
                        // 记录表单
                        JSONArray forms = pageData.optJSONArray("forms");
                        if (forms != null && forms.length() > 0) {
                            Log.d(TAG, "AuthError页面表单:");
                            for (int i = 0; i < forms.length(); i++) {
                                JSONObject form = forms.getJSONObject(i);
                                String formAction = form.optString("action", "");
                                String formMethod = form.optString("method", "");
                                Log.d(TAG, "  表单 " + i + ": " + formMethod + " " + formAction);
                                
                                JSONArray elements = form.optJSONArray("elements");
                                if (elements != null) {
                                    for (int j = 0; j < elements.length(); j++) {
                                        JSONObject element = elements.getJSONObject(j);
                                        String elementName = element.optString("name", "");
                                        String elementType = element.optString("type", "");
                                        String elementValue = element.optString("value", "");
                                        Log.d(TAG, "    元素 " + j + ": " + elementName + " (" + elementType + ") = " + elementValue);
                                    }
                                }
                            }
                        }
                        
                        // 记录脚本
                        JSONArray scripts = pageData.optJSONArray("scripts");
                        if (scripts != null && scripts.length() > 0) {
                            Log.d(TAG, "AuthError页面脚本:");
                            for (int i = 0; i < scripts.length(); i++) {
                                String script = scripts.getString(i);
                                Log.d(TAG, "  脚本 " + i + ":");
                                
                                // 分块输出脚本内容，避免日志过长
                                int maxLogSize = 1000;
                                for (int j = 0; j <= script.length() / maxLogSize; j++) {
                                    int start = j * maxLogSize;
                                    int end = (j + 1) * maxLogSize;
                                    end = end > script.length() ? script.length() : end;
                                    Log.d(TAG, "    " + script.substring(start, end));
                                }
                                
                                // 检查脚本中是否包含回调URL
                                if (script.contains("app-api.eswap.com")) {
                                    Log.d(TAG, "脚本中包含回调URL");
                                    
                                    // 提取transactionResponse
                                    if (script.contains("transactionResponse")) {
                                        Log.d(TAG, "脚本中包含transactionResponse");
                                        
                                        // 尝试提取完整的错误信息
                                        extractTransactionResponseFromScript(script);
                                    }
                                }
                            }
                        }
                        
                    } catch (Exception e) {
                        Log.e(TAG, "解析AuthError页面内容时出错", e);
                    }
                } else {
                    Log.d(TAG, "未能提取AuthError页面内容");
                }
            }
        );
    }
    
    /**
     * 从脚本中提取交易响应信息
     */
    private void extractTransactionResponseFromScript(String script) {
        try {
            // 查找transactionResponse部分
            int startIndex = script.indexOf("transactionResponse");
            if (startIndex >= 0) {
                // 查找等号后面的值
                int equalIndex = script.indexOf("=", startIndex);
                if (equalIndex >= 0) {
                    // 查找分号结束的位置
                    int endIndex = script.indexOf(";", equalIndex);
                    if (endIndex >= 0) {
                        // 提取transactionResponse的值
                        String transactionResponse = script.substring(equalIndex + 1, endIndex).trim();
                        Log.d(TAG, "提取到transactionResponse: " + transactionResponse);
                        
                        // 如果值是字符串，去掉引号
                        if (transactionResponse.startsWith("\"") && transactionResponse.endsWith("\"")) {
                            transactionResponse = transactionResponse.substring(1, transactionResponse.length() - 1);
                        }
                        
                        // 如果包含Parameter Authentication failed，记录下来
                        if (transactionResponse.contains("Parameter Authentication failed")) {
                            Log.e(TAG, "交易响应包含参数认证失败: " + transactionResponse);
                            
                            // 如果包含回调URL，尝试访问
                            if (transactionResponse.contains("app-api.eswap.com")) {
                                int urlStartIndex = transactionResponse.indexOf("http");
                                if (urlStartIndex >= 0) {
                                    int urlEndIndex = transactionResponse.indexOf("\"", urlStartIndex);
                                    if (urlEndIndex < 0) {
                                        urlEndIndex = transactionResponse.length();
                                    }
                                    String callbackUrl = transactionResponse.substring(urlStartIndex, urlEndIndex);
                                    Log.d(TAG, "从交易响应中提取到回调URL: " + callbackUrl);
                                    testPostbackWithUrl(callbackUrl);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "从脚本中提取交易响应时出错", e);
        }
    }
    
    /**
     * 测试回调URL
     */
    private void testPostbackUrl() {
        Log.d(TAG, "测试回调URL");
        
        // 准备测试数据
        final String callbackUrl = CALLBACK_URL_PREFIX;
        final String testData = "status=Failed&desc=Parameter Authentication failed&orderRefNumber=" + txnRefNo;
        
        // 在后台线程中执行网络请求
        new Thread(() -> {
            try {
                // 创建URL
                URL url = new URL(callbackUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                connection.setDoOutput(true);
                
                // 发送数据
                try (OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream())) {
                    writer.write(testData);
                    writer.flush();
                }
                
                // 获取响应
                int responseCode = connection.getResponseCode();
                
                // 读取响应内容
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                
                // 输出响应信息
                final String responseMessage = response.toString();
                Log.d(TAG, "回调URL测试结果 - 状态码: " + responseCode + ", 响应: " + responseMessage);
                
                // 在UI线程中显示结果
                runOnUiThread(() -> {
                    showToast("回调测试完成，状态码: " + responseCode);
                    
                    // 显示详细结果对话框
                    new androidx.appcompat.app.AlertDialog.Builder(this)
                        .setTitle("回调测试结果")
                        .setMessage("URL: " + callbackUrl + "\n\n" +
                                  "数据: " + testData + "\n\n" +
                                  "状态码: " + responseCode + "\n\n" +
                                  "响应: " + responseMessage)
                        .setPositiveButton("确定", null)
                        .show();
                });
                
            } catch (Exception e) {
                Log.e(TAG, "测试回调URL时出错", e);
                
                // 在UI线程中显示错误
                runOnUiThread(() -> {
                    showToast("回调测试失败: " + e.getMessage());
                });
            }
        }).start();
    }
    
    /**
     * 使用指定URL测试回调
     * 
     * @param callbackUrl 回调URL
     */
    private void testPostbackWithUrl(String callbackUrl) {
        Log.d(TAG, "使用指定URL测试回调: " + callbackUrl);
        
        // 在后台线程中执行网络请求
        new Thread(() -> {
            try {
                // 创建URL
                URL url = new URL(callbackUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                
                // 获取响应
                int responseCode = connection.getResponseCode();
                
                // 读取响应内容
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                
                // 输出响应信息
                final String responseMessage = response.toString();
                Log.d(TAG, "指定URL回调测试结果 - 状态码: " + responseCode + ", 响应: " + responseMessage);
                
                // 在UI线程中显示结果
                runOnUiThread(() -> {
                    showToast("回调测试完成，状态码: " + responseCode);
                    
                    // 显示详细结果对话框
                    new androidx.appcompat.app.AlertDialog.Builder(this)
                        .setTitle("回调测试结果")
                        .setMessage("URL: " + callbackUrl + "\n\n" +
                                  "状态码: " + responseCode + "\n\n" +
                                  "响应: " + responseMessage)
                        .setPositiveButton("确定", null)
                        .show();
                });
                
            } catch (Exception e) {
                Log.e(TAG, "测试指定URL回调时出错", e);
                
                // 在UI线程中显示错误
                runOnUiThread(() -> {
                    showToast("回调测试失败: " + e.getMessage());
                });
            }
        }).start();
    }
    
    /**
     * 处理Invalid Request错误
     * 
     * @param errorContent 错误页面内容
     */
    private void handleInvalidRequestError(String errorContent) {
        Log.e(TAG, "处理Invalid Request错误");
        
        // 检查错误类型
        boolean isTimeExceeded = errorContent.contains("exceeded the time") || 
                                errorContent.contains("time allowed to retry");
        
        String errorMessage;
        String errorDetail;
        
        if (isTimeExceeded) {
            errorMessage = "您已超过重试支付的允许时间";
            errorDetail = "这可能是由于以下原因:\n" +
                         "1. 支付会话已过期\n" +
                         "2. 您尝试了多次重复的支付请求\n" +
                         "3. 支付网关暂时不可用";
            
            Log.e(TAG, "支付会话已过期: " + errorMessage);
        } else {
            errorMessage = "无效的请求";
            errorDetail = "这可能是由于以下原因:\n" +
                         "1. 参数认证失败\n" +
                         "2. 商店ID或其他参数配置错误\n" +
                         "3. 回调URL格式不正确";
            
            Log.e(TAG, "无效的支付请求: " + errorMessage);
        }
        
        // 显示错误对话框
        runOnUiThread(() -> {
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("支付请求错误")
                .setMessage(errorMessage + "\n\n" + errorDetail + "\n\n请检查支付配置并重试。")
                .setPositiveButton("重试", (dialog, which) -> {
                    // 获取支付参数并重新加载
                    Intent intent = getIntent();
                    String amount = intent.getStringExtra("amount");
                    String description = intent.getStringExtra("description");
                    String mobileNumber = intent.getStringExtra("mobileNumber");
                    String email = intent.getStringExtra("email");
                    
                    Map<String, String> paymentData = preparePaymentData(amount, description, mobileNumber, email);
                    loadEasypayPaymentPage(paymentData);
                })
                .setNegativeButton("取消", (dialog, which) -> {
                    // 用户取消支付
                    Intent resultIntent = new Intent();
                    resultIntent.putExtra("txnRefNo", txnRefNo);
                    resultIntent.putExtra("message", "支付请求错误: " + errorMessage);
                    setResult(Activity.RESULT_CANCELED, resultIntent);
                    finish();
                })
                .setNeutralButton("诊断", (dialog, which) -> {
                    // 运行网络诊断
                    runNetworkDiagnostics();
                    
                    // 测试直接API调用
                    testDirectApiCall();
                    
                    // 测试回调URL
                    testPostbackUrl();
                })
                .setCancelable(false)
                .show();
        });
    }
    
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }
    
    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            // 用户取消支付
            Log.d(TAG, "用户取消支付");
            
            Intent resultIntent = new Intent();
            resultIntent.putExtra("txnRefNo", txnRefNo);
            resultIntent.putExtra("message", "用户取消支付");
            setResult(Activity.RESULT_CANCELED, resultIntent);
            
            super.onBackPressed();
        }
    }
} 