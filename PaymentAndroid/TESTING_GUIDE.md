# JazzCash 支付集成测试指南

本指南将帮助您测试 JazzCash 支付集成，包括常见问题的解决方案。

## 前置条件

1. 您需要有 JazzCash 的测试账号（沙箱环境）
2. 确保已正确设置以下参数：
   - 商户 ID (Merchant ID)
   - 完整性盐值 (Integrity Salt)
   - 密码 (Password)
   - 回调 URL (Return URL)

## 测试流程

### 1. 更新商户凭据

在 `JazzCashUtils.kt` 文件中，确保使用您的实际测试账号信息替换以下占位符：

```kotlin
private const val INTEGRITY_SALT = "123456789" // 替换为您的完整性盐值
private const val MERCHANT_ID = "MC12345" // 替换为您的商户 ID
private const val PASSWORD = "password123" // 替换为您的密码
```

### 2. 更新回调 URL

确保回调 URL 设置正确，并且可以从 JazzCash 服务器访问：

```kotlin
private const val RETURN_URL = "http://************:8080/jazzcash/callback" // 替换为您的回调 URL
```

如果您使用本地服务器进行测试，请确保：
- 服务器已启动并正在运行
- 使用公网 IP 或域名，而不是 localhost
- 考虑使用 ngrok 等工具创建临时公网 URL

### 3. 运行 Spring Boot 服务器

```bash
cd server
chmod +x ./mvnw
./mvnw spring-boot:run
```

确认服务器在 8080 端口启动成功。

### 4. 运行 Android 应用

在 Android Studio 中构建并运行应用，或使用以下命令：

```bash
cd JazzCashAndroidNative
./gradlew installDebug
```

### 5. 测试支付流程

1. 点击应用中的"支付"按钮
2. 观察支付页面是否正确加载
3. 如果使用测试账号，可能会直接返回测试结果
4. 检查应用中的支付结果处理

### 6. 测试回调处理

访问以下 URL 测试回调处理：

```
http://localhost:8080/test
```

您可以：
- 手动触发回调
- 查询交易状态
- 查看所有交易记录

## 常见问题及解决方案

### 1. 白屏或页面加载失败

**问题**: 支付页面显示白屏或加载失败。

**解决方案**:
- 检查商户凭据是否正确
- 确认网络连接正常
- 查看日志中是否有错误信息

### 2. 商户 ID 无效错误

**问题**: 收到 "Please provide a valid value for pp_MerchantId" 错误。

**解决方案**:
- 确保使用正确的商户 ID
- 检查 JazzCash 账号状态是否正常
- 联系 JazzCash 支持确认账号信息

### 3. 回调 URL 错误

**问题**: 支付成功但回调处理失败。

**解决方案**:
- 确保回调 URL 可以从外网访问
- 检查服务器是否正常运行
- 使用 ngrok 等工具创建临时公网 URL
- 查看服务器日志是否接收到回调请求

### 4. 混合内容警告

**问题**: 浏览器或 WebView 显示混合内容警告。

**解决方案**:
- 已通过 `network_security_config.xml` 和 `WebView` 设置解决
- 如果仍有问题，考虑使用 HTTPS 回调 URL

### 5. 安全哈希错误

**问题**: 收到安全哈希无效的错误。

**解决方案**:
- 确保使用正确的完整性盐值
- 检查哈希计算逻辑是否正确
- 参考 JazzCash 文档中的哈希计算示例

## 日志分析

应用会在 Logcat 中输出详细的日志信息，可以通过以下标签过滤：

- `MainActivity`: 主活动日志
- `PaymentActivity`: 支付活动日志
- `JazzCashUtils`: JazzCash 工具类日志

查看这些日志可以帮助您诊断问题。

## 服务器日志

服务器会在控制台输出详细的日志信息，包括：

- 收到的回调请求
- 请求头信息
- 请求参数
- 处理结果

查看这些日志可以帮助您确认回调是否正确处理。

## 联系支持

如果您遇到无法解决的问题，请联系 JazzCash 技术支持或查阅官方文档：

- [JazzCash 开发者文档](https://developer.jazzcash.com.pk/) 