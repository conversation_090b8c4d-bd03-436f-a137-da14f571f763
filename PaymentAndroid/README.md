# 巴基斯坦支付集成 - Android应用

该项目实现了巴基斯坦主要支付服务（JazzCash和Easypaisa）在Android应用中的原生集成。支持两种主要的支付方式：

1. 页面重定向支付（支持银行卡支付）
2. API支付（仅支持移动钱包支付）

## 功能特点

- 支持JazzCash页面重定向支付流程（适用于银行卡支付）
- 支持JazzCash API支付流程（适用于钱包支付）
- 支持Easypaisa页面重定向支付流程（适用于银行卡支付）
- 支持Easypaisa API支付流程（适用于钱包支付）
- 包含安全哈希生成工具
- 支持支付结果回调处理
- 支持交易状态查询
- 用户友好的错误处理和提示
- 简洁现代的UI设计

## 项目结构

```
PaymentAndroid/
│
├── app/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/example/paymentapp/
│   │   │   │   ├── MainActivity.kt          # 主活动，提供支付渠道和支付方式选择
│   │   │   │   ├── PaymentActivity.kt       # JazzCash页面重定向支付实现
│   │   │   │   ├── ApiPaymentActivity.kt    # JazzCash API支付实现
│   │   │   │   ├── EasypaisaActivity.kt     # Easypaisa页面重定向支付实现
│   │   │   │   ├── EasypaisaApiActivity.kt  # Easypaisa API支付实现
│   │   │   │   └── PaymentUtils.kt          # 支付工具类，处理安全哈希生成等功能
│   │   │   │       ├── JazzCash             # JazzCash特定的工具方法
│   │   │   │       └── Easypaisa            # Easypaisa特定的工具方法
│   │   │   │
│   │   │   ├── res/                         # 资源文件
│   │   │   └── AndroidManifest.xml          # 应用清单文件
│   │   │
│   │   └── test/                            # 测试目录
│   │
│   └── build.gradle                         # 应用级构建脚本
│
├── gradle/                                  # Gradle包装器
└── build.gradle                             # 项目级构建脚本
```

## 支付流程

详细的支付流程请参阅 [PAYMENT_FLOW.md](PAYMENT_FLOW.md) 文档，其中包含了各种支付渠道和支付方式的完整流程图和说明。

### 页面重定向支付流程（通用）

1. 用户选择支付渠道（JazzCash或Easypaisa）
2. 用户输入金额和描述
3. 用户选择"页面重定向支付"
4. 系统生成交易参考号和支付请求数据
5. 使用WebView加载支付页面
6. 用户在页面上输入银行卡信息并确认支付
7. 支付网关处理支付并重定向到回调URL
8. 应用解析回调数据并显示支付结果

### API支付流程（通用）

1. 用户选择支付渠道（JazzCash或Easypaisa）
2. 用户输入金额和描述
3. 用户选择"API支付"
4. 用户输入钱包关联的手机号
5. 系统将请求发送到服务器端
6. 服务器与支付网关API通信并返回结果
7. 用户在手机上的支付应用中确认支付
8. 应用查询支付状态并显示结果

## 安全考虑

- 商户凭据（密码、密钥等）不应在客户端存储
- API支付方式更安全，因为敏感信息仅在服务器端处理
- 所有请求均使用HTTPS加密传输
- 实现了HMAC-SHA256安全哈希验证机制
- 每个交易生成唯一的交易ID，防止重放攻击
- 详细的安全措施请参阅 [PAYMENT_FLOW.md](PAYMENT_FLOW.md) 文档中的"安全措施"部分

## 集成指南

要将支付集成到您的应用中，请按照以下步骤操作：

### 1. 更新配置

在 `PaymentUtils.kt` 文件中，更新以下参数：

```kotlin
// JazzCash配置
object JazzCash {
    private const val MERCHANT_ID = "YOUR_JAZZ_CASH_MERCHANT_ID"
    private const val PASSWORD = "YOUR_JAZZ_CASH_PASSWORD"
    private const val INTEGRITY_SALT = "YOUR_JAZZ_CASH_INTEGRITY_SALT"
}

// Easypaisa配置
object Easypaisa {
    private const val MERCHANT_ID = "YOUR_EASYPAISA_MERCHANT_ID"
    private const val MERCHANT_KEY = "YOUR_EASYPAISA_MERCHANT_KEY"
}
```

### 2. 添加权限

确保在 `AndroidManifest.xml` 中添加了以下权限：

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 3. 集成代码

- 复制 `PaymentActivity.kt`、`ApiPaymentActivity.kt`、`EasypaisaActivity.kt`、`EasypaisaApiActivity.kt`、`PaymentUtils.kt` 和相关布局文件到您的项目中
- 在您的活动中实现支付流程：

```kotlin
// 初始化JazzCash页面重定向支付
private fun initiateJazzCashRedirectPayment() {
    val amount = etAmount.text.toString().trim()
    val description = etDescription.text.toString().trim()
    
    // 生成交易参考号
    val txnRefNo = "JC" + System.currentTimeMillis() + String.format("%04d", Random().nextInt(10000))
    
    // 生成交易时间和过期时间
    val dateFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
    val txnDateTime = dateFormat.format(Date())
    val calendar = Calendar.getInstance()
    calendar.add(Calendar.HOUR, 1)
    val txnExpiryDateTime = dateFormat.format(calendar.time)
    
    // 准备支付请求数据
    val postData = PaymentUtils.JazzCash.preparePostData(
        (amount.toDouble() * 100).toInt().toString(),
        "billRef" + System.currentTimeMillis(),
        description,
        txnDateTime,
        txnExpiryDateTime,
        txnRefNo
    )
    
    // 启动支付活动
    val intent = Intent(this, PaymentActivity::class.java)
    intent.putExtra("postData", postData)
    intent.putExtra("txnRefNo", txnRefNo)
    startActivityForResult(intent, JAZZCASH_PAYMENT_REQUEST_CODE)
}

// 初始化Easypaisa页面重定向支付
private fun initiateEasypaisaRedirectPayment() {
    val amount = etAmount.text.toString().trim()
    val description = etDescription.text.toString().trim()
    val mobileNumber = "03123456789" // 在实际应用中，应该从用户输入获取
    val email = "<EMAIL>" // 在实际应用中，应该从用户输入获取
    
    // 启动Easypaisa支付活动
    val intent = Intent(this, EasypaisaActivity::class.java)
    intent.putExtra("amount", amount)
    intent.putExtra("description", description)
    intent.putExtra("mobileNumber", mobileNumber)
    intent.putExtra("email", email)
    startActivityForResult(intent, EASYPAISA_PAYMENT_REQUEST_CODE)
}
```

## 测试与开发

- 该项目使用测试环境进行开发和测试
- JazzCash沙箱环境URL: `https://sandbox.jazzcash.com.pk/`
- Easypaisa测试环境URL: `https://easypaisa.sandbox.com/payment`
- 需要向相应的支付服务提供商申请测试凭据
- 有关测试的详细信息，请参阅 [TESTING_GUIDE.md](TESTING_GUIDE.md)

## 系统要求

- Android 5.0+ (API级别21+)
- Android Studio 3.5+
- Kotlin 1.3+

## 许可证

该项目根据MIT许可证授权。详情请参阅LICENSE文件。 