# 支付集成流程文档

本文档详细说明了支付集成的各种流程，包括JazzCash和Easypaisa两种支付渠道的页面重定向和API支付方式。

## 目录

1. [JazzCash支付流程](#jazzcash支付流程)
   - [JazzCash页面重定向支付流程](#jazzcash页面重定向支付流程)
   - [JazzCash API支付流程](#jazzcash-api支付流程)
2. [Easypaisa支付流程](#easypaisa支付流程)
   - [Easypaisa页面重定向支付流程](#easypaisa页面重定向支付流程)
   - [Easypaisa API支付流程](#easypaisa-api支付流程)
3. [安全措施](#安全措施)
   - [数据完整性](#数据完整性)
   - [防止重放攻击](#防止重放攻击)
   - [交易验证](#交易验证)

## JazzCash支付流程

### JazzCash页面重定向支付流程

```
┌───────────┐        ┌───────────┐        ┌────────────────┐        ┌────────────┐        ┌───────────────┐
│           │        │           │        │                │        │            │        │               │
│   用户    │        │ 商户App   │        │  商户服务端    │        │  JazzCash  │        │ 用户JazzCash  │
│           │        │           │        │                │        │  服务器    │        │    App/网页   │
└─────┬─────┘        └─────┬─────┘        └────────┬───────┘        └─────┬──────┘        └───────┬───────┘
      │                    │                       │                      │                       │
      │ 1.选择商品并结账   │                       │                      │                       │
      │ ──────────────────>│                       │                      │                       │
      │                    │                       │                      │                       │
      │                    │ 2.生成交易ID和参数    │                      │                       │
      │                    │ ───────────────────────>                     │                       │
      │                    │                       │                      │                       │
      │                    │                       │ 3.生成安全哈希       │                      │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │  4.返回支付表单URL   │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │  5.返回支付表单URL    │                      │                       │
      │                    │ <───────────────────────                     │                       │
      │                    │                       │                      │                       │
      │                    │ 6.加载支付页面        │                      │                       │
      │                    │ ──────────────────────────────────────────────────────────────────────>
      │                    │                       │                      │                       │
      │                    │                       │                      │  7.用户输入支付信息   │
      │                    │                       │                      │ <─────────────────────│
      │                    │                       │                      │                       │
      │                    │                       │                      │  8.处理支付           │
      │                    │                       │                      │ ─────────────────────>│
      │                    │                       │                      │                       │
      │                    │                       │  9.支付结果通知      │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │  10.重定向回商户App   │                      │                       │
      │ <────────────────────────────────────────────────────────────────────────────────────────│
      │                    │                       │                      │                       │
      │                    │ 11.请求验证支付结果   │                      │                       │
      │                    │ ───────────────────────>                     │                       │
      │                    │                       │                      │                       │
      │                    │                       │ 12.验证支付结果      │                       │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │  13.确认支付状态     │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │  14.显示支付结果      │                      │                       │
      │ <────────────────────                     │                      │                       │
      │                    │                       │                      │                       │
```

#### 流程详解

1. **用户选择商品并结账**：用户在商户App中选择商品并点击"结账"按钮。
2. **生成交易ID和参数**：商户App生成唯一的交易ID和其他必要参数（金额、描述等）。
3. **生成安全哈希**：商户服务端使用交易参数和密钥生成安全哈希，确保数据完整性。
4. **返回支付表单URL**：JazzCash服务器返回带有支付表单的URL。
5. **返回支付表单URL到App**：商户服务端将URL返回给商户App。
6. **加载支付页面**：商户App打开WebView并加载JazzCash支付页面。
7. **用户输入支付信息**：用户在JazzCash页面输入银行卡信息或选择其他支付方式。
8. **处理支付**：JazzCash处理支付请求。
9. **支付结果通知**：JazzCash向商户服务端发送支付结果通知。
10. **重定向回商户App**：JazzCash将用户重定向回商户App（通过自定义URL Scheme）。
11. **请求验证支付结果**：商户App向商户服务端请求验证支付结果。
12. **验证支付结果**：商户服务端向JazzCash验证支付结果。
13. **确认支付状态**：JazzCash确认支付状态。
14. **显示支付结果**：商户App显示最终的支付结果给用户。

### JazzCash API支付流程

```
┌───────────┐        ┌───────────┐        ┌────────────────┐        ┌────────────┐        ┌───────────────┐
│           │        │           │        │                │        │            │        │               │
│   用户    │        │ 商户App   │        │  商户服务端    │        │  JazzCash  │        │ 用户JazzCash  │
│           │        │           │        │                │        │  服务器    │        │    App        │
└─────┬─────┘        └─────┬─────┘        └────────┬───────┘        └─────┬──────┘        └───────┬───────┘
      │                    │                       │                      │                       │
      │ 1.输入手机号并确认 │                       │                      │                       │
      │ ──────────────────>│                       │                      │                       │
      │                    │                       │                      │                       │
      │                    │ 2.生成交易ID和参数    │                      │                       │
      │                    │ ───────────────────────>                     │                       │
      │                    │                       │                      │                       │
      │                    │                       │ 3.生成安全哈希       │                       │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │ 4.发送API请求        │                       │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │                      │ 5.向用户发送支付通知  │
      │                    │                       │                      │ ─────────────────────>│
      │                    │                       │                      │                       │
      │                    │                       │                      │ 6.用户确认支付        │
      │                    │                       │                      │ <─────────────────────│
      │                    │                       │                      │                       │
      │                    │                       │ 7.支付结果通知       │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │ 8.返回支付结果        │                      │                       │
      │                    │ <───────────────────────                     │                       │
      │                    │                       │                      │                       │
      │                    │ 9.显示支付结果        │                      │                       │
      │ <────────────────────                     │                      │                       │
      │                    │                       │                      │                       │
```

#### 流程详解

1. **用户输入手机号并确认**：用户在商户App中输入JazzCash关联的手机号并确认支付。
2. **生成交易ID和参数**：商户App生成唯一的交易ID和其他必要参数。
3. **生成安全哈希**：商户服务端使用交易参数和密钥生成安全哈希。
4. **发送API请求**：商户服务端向JazzCash API发送支付请求。
5. **向用户发送支付通知**：JazzCash向用户的手机发送支付通知（短信或推送）。
6. **用户确认支付**：用户在JazzCash App中确认支付。
7. **支付结果通知**：JazzCash向商户服务端发送支付结果通知。
8. **返回支付结果**：商户服务端将支付结果返回给商户App。
9. **显示支付结果**：商户App显示支付结果给用户。

## Easypaisa支付流程

### Easypaisa页面重定向支付流程

```
┌───────────┐        ┌───────────┐        ┌────────────────┐        ┌────────────┐        ┌───────────────┐
│           │        │           │        │                │        │            │        │               │
│   用户    │        │ 商户App   │        │  商户服务端    │        │  Easypaisa │        │ 用户Easypaisa │
│           │        │           │        │                │        │  服务器    │        │    App/网页   │
└─────┬─────┘        └─────┬─────┘        └────────┬───────┘        └─────┬──────┘        └───────┬───────┘
      │                    │                       │                      │                       │
      │ 1.选择商品并结账   │                       │                      │                       │
      │ ──────────────────>│                       │                      │                       │
      │                    │                       │                      │                       │
      │                    │ 2.生成订单ID和参数    │                      │                       │
      │                    │ ───────────────────────>                     │                       │
      │                    │                       │                      │                       │
      │                    │                       │ 3.生成订单哈希       │                       │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │  4.返回支付表单URL   │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │  5.返回支付表单URL    │                      │                       │
      │                    │ <───────────────────────                     │                       │
      │                    │                       │                      │                       │
      │                    │ 6.加载支付页面        │                      │                       │
      │                    │ ──────────────────────────────────────────────────────────────────────>
      │                    │                       │                      │                       │
      │                    │                       │                      │  7.用户输入支付信息   │
      │                    │                       │                      │ <─────────────────────│
      │                    │                       │                      │                       │
      │                    │                       │                      │  8.处理支付           │
      │                    │                       │                      │ ─────────────────────>│
      │                    │                       │                      │                       │
      │                    │                       │  9.支付结果通知      │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │  10.重定向回商户App   │                      │                       │
      │ <────────────────────────────────────────────────────────────────────────────────────────│
      │                    │                       │                      │                       │
      │                    │ 11.请求验证支付结果   │                      │                       │
      │                    │ ───────────────────────>                     │                       │
      │                    │                       │                      │                       │
      │                    │                       │ 12.验证支付结果      │                       │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │  13.确认支付状态     │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │  14.显示支付结果      │                      │                       │
      │ <────────────────────                     │                      │                       │
      │                    │                       │                      │                       │
```

#### 流程详解

1. **用户选择商品并结账**：用户在商户App中选择商品并点击"结账"按钮。
2. **生成订单ID和参数**：商户App生成唯一的订单ID和其他必要参数。
3. **生成订单哈希**：商户服务端使用订单参数和密钥生成安全哈希。
4. **返回支付表单URL**：Easypaisa服务器返回带有支付表单的URL。
5. **返回支付表单URL到App**：商户服务端将URL返回给商户App。
6. **加载支付页面**：商户App打开WebView并加载Easypaisa支付页面。
7. **用户输入支付信息**：用户在Easypaisa页面输入银行卡信息或选择其他支付方式。
8. **处理支付**：Easypaisa处理支付请求。
9. **支付结果通知**：Easypaisa向商户服务端发送支付结果通知。
10. **重定向回商户App**：Easypaisa将用户重定向回商户App。
11. **请求验证支付结果**：商户App向商户服务端请求验证支付结果。
12. **验证支付结果**：商户服务端向Easypaisa验证支付结果。
13. **确认支付状态**：Easypaisa确认支付状态。
14. **显示支付结果**：商户App显示最终的支付结果给用户。

### Easypaisa API支付流程

```
┌───────────┐        ┌───────────┐        ┌────────────────┐        ┌────────────┐        ┌───────────────┐
│           │        │           │        │                │        │            │        │               │
│   用户    │        │ 商户App   │        │  商户服务端    │        │  Easypaisa │        │ 用户Easypaisa │
│           │        │           │        │                │        │  服务器    │        │    App        │
└─────┬─────┘        └─────┬─────┘        └────────┬───────┘        └─────┬──────┘        └───────┬───────┘
      │                    │                       │                      │                       │
      │ 1.输入手机号并确认 │                       │                      │                       │
      │ ──────────────────>│                       │                      │                       │
      │                    │                       │                      │                       │
      │                    │ 2.生成订单ID和参数    │                      │                       │
      │                    │ ───────────────────────>                     │                       │
      │                    │                       │                      │                       │
      │                    │                       │ 3.生成订单哈希       │                       │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │ 4.发送API请求        │                       │
      │                    │                       │ ────────────────────>│                       │
      │                    │                       │                      │                       │
      │                    │                       │                      │ 5.向用户发送支付通知  │
      │                    │                       │                      │ ─────────────────────>│
      │                    │                       │                      │                       │
      │                    │                       │                      │ 6.用户确认支付        │
      │                    │                       │                      │ <─────────────────────│
      │                    │                       │                      │                       │
      │                    │                       │ 7.支付结果通知       │                       │
      │                    │                       │ <────────────────────│                       │
      │                    │                       │                      │                       │
      │                    │ 8.返回支付结果        │                      │                       │
      │                    │ <───────────────────────                     │                       │
      │                    │                       │                      │                       │
      │                    │ 9.显示支付结果        │                      │                       │
      │ <────────────────────                     │                      │                       │
      │                    │                       │                      │                       │
```

#### 流程详解

1. **用户输入手机号并确认**：用户在商户App中输入Easypaisa关联的手机号并确认支付。
2. **生成订单ID和参数**：商户App生成唯一的订单ID和其他必要参数。
3. **生成订单哈希**：商户服务端使用订单参数和密钥生成安全哈希。
4. **发送API请求**：商户服务端向Easypaisa API发送支付请求。
5. **向用户发送支付通知**：Easypaisa向用户的手机发送支付通知。
6. **用户确认支付**：用户在Easypaisa App中确认支付。
7. **支付结果通知**：Easypaisa向商户服务端发送支付结果通知。
8. **返回支付结果**：商户服务端将支付结果返回给商户App。
9. **显示支付结果**：商户App显示支付结果给用户。

## 安全措施

### 数据完整性

为确保支付数据的完整性和安全性，我们采取了以下措施：

1. **安全哈希生成**：对所有支付请求参数生成安全哈希，防止数据被篡改。
   - JazzCash使用HMAC-SHA256算法生成安全哈希
   - Easypaisa也使用类似的哈希机制

2. **参数验证**：严格验证所有输入参数，确保符合格式要求。

3. **HTTPS通信**：所有与支付网关的通信均通过HTTPS加密协议进行。

### 防止重放攻击

为防止重放攻击，我们采取以下措施：

1. **唯一交易ID**：每次交易生成唯一的交易ID，由时间戳和随机数组成。

2. **交易时间戳**：在支付请求中包含交易时间戳，支付网关会验证时间戳的有效性。

3. **交易过期时间**：设置交易过期时间，超过该时间的交易将被拒绝。

### 交易验证

为确保交易的有效性和完整性，我们采取以下验证措施：

1. **服务器端验证**：所有支付结果通过服务器端进行验证，而不仅仅依赖客户端的回调。

2. **状态码验证**：验证支付网关返回的状态码，确保交易成功完成。

3. **金额验证**：验证支付金额是否与原始请求一致。

4. **通知验证**：验证支付网关发送的通知是否来自有效来源。

5. **交易日志**：记录所有交易的详细日志，用于审计和问题排查。

通过上述安全措施，我们确保整个支付流程的安全性和可靠性，为用户提供安全的支付体验。 