// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		D095CEBA2DE9953F00EC5F37 /* PAYMENT_FLOW.md in Resources */ = {isa = PBXBuildFile; fileRef = D095CEB82DE9953F00EC5F37 /* PAYMENT_FLOW.md */; };
		D095CEBB2DE9953F00EC5F37 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = D095CEB92DE9953F00EC5F37 /* README.md */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		D094AA532DE940B1000E2875 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = D094AA342DE940AF000E2875 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D094AA3B2DE940AF000E2875;
			remoteInfo = iOS;
		};
		D094AA5D2DE940B1000E2875 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = D094AA342DE940AF000E2875 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D094AA3B2DE940AF000E2875;
			remoteInfo = iOS;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		D094AA3C2DE940AF000E2875 /* iOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iOS.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D094AA522DE940B1000E2875 /* iOSTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		D094AA5C2DE940B1000E2875 /* iOSUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOSUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		D095CEB82DE9953F00EC5F37 /* PAYMENT_FLOW.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = PAYMENT_FLOW.md; sourceTree = "<group>"; };
		D095CEB92DE9953F00EC5F37 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		D094AA642DE940B1000E2875 /* Exceptions for "iOS" folder in "iOS" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = D094AA3B2DE940AF000E2875 /* iOS */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		D094AA3E2DE940AF000E2875 /* iOS */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				D094AA642DE940B1000E2875 /* Exceptions for "iOS" folder in "iOS" target */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		D094AA552DE940B1000E2875 /* iOSTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSTests;
			sourceTree = "<group>";
		};
		D094AA5F2DE940B1000E2875 /* iOSUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = iOSUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		D094AA392DE940AF000E2875 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D094AA4F2DE940B1000E2875 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D094AA592DE940B1000E2875 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		D094AA332DE940AF000E2875 = {
			isa = PBXGroup;
			children = (
				D095CEB82DE9953F00EC5F37 /* PAYMENT_FLOW.md */,
				D095CEB92DE9953F00EC5F37 /* README.md */,
				D094AA3E2DE940AF000E2875 /* iOS */,
				D094AA552DE940B1000E2875 /* iOSTests */,
				D094AA5F2DE940B1000E2875 /* iOSUITests */,
				D094AA3D2DE940AF000E2875 /* Products */,
			);
			sourceTree = "<group>";
		};
		D094AA3D2DE940AF000E2875 /* Products */ = {
			isa = PBXGroup;
			children = (
				D094AA3C2DE940AF000E2875 /* iOS.app */,
				D094AA522DE940B1000E2875 /* iOSTests.xctest */,
				D094AA5C2DE940B1000E2875 /* iOSUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		D094AA3B2DE940AF000E2875 /* iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D094AA652DE940B1000E2875 /* Build configuration list for PBXNativeTarget "iOS" */;
			buildPhases = (
				D094AA382DE940AF000E2875 /* Sources */,
				D094AA392DE940AF000E2875 /* Frameworks */,
				D094AA3A2DE940AF000E2875 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				D094AA3E2DE940AF000E2875 /* iOS */,
			);
			name = iOS;
			packageProductDependencies = (
			);
			productName = iOS;
			productReference = D094AA3C2DE940AF000E2875 /* iOS.app */;
			productType = "com.apple.product-type.application";
		};
		D094AA512DE940B1000E2875 /* iOSTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D094AA6A2DE940B1000E2875 /* Build configuration list for PBXNativeTarget "iOSTests" */;
			buildPhases = (
				D094AA4E2DE940B1000E2875 /* Sources */,
				D094AA4F2DE940B1000E2875 /* Frameworks */,
				D094AA502DE940B1000E2875 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				D094AA542DE940B1000E2875 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				D094AA552DE940B1000E2875 /* iOSTests */,
			);
			name = iOSTests;
			packageProductDependencies = (
			);
			productName = iOSTests;
			productReference = D094AA522DE940B1000E2875 /* iOSTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		D094AA5B2DE940B1000E2875 /* iOSUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D094AA6D2DE940B1000E2875 /* Build configuration list for PBXNativeTarget "iOSUITests" */;
			buildPhases = (
				D094AA582DE940B1000E2875 /* Sources */,
				D094AA592DE940B1000E2875 /* Frameworks */,
				D094AA5A2DE940B1000E2875 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				D094AA5E2DE940B1000E2875 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				D094AA5F2DE940B1000E2875 /* iOSUITests */,
			);
			name = iOSUITests;
			packageProductDependencies = (
			);
			productName = iOSUITests;
			productReference = D094AA5C2DE940B1000E2875 /* iOSUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		D094AA342DE940AF000E2875 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					D094AA3B2DE940AF000E2875 = {
						CreatedOnToolsVersion = 16.2;
					};
					D094AA512DE940B1000E2875 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = D094AA3B2DE940AF000E2875;
					};
					D094AA5B2DE940B1000E2875 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = D094AA3B2DE940AF000E2875;
					};
				};
			};
			buildConfigurationList = D094AA372DE940AF000E2875 /* Build configuration list for PBXProject "iOS" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = D094AA332DE940AF000E2875;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = D094AA3D2DE940AF000E2875 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D094AA3B2DE940AF000E2875 /* iOS */,
				D094AA512DE940B1000E2875 /* iOSTests */,
				D094AA5B2DE940B1000E2875 /* iOSUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		D094AA3A2DE940AF000E2875 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D095CEBA2DE9953F00EC5F37 /* PAYMENT_FLOW.md in Resources */,
				D095CEBB2DE9953F00EC5F37 /* README.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D094AA502DE940B1000E2875 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D094AA5A2DE940B1000E2875 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		D094AA382DE940AF000E2875 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D094AA4E2DE940B1000E2875 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D094AA582DE940B1000E2875 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		D094AA542DE940B1000E2875 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D094AA3B2DE940AF000E2875 /* iOS */;
			targetProxy = D094AA532DE940B1000E2875 /* PBXContainerItemProxy */;
		};
		D094AA5E2DE940B1000E2875 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D094AA3B2DE940AF000E2875 /* iOS */;
			targetProxy = D094AA5D2DE940B1000E2875 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		D094AA662DE940B1000E2875 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N7W4W279C5;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iOS/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = liwei.iOS;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D094AA672DE940B1000E2875 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N7W4W279C5;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = iOS/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = liwei.iOS;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		D094AA682DE940B1000E2875 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		D094AA692DE940B1000E2875 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D094AA6B2DE940B1000E2875 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N7W4W279C5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = liwei.iOSTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOS.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOS";
			};
			name = Debug;
		};
		D094AA6C2DE940B1000E2875 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N7W4W279C5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = liwei.iOSTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOS.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/iOS";
			};
			name = Release;
		};
		D094AA6E2DE940B1000E2875 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N7W4W279C5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = liwei.iOSUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOS;
			};
			name = Debug;
		};
		D094AA6F2DE940B1000E2875 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N7W4W279C5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = liwei.iOSUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = iOS;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		D094AA372DE940AF000E2875 /* Build configuration list for PBXProject "iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D094AA682DE940B1000E2875 /* Debug */,
				D094AA692DE940B1000E2875 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D094AA652DE940B1000E2875 /* Build configuration list for PBXNativeTarget "iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D094AA662DE940B1000E2875 /* Debug */,
				D094AA672DE940B1000E2875 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D094AA6A2DE940B1000E2875 /* Build configuration list for PBXNativeTarget "iOSTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D094AA6B2DE940B1000E2875 /* Debug */,
				D094AA6C2DE940B1000E2875 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D094AA6D2DE940B1000E2875 /* Build configuration list for PBXNativeTarget "iOSUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D094AA6E2DE940B1000E2875 /* Debug */,
				D094AA6F2DE940B1000E2875 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = D094AA342DE940AF000E2875 /* Project object */;
}
