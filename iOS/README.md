# 巴基斯坦支付集成指南

本项目提供了巴基斯坦两大支付平台 JazzCash 和 Easypaisa 的集成示例，包括网页支付和 API 支付两种方式。

## 目录

- [概述](#概述)
- [JazzCash 集成](#jazzcash-集成)
  - [网页支付](#jazzcash-网页支付)
  - [API 支付](#jazzcash-api-支付)
- [Easypaisa 集成](#easypaisa-集成)
  - [网页支付](#easypaisa-网页支付)
  - [API 支付](#easypaisa-api-支付)
- [使用说明](#使用说明)
- [常见问题](#常见问题)

## 概述

巴基斯坦的移动支付市场主要由 JazzCash 和 Easypaisa 两大平台主导。本项目提供了这两个平台的完整集成示例，包括：

1. 网页支付：通过 WebView 加载支付网关页面完成支付
2. API 支付：通过 API 接口直接发起支付请求

## JazzCash 集成

### JazzCash 网页支付

JazzCash 网页支付通过 `PaymentViewController` 类实现，主要流程如下：

1. 生成交易参数（交易参考号、交易时间、过期时间等）
2. 使用 `PaymentUtils.JazzCash` 工具类准备支付请求数据
3. 构建 POST 表单 HTML 并加载到 WebView 中
4. 处理支付回调

关键代码：

```swift
// 准备支付请求数据
let paymentData = PaymentUtils.JazzCash.preparePostData(
    amount: amount,
    billReference: billReference,
    description: description,
    txnDateTime: txnDateTime,
    txnExpiryDateTime: txnExpiryDateTime,
    txnRefNo: txnRefNo
)

// 构建POST表单HTML并加载到WebView
let formHtml = buildPostForm(with: paymentData)
webView.loadHTMLString(formHtml, baseURL: nil)
```

### JazzCash API 支付

JazzCash API 支付通过 `ApiPaymentViewController` 类实现，主要流程如下：

1. 收集用户的手机号和身份证号
2. 将支付信息发送到后端服务器
3. 后端服务器调用 JazzCash API 发起支付请求
4. 用户在手机上完成支付
5. 查询支付状态

关键代码：

```swift
// 发送支付请求到后端服务器
let requestBody: [String: Any] = [
    "mobileNumber": mobileNumber,
    "cnic": cnic,
    "amount": amount,
    "description": description
]

// 处理支付结果
if success {
    // 支付请求已发送，通知用户查看手机
    showPaymentInstructionsDialog()
} else {
    // 支付请求失败
    showError(title: "支付请求失败", message: errorMessage)
}
```

## Easypaisa 集成

### Easypaisa 网页支付

Easypaisa 网页支付通过 `EasypaisaViewController` 类实现，主要流程如下：

1. 使用 `PaymentUtils.Easypaisa` 工具类准备支付请求数据
2. 构建 POST 表单 HTML 并加载到 WebView 中
3. 处理支付回调

关键代码：

```swift
// 准备Easypaisa支付请求数据
let paymentData = PaymentUtils.Easypaisa.preparePaymentData(
    amount: amount,
    description: descriptionText,
    mobileNumber: mobileNumber,
    email: email
)

// 构建POST表单HTML并加载到WebView
let formHtml = buildPostForm(with: paymentData)
webView.loadHTMLString(formHtml, baseURL: nil)
```

### Easypaisa API 支付

Easypaisa API 支付通过 `EasypaisaApiPaymentViewController` 类实现，主要流程如下：

1. 收集用户的手机号和身份证号
2. 将支付信息发送到后端服务器
3. 后端服务器调用 Easypaisa API 发起支付请求
4. 用户在手机上完成支付
5. 查询支付状态

关键代码：

```swift
// 发送支付请求到后端服务器
let requestBody: [String: Any] = [
    "mobileNumber": mobileNumber,
    "cnic": cnic,
    "amount": amount,
    "description": description
]

// 处理支付结果
if success {
    // 支付请求已发送，通知用户查看手机
    showPaymentInstructionsDialog()
} else {
    // 支付请求失败
    showError(title: "支付请求失败", message: errorMessage)
}
```

## 使用说明

### 配置

在 `PaymentUtils.swift` 文件中配置支付参数：

```swift
// JazzCash 配置
private static let INTEGRITY_SALT = "xxx" // 从 JazzCash 获取的盐值
private static let MERCHANT_ID = "xxx" // 从 JazzCash 获取的商户 ID
private static let PASSWORD = "xxx" // 从 JazzCash 获取的密码
private static let RETURN_URL = "http://your-server.com/jazzcash/callback" // 回调 URL

// Easypaisa 配置
private static let MERCHANT_ID = "xxx" // 从 Easypaisa 获取的商户 ID
private static let MERCHANT_KEY = "xxx" // 从 Easypaisa 获取的商户密钥
private static let RETURN_URL = "http://your-server.com/easypaisa/callback" // 回调 URL
```

### 后端服务器

本项目需要配合后端服务器使用，后端服务器负责：

1. 接收移动端发送的支付请求
2. 调用支付平台的 API 发起支付
3. 接收支付平台的回调通知
4. 提供支付状态查询接口

## 常见问题

### 1. 支付页面加载失败

可能原因：
- 网络连接问题
- 支付参数配置错误
- 支付平台服务器问题

解决方案：
- 检查网络连接
- 确认支付参数配置正确
- 联系支付平台客服

### 2. 支付请求发送成功但用户未收到支付通知

可能原因：
- 手机号输入错误
- 用户手机未安装支付应用
- 支付平台服务器延迟

解决方案：
- 确认手机号正确
- 指导用户安装支付应用
- 等待几分钟后重新查询支付状态

### 3. 支付状态查询失败

可能原因：
- 网络连接问题
- 交易参考号错误
- 后端服务器问题

解决方案：
- 检查网络连接
- 确认交易参考号正确
- 联系后端服务器管理员

## 注意事项

1. 本项目中的支付参数仅用于测试环境，生产环境需要替换为正式参数
2. 支付成功后应立即验证支付结果，避免伪造支付成功信息
3. 支付过程中应做好错误处理和超时处理
4. 用户体验方面，应提供清晰的支付指引和状态反馈

## 功能特点

- 支持JazzCash页面重定向支付流程（适用于银行卡支付）
- 支持JazzCash API支付流程（适用于钱包支付）
- 支持Easypaisa页面重定向支付流程（适用于银行卡支付）
- 支持Easypaisa API支付流程（适用于钱包支付）
- 包含安全哈希生成工具
- 支持支付结果回调处理
- 支持交易状态查询
- 用户友好的错误处理和提示
- 简洁现代的UI设计

## 项目结构

- `AppDelegate.swift` - 应用程序入口
- `MainViewController.swift` - 主界面，提供支付渠道和支付方式选择
- `PaymentViewController.swift` - JazzCash页面重定向支付实现
- `ApiPaymentViewController.swift` - JazzCash API支付实现
- `EasypaisaViewController.swift` - Easypaisa页面重定向支付实现
- `EasypaisaApiViewController.swift` - Easypaisa API支付实现
- `PaymentUtils.swift` - 支付工具类，处理安全哈希生成等功能
  - `PaymentUtils.JazzCash` - JazzCash特定的工具方法
  - `PaymentUtils.Easypaisa` - Easypaisa特定的工具方法

## 支付流程

详细的支付流程请参阅 [PAYMENT_FLOW.md](PAYMENT_FLOW.md) 文档，其中包含了各种支付渠道和支付方式的完整流程图和说明。

### 页面重定向支付流程（通用）

1. 用户选择支付渠道（JazzCash或Easypaisa）
2. 用户输入金额和描述
3. 用户选择"页面重定向支付"
4. 系统生成交易参考号和支付请求数据
5. 使用WebView加载支付页面
6. 用户在页面上输入银行卡信息并确认支付
7. 支付网关处理支付并重定向到回调URL
8. 应用解析回调数据并显示支付结果

### API支付流程（通用）

1. 用户选择支付渠道（JazzCash或Easypaisa）
2. 用户输入金额和描述
3. 用户选择"API支付"
4. 用户输入钱包关联的手机号
5. 系统将请求发送到服务器端
6. 服务器与支付网关API通信并返回结果
7. 用户在手机上的支付应用中确认支付
8. 应用查询支付状态并显示结果

## 安全考虑

- 商户凭据（密码、密钥等）不应在客户端存储
- API支付方式更安全，因为敏感信息仅在服务器端处理
- 所有请求均使用HTTPS加密传输
- 实现了HMAC-SHA256安全哈希验证机制
- 每个交易生成唯一的交易ID，防止重放攻击
- 详细的安全措施请参阅 [PAYMENT_FLOW.md](PAYMENT_FLOW.md) 文档中的"安全措施"部分

## 测试与开发

- 该项目使用测试环境进行开发和测试
- JazzCash沙箱环境URL: `https://sandbox.jazzcash.com.pk/`
- Easypaisa测试环境URL: `https://easypaisa.sandbox.com.pk/payment`
- 需要向相应的支付服务提供商申请测试凭据

## 系统要求

- iOS 13.0+
- Xcode 13.0+
- Swift 5.0+

## 安装说明

1. 克隆仓库
2. 打开`PaymentiOS.xcodeproj`
3. 更新`PaymentUtils.swift`中的支付凭据（商户ID、密码、安全盐值等）
4. 构建并运行项目 
