@preconcurrency
import UIKit
import WebKit

/**
 * Easypaisa支付视图控制器
 * 
 * 该类负责：
 * 1. 接收支付参数
 * 2. 准备支付请求
 * 3. 加载Easypaisa支付页面
 * 4. 处理支付回调
 */
class EasypaisaViewController: UIViewController {
    
    // MARK: - UI 元素
    
    private lazy var webView: WKWebView = {
        let preferences = WKWebpagePreferences()
        preferences.allowsContentJavaScript = true
        
        let configuration = WKWebViewConfiguration()
        configuration.defaultWebpagePreferences = preferences
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.translatesAutoresizingMaskIntoConstraints = false
        webView.navigationDelegate = self
        return webView
    }()
    
    private lazy var progressView: UIProgressView = {
        let progressView = UIProgressView(progressViewStyle: .default)
        progressView.translatesAutoresizingMaskIntoConstraints = false
        progressView.progressTintColor = UIColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 1.0) // 绿色，Easypaisa的品牌色
        return progressView
    }()
    
    private lazy var activityIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .large)
        indicator.translatesAutoresizingMaskIntoConstraints = false
        indicator.hidesWhenStopped = true
        indicator.color = UIColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 1.0) // 绿色，Easypaisa的品牌色
        return indicator
    }()
    
    private lazy var errorView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        view.isHidden = true
        return view
    }()
    
    private lazy var errorLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "加载支付页面失败"
        label.textAlignment = .center
        label.numberOfLines = 0
        label.font = UIFont.systemFont(ofSize: 16)
        return label
    }()
    
    private lazy var retryButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("重试", for: .normal)
        button.backgroundColor = UIColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 1.0) // 绿色，Easypaisa的品牌色
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(retryPayment), for: .touchUpInside)
        return button
    }()
    
    // MARK: - 属性
    
    var amount: String = ""
    var descriptionText: String = ""
    var mobileNumber: String = ""
    var email: String = ""
    
    private var txnRefNo: String = ""
    private var progressObserver: NSKeyValueObservation?
    private var loadingTimer: Timer?
    private var paymentData: [String: String] = [:]
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        title = "Easypaisa 支付"
        view.backgroundColor = .white
        
        setupUI()
        setupObservers()
        initiatePayment()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        // 清除计时器和观察者
        loadingTimer?.invalidate()
        loadingTimer = nil
        progressObserver?.invalidate()
    }
    
    deinit {
        progressObserver?.invalidate()
        loadingTimer?.invalidate()
    }
    
    // MARK: - 私有方法
    
    private func setupUI() {
        view.addSubview(webView)
        view.addSubview(progressView)
        view.addSubview(activityIndicator)
        view.addSubview(errorView)
        
        errorView.addSubview(errorLabel)
        errorView.addSubview(retryButton)
        
        NSLayoutConstraint.activate([
            progressView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            progressView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            progressView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            progressView.heightAnchor.constraint(equalToConstant: 2),
            
            webView.topAnchor.constraint(equalTo: progressView.bottomAnchor),
            webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            activityIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            
            errorView.topAnchor.constraint(equalTo: progressView.bottomAnchor),
            errorView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            errorView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            errorView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            errorLabel.centerXAnchor.constraint(equalTo: errorView.centerXAnchor),
            errorLabel.centerYAnchor.constraint(equalTo: errorView.centerYAnchor, constant: -40),
            errorLabel.leadingAnchor.constraint(equalTo: errorView.leadingAnchor, constant: 20),
            errorLabel.trailingAnchor.constraint(equalTo: errorView.trailingAnchor, constant: -20),
            
            retryButton.topAnchor.constraint(equalTo: errorLabel.bottomAnchor, constant: 20),
            retryButton.centerXAnchor.constraint(equalTo: errorView.centerXAnchor),
            retryButton.widthAnchor.constraint(equalToConstant: 120),
            retryButton.heightAnchor.constraint(equalToConstant: 44)
        ])
        
        // 添加取消按钮
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            title: "取消",
            style: .plain,
            target: self,
            action: #selector(cancelPayment)
        )
    }
    
    private func setupObservers() {
        // 观察WebView加载进度
        progressObserver = webView.observe(\.estimatedProgress, options: [.new]) { [weak self] _, change in
            guard let self = self, let newValue = change.newValue else { return }
            self.progressView.progress = Float(newValue)
            self.progressView.isHidden = newValue == 1
        }
    }
    
    private func initiatePayment() {
        activityIndicator.startAnimating()
        
        // 准备Easypaisa支付请求数据
        paymentData = PaymentUtils.Easypaisa.preparePaymentData(
            amount: amount,
            description: descriptionText,
            mobileNumber: mobileNumber,
            email: email
        )
        
        // 保存交易参考号
        txnRefNo = paymentData["orderId"] ?? "EP\(Int(Date().timeIntervalSince1970))"
        
        // 构建POST表单HTML
        let formHtml = buildPostForm(with: paymentData)
        
        // 加载表单到WebView
        webView.loadHTMLString(formHtml, baseURL: nil)
        
        // 设置加载超时
        startLoadingTimer()
    }
    
    private func buildPostForm(with data: [String: String]) -> String {
        let paymentUrl = PaymentUtils.Easypaisa.PAYMENT_URL_TEST
        
        var formFields = ""
        for (key, value) in data {
            let escapedValue = value.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? value
            formFields += "<input type=\"hidden\" name=\"\(key)\" value=\"\(escapedValue)\">\n"
        }
        
        let html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Easypaisa Payment</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
                .loader { border: 5px solid #f3f3f3; border-radius: 50%; border-top: 5px solid #007500; width: 50px; height: 50px; animation: spin 1s linear infinite; margin: 0 auto; }
                @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                p { margin-top: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class="loader"></div>
            <p>正在跳转到Easypaisa支付页面，请稍候...</p>
            <form id="paymentForm" action="\(paymentUrl)" method="post">
                \(formFields)
            </form>
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        document.getElementById('paymentForm').submit();
                    }, 1000);
                };
            </script>
        </body>
        </html>
        """
        
        return html
    }
    
    private func startLoadingTimer() {
        // 设置60秒超时
        loadingTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: false) { [weak self] _ in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                if self.activityIndicator.isAnimating {
                    self.showLoadingError(message: "加载支付页面超时，请检查您的网络连接后重试。")
                }
            }
        }
    }
    
    private func showLoadingError(message: String) {
        activityIndicator.stopAnimating()
        webView.isHidden = true
        errorView.isHidden = false
        errorLabel.text = message
    }
    
    @objc private func retryPayment() {
        errorView.isHidden = true
        webView.isHidden = false
        activityIndicator.startAnimating()
        
        // 重新加载表单
        let formHtml = buildPostForm(with: paymentData)
        webView.loadHTMLString(formHtml, baseURL: nil)
        
        // 重新设置加载超时
        loadingTimer?.invalidate()
        startLoadingTimer()
    }
    
    @objc private func cancelPayment() {
        // 发送取消支付通知
        let userInfo: [String: Any] = [
            "success": false,
            "txnRefNo": txnRefNo,
            "message": "用户取消支付"
        ]
        
        NotificationCenter.default.post(
            name: NSNotification.Name("EasypaisaPaymentResult"),
            object: nil,
            userInfo: userInfo
        )
        
        // 返回上一页
        navigationController?.popViewController(animated: true)
    }
    
    private func handlePaymentResponse(url: URL) {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false) else {
            completePayment(success: false, message: "无效的回调URL")
            return
        }
        
        // 提取查询参数
        let queryItems = components.queryItems ?? []
        var responseData: [String: String] = [:]
        
        for item in queryItems {
            if let value = item.value {
                responseData[item.name] = value
            }
        }
        
        // 解析支付结果
        if let responseCode = responseData["responseCode"], responseCode == "0000" {
            // 支付成功
            completePayment(
                success: true,
                message: responseData["responseDesc"] ?? "支付成功",
                responseData: responseData
            )
        } else {
            // 支付失败
            completePayment(
                success: false,
                message: responseData["responseDesc"] ?? "支付失败",
                responseData: responseData
            )
        }
    }
    
    private func completePayment(success: Bool, message: String, responseData: [String: String] = [:]) {
        // 构建通知数据
        var userInfo: [String: Any] = [
            "success": success,
            "txnRefNo": txnRefNo,
            "message": message
        ]
        
        // 添加响应数据
        for (key, value) in responseData {
            userInfo[key] = value
        }
        
        // 发送支付结果通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EasypaisaPaymentResult"),
            object: nil,
            userInfo: userInfo
        )
        
        // 返回上一页
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - WKNavigationDelegate

extension EasypaisaViewController: WKNavigationDelegate {
    
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        activityIndicator.startAnimating()
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        activityIndicator.stopAnimating()
        
        // 停止加载计时器
        loadingTimer?.invalidate()
        loadingTimer = nil
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        activityIndicator.stopAnimating()
        
        // 停止加载计时器
        loadingTimer?.invalidate()
        loadingTimer = nil
        
        // 显示错误信息
        showLoadingError(message: "加载支付页面失败: \(error.localizedDescription)")
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        activityIndicator.stopAnimating()
        
        // 停止加载计时器
        loadingTimer?.invalidate()
        loadingTimer = nil
        
        // 显示错误信息
        if (error as NSError).code == NSURLErrorNotConnectedToInternet {
            showLoadingError(message: "网络连接失败，请检查您的网络设置后重试。")
        } else {
            showLoadingError(message: "加载支付页面失败: \(error.localizedDescription)")
        }
    }
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        // 检查URL是否是回调URL
        if let url = navigationAction.request.url,
           url.absoluteString.contains("easypaisa/callback") {
            // 处理回调
            handlePaymentResponse(url: url)
            decisionHandler(.cancel)
            return
        }
        
        decisionHandler(.allow)
    }
}

// MARK: - CommonCrypto Import
import CommonCrypto

// 添加必要的CommonCrypto常量
private let CC_SHA256_DIGEST_LENGTH = 32 