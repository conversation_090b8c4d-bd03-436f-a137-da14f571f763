import Foundation
import CommonCrypto

/**
 * 支付工具类
 * 
 * 包含 JazzCash 和 Easypaisa 支付的工具方法
 */
class PaymentUtils {
    
    // MARK: - JazzCash 支付工具
    
    class JazzCash {
        // MARK: - 常量
        
        /// JazzCash 集成参数，需要从 JazzCash 获取
        private static let INTEGRITY_SALT = "8zzx29y512" // 从 JazzCash 获取的测试盐值
        private static let MERCHANT_ID = "MC158532" // 从 JazzCash 获取的测试商户 ID
        private static let PASSWORD = "0b64sxf81t" // 从 JazzCash 获取的测试密码
        
        /// 支付网关URL
        static let PAYMENT_URL = "https://sandbox.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform"
        
        /// 回调 URL，JazzCash 将在支付完成后重定向到此 URL
        private static let RETURN_URL = "https://app-api.eswap.com/swap/pay/jazzcash/notify" // 你的回调 URL
        
        // MARK: - 公共方法
        
        /**
         * 准备支付请求数据
         *
         * @param amount 金额（以分为单位）
         * @param billReference 账单参考号
         * @param description 交易描述
         * @param txnDateTime 交易时间
         * @param txnExpiryDateTime 交易过期时间
         * @param txnRefNo 交易参考号
         * @return 格式化的请求数据字典
         */
        static func preparePostData(
            amount: String,
            billReference: String,
            description: String,
            txnDateTime: String,
            txnExpiryDateTime: String,
            txnRefNo: String
        ) -> [String: String] {
            
            print("准备JazzCash支付请求数据 - 开始")
            print("金额: \(amount), 账单参考号: \(billReference), 描述: \(description)")
            print("交易时间: \(txnDateTime), 过期时间: \(txnExpiryDateTime), 参考号: \(txnRefNo)")
            print("使用回调 URL: \(RETURN_URL)")
            
            // 准备支付请求数据
            var requestData: [String: String] = [
                "pp_Amount": amount,               // 交易金额
                "pp_BillReference": billReference, // 账单参考号，用于识别账单
                "pp_Description": description,     // 交易描述
                "pp_Language": "EN",               // 语言设置
                "pp_MerchantID": MERCHANT_ID,      // 商户 ID
                "pp_Password": PASSWORD,           // 商户密码
                "pp_ReturnURL": RETURN_URL,        // 支付完成后的回调 URL
                "pp_TxnCurrency": "PKR",           // 交易货币，PKR 为巴基斯坦卢比
                "pp_TxnDateTime": txnDateTime,     // 交易时间
                "pp_TxnExpiryDateTime": txnExpiryDateTime, // 交易过期时间
                "pp_TxnRefNo": txnRefNo,           // 交易参考号，必须唯一
                "pp_Version": "1.1",               // API 版本
                "pp_BankID": "TBANK",              // 银行 ID
                "pp_ProductID": "RETL",            // 产品 ID
                "ppmpf_1": "1",                    // 商户自定义字段 1
                "ppmpf_2": "2",                    // 商户自定义字段 2
                "ppmpf_3": "3",                    // 商户自定义字段 3
                "ppmpf_4": "4",                    // 商户自定义字段 4
                "ppmpf_5": "5"                     // 商户自定义字段 5
            ]
            
            // 生成安全哈希
            let secureHash = generateSecureHash(data: requestData)
            requestData["pp_SecureHash"] = secureHash
            print("生成的安全哈希: \(secureHash)")
            
            print("准备JazzCash支付请求数据 - 完成")
            
            return requestData
        }
        
        /**
         * 解析支付响应
         *
         * @param response 支付响应字符串
         * @return 解析后的响应数据字典
         */
        static func parsePaymentResponse(response: String?) -> [String: String] {
                    var resultMap: [String: String] = [:]
                    
                    guard let response = response, !response.isEmpty else {
                        print("支付响应为空")
                        return resultMap
                    }
                    
                    print("解析JazzCash支付响应: \(response)")
                    
                    // 转换为 Data 对象
                    guard let jsonData = response.data(using: .utf8) else {
                        print("Error: Couldn't convert string to data")
                        return resultMap
                    }
                    
                    // 解析 JSON
                    var jsonObject:[String:Any] = [:]
                    do {
                        jsonObject = try JSONSerialization.jsonObject(with: jsonData, options: []) as! [String:Any]
                    } catch {
                        return resultMap
                    }
                    
                    
                    // 检查类型是否为字典
                    guard let rootDictionary = jsonObject as? [String: Any] else {
                        print("Error: Root object is not a dictionary")
                        return resultMap
                    }
                    
                    print("✅ 完整字典结构:")
                    dump(rootDictionary) // 打印完整结构
                    
                    resultMap = rootDictionary["data"] as! [String : String]
                    
                    // 检查支付状态
                    if let responseCode = resultMap["pp_ResponseCode"], responseCode == "000" {
                        print("JazzCash支付成功！交易 ID: \(resultMap["pp_TxnRefNo"] ?? "")")
                    } else {
                        print("JazzCash支付失败，错误码: \(resultMap["pp_ResponseCode"] ?? "未知"), 消息: \(resultMap["pp_ResponseMessage"] ?? "未知")")
                    }
                    
                    return resultMap
                }

        
        /**
         * 生成交易参考号
         *
         * @return 唯一交易参考号
         */
        static func generateTxnRefNo() -> String {
            // 格式: JC + 时间戳 + 4位随机数
            let timestamp = Int(Date().timeIntervalSince1970 * 1000)
            let randomNum = Int.random(in: 0..<10000)
            return "JC\(timestamp)\(String(format: "%04d", randomNum))"
        }
        
        /**
         * 生成交易日期时间
         *
         * @return 当前日期时间，格式：yyyyMMddHHmmss
         */
        static func generateTxnDateTime() -> String {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyyMMddHHmmss"
            return dateFormatter.string(from: Date())
        }
        
        /**
         * 生成交易过期日期时间（当前时间 + 1小时）
         *
         * @return 过期日期时间，格式：yyyyMMddHHmmss
         */
        static func generateExpiryDateTime() -> String {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyyMMddHHmmss"
            let expiryDate = Calendar.current.date(byAdding: .hour, value: 1, to: Date())!
            return dateFormatter.string(from: expiryDate)
        }
        
        // MARK: - 私有方法
        
        /**
         * 生成安全哈希
         *
         * 安全哈希是通过对排序后的请求参数值进行 HMAC-SHA256 哈希计算得到的。
         * 这个哈希值用于验证请求的完整性和真实性。
         *
         * @param data 要哈希的数据
         * @return 安全哈希字符串
         */
        private static func generateSecureHash(data: [String: String]) -> String {
            do {
                print("生成JazzCash安全哈希 - 开始")
                
                // 创建一个新的Map，不包含pp_SecureHash字段
                let dataForHash = data.filter { $0.key != "pp_SecureHash" }
                
                // 按字母顺序排序参数 (Android版本也是这样做的)
                let sortedKeys = dataForHash.keys.sorted()
                print("排序后的数据键: \(sortedKeys)")
                
                // 构建要哈希的字符串 - 与Android版本完全一致
                var hashStringBuilder = INTEGRITY_SALT
                
                // 先添加盐值，然后添加所有值，用&连接
                for key in sortedKeys {
                    if let value = dataForHash[key], !value.isEmpty {
                        hashStringBuilder += "&\(value)"
                    }
                }
                
                print("要哈希的字符串: \(hashStringBuilder)")
                
                // 使用 HMAC-SHA256 进行哈希
                guard let saltData = INTEGRITY_SALT.data(using: .utf8),
                      let dataToHash = hashStringBuilder.data(using: .utf8) else {
                    print("字符串转换为数据失败")
                    return ""
                }
                
                let hashBytes = hmacSHA256(data: dataToHash, key: saltData)
                let hashHex = hashBytes.map { String(format: "%02hhx", $0) }.joined()
                
                print("生成的哈希: \(hashHex)")
                print("生成JazzCash安全哈希 - 完成")
                
                return hashHex
            } catch {
                print("生成JazzCash安全哈希时出错: \(error)")
                return ""
            }
        }
    }
    
    // MARK: - Easypaisa 支付工具
    
    class Easypaisa {
        // MARK: - 常量
        
        /// Easypaisa 集成参数
        private static let MERCHANT_ID = "EP12345" // 从 Easypaisa 获取的测试商户 ID
        private static let MERCHANT_KEY = "easypaisa_key_123" // 从 Easypaisa 获取的测试商户密钥
        
        /// 支付网关URL
        static let PAYMENT_URL_TEST = "https://easypaisa.sandbox.com/payment"
        static let PAYMENT_URL_PROD = "https://easypaisa.com.pk/payment"
        
        /// 回调 URL
        private static let RETURN_URL = "http://************:8080/easypaisa/callback"
        
        // MARK: - 公共方法
        
        /**
         * 准备 Easypaisa 支付请求数据
         *
         * @param amount 金额
         * @param description 交易描述
         * @param mobileNumber 用户手机号码
         * @param email 用户电子邮件
         * @return 支付请求数据字典
         */
        static func preparePaymentData(
            amount: String,
            description: String,
            mobileNumber: String,
            email: String
        ) -> [String: String] {
            print("准备Easypaisa支付请求数据 - 开始")
            
            // 生成唯一订单号
            let orderId = "EP\(Int(Date().timeIntervalSince1970 * 1000))"
            
            // 准备支付请求数据
            var requestData: [String: String] = [
                "merchantId": MERCHANT_ID,
                "storeId": "1",
                "orderId": orderId,
                "transactionAmount": amount,
                "mobileAccountNo": mobileNumber,
                "emailAddress": email,
                "transactionType": "MA", // MA = Mobile Account
                "tokenExpiry": "********",
                "bankIdentificationNumber": "0000",
                "merchantPaymentMethod": "CC_PAYMENT_METHOD",
                "postBackURL": RETURN_URL,
                "description": description
            ]
            
            // 生成安全哈希
            let secureHash = generateEasypaisaHash(data: requestData)
            requestData["merchantHashedReq"] = secureHash
            
            print("Easypaisa支付请求数据准备完成: \(requestData)")
            return requestData
        }
        
        /**
         * 解析 Easypaisa 支付响应
         *
         * @param response 支付响应字符串
         * @return 解析后的响应数据字典
         */
        static func parsePaymentResponse(response: String?) -> [String: String] {
            var resultMap: [String: String] = [:]
            
            guard let response = response, !response.isEmpty else {
                print("Easypaisa支付响应为空")
                return resultMap
            }
            
            print("解析Easypaisa支付响应: \(response)")
            
            do {
                // 假设响应是查询字符串格式
                let pairs = response.components(separatedBy: "&")
                for pair in pairs {
                    let keyValue = pair.components(separatedBy: "=")
                    if keyValue.count == 2 {
                        let key = keyValue[0]
                        let value = keyValue[1]
                        resultMap[key] = value
                    }
                }
                
                // 检查支付状态
                if let responseCode = resultMap["responseCode"], responseCode == "0000" {
                    print("Easypaisa支付成功！交易 ID: \(resultMap["transactionId"] ?? "")")
                } else {
                    print("Easypaisa支付失败，错误码: \(resultMap["responseCode"] ?? "未知"), 消息: \(resultMap["responseDesc"] ?? "未知")")
                }
            } catch {
                print("解析Easypaisa响应时出错: \(error)")
            }
            
            return resultMap
        }
        
        /**
         * 生成 Easypaisa 安全哈希
         *
         * @param data 要哈希的数据
         * @return 安全哈希字符串
         */
        private static func generateEasypaisaHash(data: [String: String]) -> String {
            do {
                print("生成Easypaisa安全哈希 - 开始")
                
                // 构建要哈希的字符串
                let valueString = "\(data["merchantId"] ?? "")\(data["storeId"] ?? "")\(data["orderId"] ?? "")\(data["transactionAmount"] ?? "")\(data["mobileAccountNo"] ?? "")"
                
                // 使用 HMAC-SHA256 进行哈希
                guard let keyData = MERCHANT_KEY.data(using: .utf8),
                      let dataToHash = valueString.data(using: .utf8) else {
                    print("字符串转换为数据失败")
                    return ""
                }
                
                let digestLength = Int(CC_SHA256_DIGEST_LENGTH)
                var macData = Data(count: digestLength)
                
                dataToHash.withUnsafeBytes { dataBytes in
                    keyData.withUnsafeBytes { keyBytes in
                        macData.withUnsafeMutableBytes { macBytes in
                            CCHmac(
                                CCHmacAlgorithm(kCCHmacAlgSHA256),
                                keyBytes.baseAddress,
                                keyData.count,
                                dataBytes.baseAddress,
                                dataToHash.count,
                                macBytes.baseAddress
                            )
                        }
                    }
                }
                
                let hashHex = macData.map { String(format: "%02hhx", $0) }.joined()
                print("生成的Easypaisa哈希: \(hashHex)")
                
                return hashHex
            } catch {
                print("生成Easypaisa安全哈希时出错: \(error)")
                return ""
            }
        }
    }
    
    // MARK: - 通用工具方法
    
    /**
     * 计算HMAC-SHA256
     *
     * @param data 要哈希的数据
     * @param key 哈希密钥
     * @return 哈希结果
     */
    private static func hmacSHA256(data: Data, key: Data) -> Data {
        // 为了与Android版本保持一致，先打印数据
        print("进行HMAC-SHA256计算: 数据长度=\(data.count)字节, 密钥长度=\(key.count)字节")
        
        let digestLength = Int(CC_SHA256_DIGEST_LENGTH)
        var macData = Data(count: digestLength)
        
        data.withUnsafeBytes { dataBytes in
            key.withUnsafeBytes { keyBytes in
                macData.withUnsafeMutableBytes { macBytes in
                    CCHmac(
                        CCHmacAlgorithm(kCCHmacAlgSHA256),
                        keyBytes.baseAddress,
                        key.count,
                        dataBytes.baseAddress,
                        data.count,
                        macBytes.baseAddress
                    )
                }
            }
        }
        
        return macData
    }
} 


































