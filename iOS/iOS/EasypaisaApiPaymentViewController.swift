import UIKit

/**
 * Easypaisa API支付视图控制器
 * 
 * 该类负责：
 * 1. 收集用户手机号和CNIC（巴基斯坦身份证号码）
 * 2. 将支付请求发送到后端服务器
 * 3. 显示支付结果
 */
class EasypaisaApiPaymentViewController: UIViewController {
    
    // MARK: - 常量
    
    private let SERVER_API_URL = "http://************:8080/easypaisa/api/create-payment"
    private let SERVER_STATUS_URL = "http://************:8080/easypaisa/api/transaction/"
    
    // MARK: - UI 元素
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "Easypaisa 钱包支付"
        label.font = UIFont.boldSystemFont(ofSize: 24)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var instructionsLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "请输入您的Easypaisa钱包账户信息"
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .left
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var mobileNumberTextField: UITextField = {
        let textField = UITextField()
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.placeholder = "手机号码（11位）"
        textField.borderStyle = .roundedRect
        textField.keyboardType = .phonePad
        return textField
    }()
    
    private lazy var cnicTextField: UITextField = {
        let textField = UITextField()
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.placeholder = "身份证号码（13位）"
        textField.borderStyle = .roundedRect
        textField.keyboardType = .numberPad
        return textField
    }()
    
    private lazy var amountTextField: UITextField = {
        let textField = UITextField()
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.placeholder = "支付金额（PKR）"
        textField.borderStyle = .roundedRect
        textField.keyboardType = .decimalPad
        return textField
    }()
    
    private lazy var descriptionTextField: UITextField = {
        let textField = UITextField()
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.placeholder = "交易描述"
        textField.borderStyle = .roundedRect
        return textField
    }()
    
    private lazy var submitButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("提交支付", for: .normal)
        button.backgroundColor = UIColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 1.0) // 绿色，Easypaisa的品牌色
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(submitPayment), for: .touchUpInside)
        return button
    }()
    
    private lazy var progressView: UIActivityIndicatorView = {
        let view = UIActivityIndicatorView(style: .large)
        view.translatesAutoresizingMaskIntoConstraints = false
        view.hidesWhenStopped = true
        view.color = UIColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 1.0) // 绿色，Easypaisa的品牌色
        return view
    }()
    
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textAlignment = .center
        label.numberOfLines = 0
        label.font = UIFont.systemFont(ofSize: 16)
        return label
    }()
    
    // MARK: - 属性
    
    var amount: String = ""
    var descriptionText: String = ""
    var txnRefNo: String?
    private var timeoutTimer: Timer?
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        title = "Easypaisa API支付"
        view.backgroundColor = .white
        
        setupUI()
        setupGestures()
        
        // 预填充金额和描述
        amountTextField.text = amount
        descriptionTextField.text = descriptionText
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        // 清除超时计时器
        timeoutTimer?.invalidate()
        timeoutTimer = nil
    }
    
    // MARK: - 私有方法
    
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(titleLabel)
        contentView.addSubview(instructionsLabel)
        contentView.addSubview(mobileNumberTextField)
        contentView.addSubview(cnicTextField)
        contentView.addSubview(amountTextField)
        contentView.addSubview(descriptionTextField)
        contentView.addSubview(submitButton)
        contentView.addSubview(progressView)
        contentView.addSubview(statusLabel)
        
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            
            // 内容视图
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 标题标签
            titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            // 说明标签
            instructionsLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            instructionsLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            instructionsLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            
            // 手机号输入框
            mobileNumberTextField.topAnchor.constraint(equalTo: instructionsLabel.bottomAnchor, constant: 20),
            mobileNumberTextField.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            mobileNumberTextField.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            mobileNumberTextField.heightAnchor.constraint(equalToConstant: 44),
            
            // 身份证号输入框
            cnicTextField.topAnchor.constraint(equalTo: mobileNumberTextField.bottomAnchor, constant: 16),
            cnicTextField.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            cnicTextField.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            cnicTextField.heightAnchor.constraint(equalToConstant: 44),
            
            // 金额输入框
            amountTextField.topAnchor.constraint(equalTo: cnicTextField.bottomAnchor, constant: 16),
            amountTextField.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            amountTextField.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            amountTextField.heightAnchor.constraint(equalToConstant: 44),
            
            // 描述输入框
            descriptionTextField.topAnchor.constraint(equalTo: amountTextField.bottomAnchor, constant: 16),
            descriptionTextField.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            descriptionTextField.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            descriptionTextField.heightAnchor.constraint(equalToConstant: 44),
            
            // 提交按钮
            submitButton.topAnchor.constraint(equalTo: descriptionTextField.bottomAnchor, constant: 24),
            submitButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            submitButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            submitButton.heightAnchor.constraint(equalToConstant: 50),
            
            // 进度指示器
            progressView.topAnchor.constraint(equalTo: submitButton.bottomAnchor, constant: 20),
            progressView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            
            // 状态标签
            statusLabel.topAnchor.constraint(equalTo: progressView.bottomAnchor, constant: 16),
            statusLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            statusLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20)
        ])
    }
    
    private func setupGestures() {
        // 添加点击空白处收起键盘的手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        view.addGestureRecognizer(tapGesture)
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    @objc private func submitPayment() {
        // 验证输入
        guard validateInputs() else { return }
        
        // 显示进度指示器，禁用提交按钮
        progressView.startAnimating()
        submitButton.isEnabled = false
        statusLabel.text = "正在处理支付请求..."
        
        // 获取输入值
        guard let mobileNumber = mobileNumberTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
              let cnic = cnicTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
              let amount = amountTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
              let description = descriptionTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) else {
            return
        }
        
        // 创建请求
        guard let url = URL(string: SERVER_API_URL) else {
            showError(title: "错误", message: "无效的服务器URL")
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 创建请求体
        let requestBody: [String: Any] = [
            "mobileNumber": mobileNumber,
            "cnic": cnic,
            "amount": amount,
            "description": description
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            showError(title: "错误", message: "准备请求数据时出错: \(error.localizedDescription)")
            return
        }
        
        // 设置请求超时
        startTimeoutTimer()
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: request) { [weak self] (data, response, error) in
            guard let self = self else { return }
            
            // 返回主线程更新UI
            DispatchQueue.main.async {
                // 停止超时计时器
                self.timeoutTimer?.invalidate()
                self.timeoutTimer = nil
                
                self.progressView.stopAnimating()
                self.submitButton.isEnabled = true
                
                if let error = error {
                    self.statusLabel.text = "请求失败: \(error.localizedDescription)"
                    self.showError(title: "请求失败", message: error.localizedDescription)
                    return
                }
                
                guard let data = data else {
                    self.statusLabel.text = "没有收到数据"
                    self.showError(title: "错误", message: "没有收到数据")
                    return
                }
                
                // 解析响应
                do {
                    guard let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                        self.statusLabel.text = "无法解析响应数据"
                        self.showError(title: "错误", message: "无法解析响应数据")
                        return
                    }
                    
                    let success = jsonResponse["success"] as? Bool ?? false
                    self.txnRefNo = jsonResponse["txnRefNo"] as? String
                    
                    if success {
                        // 支付请求已发送到Easypaisa，通知用户查看手机
                        self.statusLabel.text = "支付请求已发送，交易参考号: \(self.txnRefNo ?? "")"
                        self.showPaymentInstructionsDialog()
                    } else {
                        // 支付请求失败，优先使用服务器提供的用户友好错误信息
                        let userMessage = jsonResponse["userMessage"] as? String ?? ""
                        let errorMessage = userMessage.isEmpty ? (jsonResponse["error"] as? String ?? "未知错误") : userMessage
                        
                        // 记录原始错误信息用于调试
                        let originalErrorCode = jsonResponse["responseCode"] as? String ?? ""
                        let originalErrorMessage = jsonResponse["responseDesc"] as? String ?? ""
                        print("原始错误信息 - 代码: \(originalErrorCode), 消息: \(originalErrorMessage)")
                        
                        self.statusLabel.text = "支付请求失败: \(errorMessage)"
                        self.showError(title: "支付请求失败", message: errorMessage)
                    }
                } catch {
                    self.statusLabel.text = "解析响应时出错: \(error.localizedDescription)"
                    self.showError(title: "错误", message: "解析响应时出错: \(error.localizedDescription)")
                }
            }
        }
        
        task.resume()
    }
    
    private func startTimeoutTimer() {
        // 设置30秒超时
        timeoutTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: false) { [weak self] _ in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                if self.progressView.isAnimating {
                    self.progressView.stopAnimating()
                    self.submitButton.isEnabled = true
                    self.statusLabel.text = "请求超时，请检查网络连接后重试"
                    self.showError(title: "请求超时", message: "服务器响应超时，请检查网络连接后重试")
                }
            }
        }
    }
    
    private func validateInputs() -> Bool {
        guard let mobileNumber = mobileNumberTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !mobileNumber.isEmpty else {
            showError(title: "输入错误", message: "请输入手机号")
            return false
        }
        
        if mobileNumber.count != 11 {
            showError(title: "输入错误", message: "手机号必须为11位")
            return false
        }
        
        guard let cnic = cnicTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !cnic.isEmpty else {
            showError(title: "输入错误", message: "请输入身份证号")
            return false
        }
        
        if cnic.count != 13 {
            showError(title: "输入错误", message: "身份证号必须为13位")
            return false
        }
        
        guard let amount = amountTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !amount.isEmpty else {
            showError(title: "输入错误", message: "请输入金额")
            return false
        }
        
        if let amountValue = Double(amount), amountValue <= 0 {
            showError(title: "输入错误", message: "金额必须大于0")
            return false
        }
        
        guard let description = descriptionTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !description.isEmpty else {
            showError(title: "输入错误", message: "请输入描述")
            return false
        }
        
        return true
    }
    
    private func showPaymentInstructionsDialog() {
        let alert = UIAlertController(
            title: "支付请求已发送",
            message: "请查看您的手机，按照Easypaisa应用的提示完成支付。\n\n交易参考号: \(txnRefNo ?? "")",
            preferredStyle: .alert
        )
        
        let completeAction = UIAlertAction(title: "支付已完成", style: .default) { [weak self] _ in
            self?.checkTransactionStatus()
        }
        
        let cancelAction = UIAlertAction(title: "取消支付", style: .cancel) { [weak self] _ in
            self?.setFailResult(message: "用户取消支付")
        }
        
        alert.addAction(completeAction)
        alert.addAction(cancelAction)
        
        present(alert, animated: true)
    }
    
    private func checkTransactionStatus() {
        guard let txnRefNo = txnRefNo, !txnRefNo.isEmpty else {
            showError(title: "状态查询失败", message: "交易参考号为空")
            return
        }
        
        progressView.startAnimating()
        statusLabel.text = "正在查询交易状态..."
        
        guard let url = URL(string: SERVER_STATUS_URL + txnRefNo) else {
            showError(title: "错误", message: "无效的状态查询URL")
            return
        }
        
        // 设置请求超时
        startTimeoutTimer()
        
        let task = URLSession.shared.dataTask(with: url) { [weak self] (data, response, error) in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // 停止超时计时器
                self.timeoutTimer?.invalidate()
                self.timeoutTimer = nil
                
                self.progressView.stopAnimating()
                
                if let error = error {
                    self.statusLabel.text = "状态查询失败: \(error.localizedDescription)"
                    self.showConfirmationDialog()
                    return
                }
                
                guard let data = data else {
                    self.statusLabel.text = "没有收到状态数据"
                    self.showConfirmationDialog()
                    return
                }
                
                do {
                    guard let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                        self.showConfirmationDialog()
                        return
                    }
                    
                    let success = jsonResponse["success"] as? Bool ?? false
                    let status = jsonResponse["status"] as? String ?? "PENDING"
                    let message = jsonResponse["message"] as? String ?? ""
                    
                    if success || status == "COMPLETED" {
                        // 支付成功
                        self.showPaymentResult(success: true, message: message.isEmpty ? "支付成功" : message)
                    } else if status == "FAILED" {
                        // 支付失败
                        self.showPaymentResult(success: false, message: message.isEmpty ? "支付失败" : message)
                    } else if status == "NOT_FOUND" {
                        // 找不到交易，可能还没有收到回调
                        self.showConfirmationDialog()
                    } else {
                        // 其他状态，询问用户
                        self.showConfirmationDialog()
                    }
                } catch {
                    self.statusLabel.text = "解析状态响应时出错"
                    self.showConfirmationDialog()
                }
            }
        }
        
        task.resume()
    }
    
    private func showConfirmationDialog() {
        let alert = UIAlertController(
            title: "确认支付状态",
            message: "我们无法确认您的支付状态，您的支付是否成功？\n\n交易参考号: \(txnRefNo ?? "")",
            preferredStyle: .alert
        )
        
        let successAction = UIAlertAction(title: "支付成功", style: .default) { [weak self] _ in
            self?.showPaymentResult(success: true, message: "用户确认支付成功")
        }
        
        let failureAction = UIAlertAction(title: "支付失败", style: .destructive) { [weak self] _ in
            self?.showPaymentResult(success: false, message: "用户确认支付失败")
        }
        
        alert.addAction(successAction)
        alert.addAction(failureAction)
        
        present(alert, animated: true)
    }
    
    private func showPaymentResult(success: Bool, message: String) {
        let title = success ? "支付成功" : "支付失败"
        
        let alert = UIAlertController(
            title: title,
            message: message,
            preferredStyle: .alert
        )
        
        let okAction = UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            self?.setResult(success: success, message: message)
        }
        
        alert.addAction(okAction)
        
        present(alert, animated: true)
    }
    
    private func setResult(success: Bool, message: String) {
        // 创建结果信息
        let resultInfo: [String: Any] = [
            "success": success,
            "message": message,
            "txnRefNo": txnRefNo ?? ""
        ]
        
        // 发送通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EasypaisaApiPaymentResult"),
            object: nil,
            userInfo: resultInfo
        )
        
        // 关闭当前视图控制器
        navigationController?.popViewController(animated: true)
    }
    
    private func setFailResult(message: String) {
        setResult(success: false, message: message)
    }
    
    private func showError(title: String, message: String) {
        let alert = UIAlertController(
            title: title,
            message: message,
            preferredStyle: .alert
        )
        
        let okAction = UIAlertAction(title: "确定", style: .default)
        
        alert.addAction(okAction)
        
        present(alert, animated: true)
    }
} 