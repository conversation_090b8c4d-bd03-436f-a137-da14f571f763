@preconcurrency
import UIKit
import WebKit

/**
 * JazzCash支付视图控制器
 * 
 * 该类负责：
 * 1. 加载 WebView 并发送支付请求到 JazzCash 支付网关
 * 2. 监听支付过程
 * 3. 接收支付响应并返回结果到主视图控制器
 */
class JazzCashViewController: UIViewController {
    
    // MARK: - 常量
    
    // JazzCash 支付网关 URL
    private let PAYMENT_URL = "https://sandbox.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform"
    
    // JazzCash 支付完成后的回调 URL
    private let CALLBACK_URL_PREFIX = "https://app-api.eswap.com/swap/pay/jazzcash/notify"
    
    private let MAX_LOAD_ATTEMPTS = 3
    
    // MARK: - UI 元素
    
    private lazy var webView: WKWebView = {
        let preferences = WKWebpagePreferences()
        preferences.allowsContentJavaScript = true
        
        let configuration = WKWebViewConfiguration()
        configuration.defaultWebpagePreferences = preferences
        
        // 添加允许运行不安全内容的设置
        if #available(iOS 14.0, *) {
            configuration.defaultWebpagePreferences.allowsContentJavaScript = true
        }
        
        // 允许不安全内容和混合内容
        configuration.preferences.javaScriptCanOpenWindowsAutomatically = true
        
        // 允许加载第三方Cookie
        if #available(iOS 11.0, *) {
            configuration.websiteDataStore.httpCookieStore.setCookie(HTTPCookie(properties: [
                .domain: "sandbox.jazzcash.com.pk",
                .path: "/",
                .name: "allowThirdParty",
                .value: "true",
                .secure: "TRUE",
                .expires: NSDate(timeIntervalSinceNow: 31536000)
            ])!)
        }
        
        // 添加JavaScript接口
        let userController = WKUserContentController()
        userController.add(self, name: "paymentCallback")
        configuration.userContentController = userController
        
        // 注入自定义CSS，确保表单元素完全可见
        let cssString = """
        input, button, select, textarea {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
        }
        """
        let cssScript = WKUserScript(source: "var style = document.createElement('style'); style.innerHTML = '\(cssString)'; document.head.appendChild(style);", injectionTime: .atDocumentEnd, forMainFrameOnly: true)
        userController.addUserScript(cssScript)
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.translatesAutoresizingMaskIntoConstraints = false
        webView.navigationDelegate = self
        webView.backgroundColor = .white
        
        // 设置通用属性
        webView.allowsBackForwardNavigationGestures = true
        webView.allowsLinkPreview = false
        
        return webView
    }()
    
    private lazy var progressView: UIProgressView = {
        let progressView = UIProgressView(progressViewStyle: .default)
        progressView.translatesAutoresizingMaskIntoConstraints = false
        progressView.tintColor = .systemBlue
        return progressView
    }()
    
    private lazy var activityIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .large)
        indicator.translatesAutoresizingMaskIntoConstraints = false
        indicator.hidesWhenStopped = true
        return indicator
    }()
    
    private lazy var errorView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        view.isHidden = true
        return view
    }()
    
    private lazy var errorLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textAlignment = .center
        label.numberOfLines = 0
        label.textColor = .systemRed
        label.font = UIFont.systemFont(ofSize: 16)
        return label
    }()
    
    private lazy var retryButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("重试", for: .normal)
        button.addTarget(self, action: #selector(retryLoading), for: .touchUpInside)
        return button
    }()
    
    // MARK: - 属性
    
    var postData: [String: String] = [:]
    var txnRefNo: String?
    var txnType: String = "MPAY"  // 默认交易类型
    
    private var loadAttempts = 0
    private var manuallyHandlingCallback = false
    private var loadingTimer: Timer?
    private var isCallbackPage = false
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        title = "JazzCash 支付"
        view.backgroundColor = .white
        
        setupUI()
        loadPaymentPage()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        // 清除计时器和观察者
        loadingTimer?.invalidate()
        loadingTimer = nil
    }
    
    deinit {
        // 移除JavaScript接口
        webView.configuration.userContentController.removeScriptMessageHandler(forName: "paymentCallback")
        manuallyHandlingCallback = false
    }
    
    // MARK: - 私有方法
    
    private func setupUI() {
        // 添加WebView
        view.addSubview(webView)
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
        
        // 添加进度条
        view.addSubview(progressView)
        NSLayoutConstraint.activate([
            progressView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            progressView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            progressView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            progressView.heightAnchor.constraint(equalToConstant: 2)
        ])
        
        // 添加活动指示器
        view.addSubview(activityIndicator)
        NSLayoutConstraint.activate([
            activityIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
        
        // 添加错误视图
        view.addSubview(errorView)
        NSLayoutConstraint.activate([
            errorView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            errorView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            errorView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            errorView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
        
        errorView.addSubview(errorLabel)
        errorView.addSubview(retryButton)
        
        NSLayoutConstraint.activate([
            errorLabel.centerYAnchor.constraint(equalTo: errorView.centerYAnchor, constant: -40),
            errorLabel.leadingAnchor.constraint(equalTo: errorView.leadingAnchor, constant: 20),
            errorLabel.trailingAnchor.constraint(equalTo: errorView.trailingAnchor, constant: -20),
            
            retryButton.topAnchor.constraint(equalTo: errorLabel.bottomAnchor, constant: 20),
            retryButton.centerXAnchor.constraint(equalTo: errorView.centerXAnchor),
            retryButton.widthAnchor.constraint(equalToConstant: 120),
            retryButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    private func loadPaymentPage() {
        loadAttempts += 1
        
        // 显示进度条，隐藏错误信息
        progressView.isHidden = false
        errorView.isHidden = true
        webView.isHidden = false
        
        print("正在向 JazzCash 支付网关发送请求: \(PAYMENT_URL) (尝试 \(loadAttempts))")
        
        // 准备POST请求数据
        let urlComponents = URLComponents(string: PAYMENT_URL)!
        
        // 创建可修改的副本
        let safePostData = postData
        
        // 强制将中文描述替换为英文描述，不管是否包含非字母数字字符
        var updatedPostData = safePostData
        updatedPostData["pp_Description"] = "Product test description"
        print("将描述替换为英文测试描述")
        
        // 确保交易类型已设置，强制使用MIGS（与Android对齐）
        updatedPostData["pp_TxnType"] = "MIGS"
        print("设置交易类型为MIGS")
        
        // 从字典创建请求体 - 直接使用字符串拼接而不是URLComponents
        let postDataString = updatedPostData.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        print("POST数据: \(postDataString)")
        
        guard let requestBody = postDataString.data(using: .utf8) else {
            showError(message: "准备请求数据时出错")
            return
        }
        
        // 创建POST请求
        var request = URLRequest(url: urlComponents.url!)
        request.httpMethod = "POST"
        request.httpBody = requestBody
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        // 加载请求
        webView.load(request)
        
        // 设置加载超时
        startLoadingTimer()
    }
    
    private func startLoadingTimer() {
        // 注释掉超时处理，让用户决定是否启用
        // 设置60秒超时
        // loadingTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: false) { [weak self] _ in
        //     guard let self = self else { return }
        //     
        //     DispatchQueue.main.async {
        //         if self.activityIndicator.isAnimating {
        //             self.showLoadingError(message: "加载支付页面超时，请检查您的网络连接后重试。")
        //         }
        //     }
        // }
    }
    
    private func showLoadingError(message: String) {
        activityIndicator.stopAnimating()
        webView.isHidden = true
        errorView.isHidden = false
        errorLabel.text = message
        
        // 添加错误图标以提高视觉提示
        if let errorIcon = errorView.viewWithTag(100) as? UIImageView {
            // 如果已存在错误图标，不需要重新创建
        } else {
            // 创建错误图标
            let iconSize: CGFloat = 60
            let errorIcon = UIImageView(frame: CGRect(x: 0, y: 0, width: iconSize, height: iconSize))
            errorIcon.tag = 100
            errorIcon.translatesAutoresizingMaskIntoConstraints = false
            errorIcon.contentMode = .scaleAspectFit
            errorIcon.tintColor = .systemRed
            
            // 使用系统错误图标
            if #available(iOS 13.0, *) {
                errorIcon.image = UIImage(systemName: "exclamationmark.triangle.fill")
            } else {
                // 对于iOS 13以下版本，使用简单的方形作为错误标记
                errorIcon.backgroundColor = .systemRed
                errorIcon.layer.cornerRadius = 10
            }
            
            errorView.addSubview(errorIcon)
            
            // 设置约束
            NSLayoutConstraint.activate([
                errorIcon.centerXAnchor.constraint(equalTo: errorView.centerXAnchor),
                errorIcon.bottomAnchor.constraint(equalTo: errorLabel.topAnchor, constant: -20),
                errorIcon.widthAnchor.constraint(equalToConstant: iconSize),
                errorIcon.heightAnchor.constraint(equalToConstant: iconSize)
            ])
        }
        
        // 确保重试按钮是可见的
        retryButton.isHidden = loadAttempts >= MAX_LOAD_ATTEMPTS
        
        // 如果达到最大尝试次数，显示更明确的消息
        if loadAttempts >= MAX_LOAD_ATTEMPTS {
            errorLabel.text = message + "\n\n已达到最大尝试次数，请返回重新发起支付"
            
            // 添加返回按钮
            if errorView.viewWithTag(101) == nil {
                let backButton = UIButton(type: .system)
                backButton.tag = 101
                backButton.translatesAutoresizingMaskIntoConstraints = false
                backButton.setTitle("返回", for: .normal)
                backButton.backgroundColor = .systemBlue
                backButton.setTitleColor(.white, for: .normal)
                backButton.layer.cornerRadius = 8
                backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
                
                errorView.addSubview(backButton)
                
                NSLayoutConstraint.activate([
                    backButton.topAnchor.constraint(equalTo: retryButton.topAnchor),
                    backButton.centerXAnchor.constraint(equalTo: errorView.centerXAnchor),
                    backButton.widthAnchor.constraint(equalToConstant: 120),
                    backButton.heightAnchor.constraint(equalToConstant: 44)
                ])
            }
        }
    }
    
    @objc private func retryLoading() {
        if loadAttempts < MAX_LOAD_ATTEMPTS {
            loadPaymentPage()
        } else {
            // 达到最大尝试次数，显示错误信息
            showError(message: "已达到最大尝试次数，请返回重新发起支付")
            // 3秒后自动返回
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                self.setFailResult(message: "已达到最大尝试次数，请重新发起支付")
            }
        }
    }
    
    private func showError(message: String) {
        progressView.isHidden = true
        webView.isHidden = true
        errorView.isHidden = false
        errorLabel.text = message
        
        // 如果达到最大尝试次数，隐藏重试按钮
        retryButton.isHidden = loadAttempts >= MAX_LOAD_ATTEMPTS
    }
    
    @objc private func handlePaymentResult(_ notification: Notification) {
        // 处理支付结果通知
        if let userInfo = notification.userInfo {
            let success = userInfo["success"] as? Bool ?? false
            let message = userInfo["message"] as? String ?? "支付完成"
            
            if success {
                print("支付成功: \(message)")
            } else {
                print("支付失败: \(message)")
            }
        }
    }
    
    // MARK: - 错误处理
    
    private func handleAPIError(_ error: Error) {
        let errorMessage = "API错误: \(error.localizedDescription)"
        print(errorMessage)
        setFailResult(message: errorMessage)
    }
    
    private func handleNetworkError(_ error: Error) {
        let errorMessage = "网络错误: \(error.localizedDescription)"
        print(errorMessage)
        setFailResult(message: errorMessage)
    }
    
    // MARK: - 支付结果处理
    
    private func setSuccessResult(response: [String: String]) {
        print("设置支付成功结果，响应数据: \(response)")
        
        // 停止加载计时器
        loadingTimer?.invalidate()
        loadingTimer = nil
        
        // 构建详细的结果信息
        var resultInfo: [String: Any] = [
            "success": true,
            "message": "支付成功",
            "transactionId": response["pp_TxnRefNo"] ?? (txnRefNo ?? "未知"),
            "responseCode": response["pp_ResponseCode"] ?? "000",
            "responseMessage": response["pp_ResponseMessage"] ?? "Success"
        ]

        // 添加所有响应参数到resultInfo
        var allParams = [String: Any]()
        for (key, value) in response {
            allParams[key] = value
        }
        resultInfo["response"] = allParams
        
        // 先显示一个成功提示，然后发送通知并返回
        let alert = UIAlertController(
            title: "支付成功",
            message: "交易号: \(resultInfo["transactionId"] as? String ?? "")\n响应信息: \(resultInfo["responseMessage"] as? String ?? "")",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            guard let self = self else { return }
            
            // 发送通知
            NotificationCenter.default.post(
                name: NSNotification.Name("JazzCashPaymentResult"),
                object: nil,
                userInfo: resultInfo
            )
            
            // 返回上一页
            DispatchQueue.main.async {
                self.navigationController?.popViewController(animated: true)
            }
        })
        
        // 在主线程显示提示
        DispatchQueue.main.async {
            self.present(alert, animated: true)
        }
    }
    
    private func setFailResult(message: String) {
        print("设置支付失败结果，错误信息: \(message)")
        
        // 停止加载计时器
        loadingTimer?.invalidate()
        loadingTimer = nil
        
        // 构建结果信息
        let resultInfo: [String: Any] = [
            "success": false,
            "error": message,
            "txnRefNo": txnRefNo ?? "未知"
        ]
        
        // 先显示一个失败提示，然后发送通知并返回
        let alert = UIAlertController(
            title: "支付失败",
            message: message,
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            guard let self = self else { return }
            
            // 发送通知
            NotificationCenter.default.post(
                name: NSNotification.Name("JazzCashPaymentResult"),
                object: nil,
                userInfo: resultInfo
            )
            
            // 返回上一页
            DispatchQueue.main.async {
                self.navigationController?.popViewController(animated: true)
            }
        })
        
        // 在主线程显示提示
        DispatchQueue.main.async {
            self.present(alert, animated: true)
        }
    }
    
    private func processCallbackData(_ callbackData: String) {
        print("处理回调数据: \(callbackData)")
        
        // 如果已经处理过回调，忽略
        if manuallyHandlingCallback {
            print("已经处理过回调，忽略")
            return
        }
        
        // 检查这是否为表单数据而不是真正的支付回调
        // 表单数据通常包含__RequestVerificationToken, ddl_TxnList等字段
        if callbackData.contains("__RequestVerificationToken") || 
           callbackData.contains("ddl_TxnList") || 
           callbackData.contains("recaptcha") {
            print("检测到表单数据，不是真正的支付回调，忽略")
            return
        }
        
        // 检查是否包含支付响应相关的关键字段
        let isPaymentResponse = callbackData.contains("pp_ResponseCode") || 
                               callbackData.contains("pp_ResponseMessage") || 
                               callbackData.contains("pp_TxnRefNo") ||
                               callbackData.contains("PostParameter")
        
        if !isPaymentResponse {
            print("数据不包含支付响应必要字段，忽略")
            return
        }
        
        manuallyHandlingCallback = true
        
        // 解析回调数据
        var response = [String: String]()
        var isError = false
        
        // 尝试解析为URL查询参数格式
        if callbackData.contains("=") {
            let pairs = callbackData.components(separatedBy: "&")
            for pair in pairs {
                let keyValue = pair.components(separatedBy: "=")
                if keyValue.count == 2 {
                    response[keyValue[0]] = keyValue[1]
                }
            }
        } else {
            // 尝试解析为JSON格式
            do {
                if let data = callbackData.data(using: .utf8) {
                    if let jsonObj = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                        // 如果有嵌套的data字段
                        if let dataObj = jsonObj["data"] as? [String: String] {
                            for (key, value) in dataObj {
                                response[key] = value
                            }
                            
                            // 特别处理响应码和消息
                            if let ppResponseCode = dataObj["ppResponseCode"] {
                                response["pp_ResponseCode"] = ppResponseCode
                            }
                            if let ppResponseMessage = dataObj["ppResponseMessage"] {
                                response["pp_ResponseMessage"] = ppResponseMessage
                            }
                        } else if let postParameter = jsonObj["PostParameter"] as? [String: Any] {
                            // 处理JazzCash错误响应格式
                            print("检测到PostParameter格式的响应")
                            for (key, value) in postParameter {
                                if let stringValue = value as? String {
                                    response[key] = stringValue
                                }
                            }
                            
                            // 如果是错误响应，标记为错误
                            if let responseCode = response["pp_ResponseCode"], responseCode != "000" {
                                isError = true
                            }
                        } else {
                            // 直接使用顶层字段
                            for (key, value) in jsonObj {
                                if let stringValue = value as? String {
                                    response[key] = stringValue
                                }
                            }
                        }
                    } else if let jsonObj = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                              let message = jsonObj["message"] as? [String: Any] {
                        // 尝试解析message对象
                        if let postParameter = message["PostParameter"] as? [String: Any] {
                            print("检测到message.PostParameter格式的响应")
                            for (key, value) in postParameter {
                                if let stringValue = value as? String {
                                    response[key] = stringValue
                                }
                            }
                            
                            // 如果是错误响应，标记为错误
                            if let responseCode = response["pp_ResponseCode"], responseCode != "000" {
                                isError = true
                            }
                        }
                    }
                }
            } catch {
                print("解析JSON失败: \(error)")
                
                // 尝试在HTML中查找错误信息
                if callbackData.contains("pp_ResponseCode") && callbackData.contains("pp_ResponseMessage") {
                    let pattern = "pp_ResponseCode\":\"(\\d+)\",\"pp_ResponseMessage\":\"([^\"]+)\""
                    if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
                        let nsString = callbackData as NSString
                        let matches = regex.matches(in: callbackData, options: [], range: NSRange(location: 0, length: nsString.length))
                        
                        if let match = matches.first {
                            let codeRange = match.range(at: 1)
                            let messageRange = match.range(at: 2)
                            
                            if codeRange.location != NSNotFound && messageRange.location != NSNotFound {
                                let code = nsString.substring(with: codeRange)
                                let message = nsString.substring(with: messageRange)
                                
                                response["pp_ResponseCode"] = code
                                response["pp_ResponseMessage"] = message
                                isError = code != "000"
                                
                                print("从HTML提取到错误信息: 代码=\(code), 消息=\(message)")
                            }
                        }
                    }
                }
            }
        }
        
        // 如果没有解析到任何支付相关字段，则认为不是真正的支付响应
        if response.isEmpty || (!response.keys.contains("pp_ResponseCode") && 
                               !response.keys.contains("pp_TxnRefNo") &&
                               !response.keys.contains("pp_ResponseMessage")) {
            print("解析结果不包含任何支付响应字段，忽略处理")
            manuallyHandlingCallback = false
            return
        }
        
        // 从HTML页面源码中提取错误信息
        if response.isEmpty && callbackData.contains("pp_ResponseCode") {
            webView.evaluateJavaScript("document.body.innerHTML") { [weak self] (result, error) in
                guard let self = self else { return }
                
                if let htmlContent = result as? String {
                    print("尝试从HTML源码提取错误信息")
                    
                    // 查找JSON对象
                    let startPattern = "window.parent.postMessage({"
                    let endPattern = "}, window.origin);"
                    
                    if let startRange = htmlContent.range(of: startPattern),
                       let endRange = htmlContent.range(of: endPattern, options: [], range: htmlContent.index(startRange.upperBound, offsetBy: 1)..<htmlContent.endIndex) {
                        
                        let jsonStartIndex = htmlContent.index(startRange.upperBound, offsetBy: 0)
                        let jsonString = String(htmlContent[jsonStartIndex..<endRange.lowerBound])
                        
                        print("提取到的JSON字符串: \(jsonString)")
                        
                        // 尝试解析提取的JSON
                        if let messageStart = jsonString.range(of: "'message': "),
                           let messageEnd = jsonString.range(of: ",", options: [], range: messageStart.upperBound..<jsonString.endIndex) {
                            
                            let messageJson = String(jsonString[messageStart.upperBound..<messageEnd.lowerBound])
                            print("提取到的message JSON: \(messageJson)")
                            
                            do {
                                if let data = messageJson.data(using: .utf8),
                                   let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                                    
                                    if let postParameter = json["PostParameter"] as? [String: Any] {
                                        var errorResponse = [String: String]()
                                        
                                        for (key, value) in postParameter {
                                            if let stringValue = value as? String {
                                                errorResponse[key] = stringValue
                                            }
                                        }
                                        
                                        if let responseCode = errorResponse["pp_ResponseCode"],
                                           let responseMessage = errorResponse["pp_ResponseMessage"] {
                                            
                                            print("从HTML提取到错误信息: 代码=\(responseCode), 消息=\(responseMessage)")
                                            
                                            // 设置失败结果
                                            self.setFailResult(message: responseMessage)
                                        }
                                    }
                                }
                            } catch {
                                print("解析提取的JSON失败: \(error)")
                            }
                        }
                    }
                }
            }
            return
        }
        
        // 检查支付状态
        if let responseCode = response["pp_ResponseCode"] {
            if responseCode == "000" {
                // 支付成功
                print("支付成功！交易 ID: \(response["pp_TxnRefNo"] ?? txnRefNo ?? "")")
                setSuccessResult(response: response)
            } else {
                // 支付失败
                let errorMessage = response["pp_ResponseMessage"] ?? "支付失败，错误码: \(responseCode)"
                print("支付失败: \(errorMessage)")
                setFailResult(message: errorMessage)
            }
        } else if response.isEmpty && !isError {
            // 如果响应为空且没有明确的错误，查看页面源码尝试提取错误信息
            webView.evaluateJavaScript("document.documentElement.outerHTML") { [weak self] (result, error) in
                guard let self = self else { return }
                
                if let htmlContent = result as? String {
                    print("尝试从页面源码中提取信息，HTML长度: \(htmlContent.count)")
                    
                    // 在页面中查找常见的错误信息或JSON对象
                    if htmlContent.contains("pp_ResponseCode") && htmlContent.contains("pp_ResponseMessage") {
                        // 提取错误信息的逻辑...
                        print("页面中包含响应码和响应消息，但提取失败")
                    }
                }
                
                // 默认认为成功，因为回调URL通常只在成功时才会调用
                print("没有找到明确的错误信息，默认为成功")
                let defaultResponse = ["pp_ResponseCode": "000", 
                                      "pp_ResponseMessage": "Success", 
                                      "pp_TxnRefNo": self.txnRefNo ?? ""]
                self.setSuccessResult(response: defaultResponse)
            }
        } else {
            // 没有响应码，且存在错误标记
            print("回调数据格式不正确或明确存在错误")
            
            // 从响应中获取错误信息
            let errorMessage = response["pp_ResponseMessage"] ?? "未知错误"
            print("错误信息: \(errorMessage)")
            setFailResult(message: errorMessage)
        }
    }
    
    // MARK: - 注入表单提交拦截器
    private func injectFormSubmitInterceptor() {
        // 首先，获取并打印整个页面的HTML，帮助调试
        webView.evaluateJavaScript("document.documentElement.outerHTML") { (result, error) in
            if let htmlContent = result as? String {
                print("完整页面HTML: \n\(htmlContent)")
                
                // 尝试提取错误信息
                if htmlContent.contains("pp_ResponseCode") && htmlContent.contains("pp_ResponseMessage") {
                    print("页面HTML中包含错误信息，尝试提取")
                    
                    // 查找响应对象
                    if let jsonStart = htmlContent.range(of: "{\"PostParameter\":"),
                       let jsonEnd = htmlContent.range(of: "}", options: .backwards) {
                        
                        let jsonString = String(htmlContent[jsonStart.lowerBound..<jsonEnd.upperBound])
                        print("提取到的JSON: \(jsonString)")
                        
                        if let data = jsonString.data(using: .utf8),
                           let json = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                           let postParam = json["PostParameter"] as? [String: Any] {
                            
                            // 提取响应码和消息
                            if let responseCode = postParam["pp_ResponseCode"] as? String,
                               let responseMessage = postParam["pp_ResponseMessage"] as? String {
                                
                                print("提取到错误信息: 代码=\(responseCode), 消息=\(responseMessage)")
                                
                                // 如果是错误响应，直接处理
                                if responseCode != "000" {
                                    let errorData: [String: String] = [
                                        "pp_ResponseCode": responseCode,
                                        "pp_ResponseMessage": responseMessage,
                                        "pp_TxnRefNo": postParam["pp_TxnRefNo"] as? String ?? self.txnRefNo ?? ""
                                    ]
                                    
                                    // 将JSON字符串化，以便传递给processCallbackData
                                    if let errorJson = try? JSONSerialization.data(withJSONObject: errorData, options: []),
                                       let errorJsonString = String(data: errorJson, encoding: .utf8) {
                                        self.processCallbackData(errorJsonString)
                                    }
                                }
                            }
                        }
                    }
                }
            } else if let error = error {
                print("获取页面HTML失败: \(error)")
            }
        }
        
        // 该方法实现与Android版本相同的功能，用于拦截表单提交并获取支付响应数据
        // 对应Android中的injectFormSubmitInterceptor方法
        let script = """
        (function() {
            console.log("注入表单提交拦截器");
            
            // 调试页面元素
            console.log("页面标题: " + document.title);
            console.log("页面URL: " + window.location.href);
            
            // 首先尝试获取错误信息
            try {
                // 尝试从script标签中提取错误JSON
                const scripts = document.querySelectorAll('script');
                for (let i = 0; i < scripts.length; i++) {
                    const scriptContent = scripts[i].textContent || '';
                    if (scriptContent.includes('pp_ResponseCode') && scriptContent.includes('pp_ResponseMessage')) {
                        console.log("找到包含错误信息的脚本");
                        
                        // 尝试提取JSON对象
                        const jsonMatch = scriptContent.match(/\\{[^\\{\\}]*"PostParameter"[^\\{\\}]*\\}/);
                        if (jsonMatch) {
                            console.log("提取到错误JSON:", jsonMatch[0]);
                            try {
                                window.webkit.messageHandlers.paymentCallback.postMessage("ERROR_DATA:" + jsonMatch[0]);
                            } catch(e) {
                                console.error("调用iOS接口出错:", e);
                            }
                        }
                        
                        // 也尝试提取完整消息对象
                        const messageMatch = scriptContent.match(/\\{[^\\{\\}]*"message"[^\\{\\}]*\\}/);
                        if (messageMatch) {
                            console.log("提取到message JSON:", messageMatch[0]);
                            try {
                                window.webkit.messageHandlers.paymentCallback.postMessage("ERROR_DATA:" + messageMatch[0]);
                            } catch(e) {
                                console.error("调用iOS接口出错:", e);
                            }
                        }
                    }
                }
            } catch(e) {
                console.error("提取错误信息失败:", e);
            }
            
            // 查找所有表单
            const forms = document.forms;
            console.log("页面中表单数量:", forms.length);
            
            // 监听所有表单的提交事件
            for (let i = 0; i < forms.length; i++) {
                const form = forms[i];
                console.log("表单", i, "action:", form.action);
                
                // 监听表单提交事件
                form.addEventListener('submit', function(e) {
                    console.log("表单提交事件触发");
                    
                    // 收集表单数据
                    const formData = new FormData(form);
                    let formDataObj = {};
                    for (let [key, value] of formData.entries()) {
                        formDataObj[key] = value;
                    }
                    
                    console.log("表单提交数据:", JSON.stringify(formDataObj));
                    
                    // 检查是否是支付响应数据
                    const isPaymentResponse = 
                        Object.keys(formDataObj).some(key => 
                            key.startsWith('pp_') || 
                            key === 'PostParameter'
                        );
                    
                    try {
                        if (isPaymentResponse) {
                            window.webkit.messageHandlers.paymentCallback.postMessage("PAYMENT_RESPONSE:" + JSON.stringify(formDataObj));
                        } else {
                            window.webkit.messageHandlers.paymentCallback.postMessage("FORM_DATA:" + JSON.stringify(formDataObj));
                        }
                    } catch(e) {
                        console.error("调用iOS接口出错:", e);
                    }
                });
                
                // 劫持表单提交
                const originalSubmit = form.submit;
                form.submit = function() {
                    console.log("表单提交方法被调用");
                    
                    // 收集表单数据
                    const formData = new FormData(form);
                    let formDataObj = {};
                    for (let [key, value] of formData.entries()) {
                        formDataObj[key] = value;
                    }
                    
                    console.log("表单提交数据:", JSON.stringify(formDataObj));
                    
                    // 检查是否是支付响应数据
                    const isPaymentResponse = 
                        Object.keys(formDataObj).some(key => 
                            key.startsWith('pp_') || 
                            key === 'PostParameter'
                        );
                    
                    try {
                        if (isPaymentResponse) {
                            window.webkit.messageHandlers.paymentCallback.postMessage("PAYMENT_RESPONSE:" + JSON.stringify(formDataObj));
                        } else {
                            window.webkit.messageHandlers.paymentCallback.postMessage("FORM_DATA:" + JSON.stringify(formDataObj));
                        }
                    } catch(e) {
                        console.error("调用iOS接口出错:", e);
                    }
                    
                    // 执行原始提交
                    originalSubmit.apply(form);
                };
                
                // 立即收集表单当前数据
                const formData = new FormData(form);
                let formDataObj = {};
                for (let [key, value] of formData.entries()) {
                    formDataObj[key] = value;
                }
                
                console.log("表单", i, "当前数据:", JSON.stringify(formDataObj));
                
                // 检查是否是支付响应数据
                const isPaymentResponse = 
                    Object.keys(formDataObj).some(key => 
                        key.startsWith('pp_') || 
                        key === 'PostParameter'
                    );
                
                try {
                    if (isPaymentResponse) {
                        window.webkit.messageHandlers.paymentCallback.postMessage("PAYMENT_RESPONSE:" + JSON.stringify(formDataObj));
                    } else {
                        window.webkit.messageHandlers.paymentCallback.postMessage("FORM_DATA:" + JSON.stringify(formDataObj));
                    }
                } catch(e) {
                    console.error("调用iOS接口出错:", e);
                }
            }
            
            // 查找所有输入字段（包括隐藏和可见的）
            const allInputs = document.querySelectorAll('input');
            console.log("页面中所有输入字段数量:", allInputs.length);
            
            let allInputData = {};
            for (let i = 0; i < allInputs.length; i++) {
                const input = allInputs[i];
                if (input.name) {
                    allInputData[input.name] = input.value;
                    console.log("输入字段:", input.name, "=", input.value, "类型:", input.type);
                }
            }
            
            console.log("所有输入字段数据:", JSON.stringify(allInputData));
            
            // 检查是否是支付响应数据
            const isPaymentResponse = 
                Object.keys(allInputData).some(key => 
                    key.startsWith('pp_') || 
                    key === 'PostParameter'
                );
            
            try {
                if (isPaymentResponse) {
                    window.webkit.messageHandlers.paymentCallback.postMessage("PAYMENT_RESPONSE:" + JSON.stringify(allInputData));
                } else {
                    window.webkit.messageHandlers.paymentCallback.postMessage("FORM_DATA:" + JSON.stringify(allInputData));
                }
            } catch(e) {
                console.error("调用iOS接口出错:", e);
            }
            
            // 监听页面上所有可能的表单按钮点击事件
            const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
            console.log("找到按钮数量:", buttons.length);
            for (let i = 0; i < buttons.length; i++) {
                console.log("按钮", i, ":", buttons[i].innerText || buttons[i].value, "类型:", buttons[i].type);
                buttons[i].addEventListener('click', function(e) {
                    console.log("按钮点击:", this.innerText || this.value);
                    
                    // 延迟一点时间收集表单数据
                    setTimeout(function() {
                        // 重新获取所有表单数据
                        for (let j = 0; j < forms.length; j++) {
                            const form = forms[j];
                            const formData = new FormData(form);
                            let formDataObj = {};
                            for (let [key, value] of formData.entries()) {
                                formDataObj[key] = value;
                            }
                            
                            console.log("按钮点击后表单数据:", JSON.stringify(formDataObj));
                            
                            // 检查是否是支付响应数据
                            const isPaymentResponse = 
                                Object.keys(formDataObj).some(key => 
                                    key.startsWith('pp_') || 
                                    key === 'PostParameter'
                                );
                            
                            try {
                                if (isPaymentResponse) {
                                    window.webkit.messageHandlers.paymentCallback.postMessage("PAYMENT_RESPONSE:" + JSON.stringify(formDataObj));
                                } else {
                                    window.webkit.messageHandlers.paymentCallback.postMessage("FORM_DATA:" + JSON.stringify(formDataObj));
                                }
                            } catch(e) {
                                console.error("调用iOS接口出错:", e);
                            }
                        }
                    }, 100);
                });
            }
        })();
        """
        
        webView.evaluateJavaScript(script) { (result, error) in
            if let error = error {
                print("注入表单提交拦截器出错: \(error)")
            } else {
                print("表单提交拦截器注入成功: \(String(describing: result))")
            }
        }
    }
    
    @objc private func backButtonTapped() {
        // 取消支付并返回
        let resultInfo: [String: Any] = [
            "success": false,
            "error": "用户取消支付",
            "txnRefNo": txnRefNo ?? "未知"
        ]
        
        // 发送通知
        NotificationCenter.default.post(
            name: NSNotification.Name("JazzCashPaymentResult"),
            object: nil,
            userInfo: resultInfo
        )
        
        // 返回上一页
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - WKNavigationDelegate

extension JazzCashViewController: WKNavigationDelegate {
    
    // 页面开始加载
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        print("页面开始加载: \(webView.url?.absoluteString ?? "")")
        
        // 显示进度条
        progressView.isHidden = false
        errorView.isHidden = true
        
        // 显示活动指示器
        activityIndicator.startAnimating()
        
        // 检查是否是回调 URL
        if let url = webView.url?.absoluteString {
            if url.contains(CALLBACK_URL_PREFIX) {
                print("检测到回调 URL: \(url)")
                isCallbackPage = true
                
                // 注入表单提交拦截器
                injectFormSubmitInterceptor()
                
                // 从 URL 中提取查询参数
                if let components = URLComponents(string: url), let queryItems = components.queryItems {
                    let queryDict = queryItems.reduce(into: [String: String]()) { (result, item) in
                        result[item.name] = item.value
                    }
                    
                    // 将查询参数转换为查询字符串格式
                    let queryString = queryDict.map { "\($0.key)=\($0.value ?? "")" }.joined(separator: "&")
                    print("从回调 URL 提取的参数: \(queryString)")
                    
                    // 手动处理回调数据
                    if !queryString.isEmpty {
                        processCallbackData(queryString)
                    }
                }
            }
        }
    }
    
    // 页面加载完成
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        print("页面加载完成: \(webView.url?.absoluteString ?? "")")
        
        // 隐藏进度条
        progressView.isHidden = true
        
        // 停止加载计时器
        loadingTimer?.invalidate()
        loadingTimer = nil
        
        // 停止活动指示器
        activityIndicator.stopAnimating()
        
        // 始终注入表单提交拦截器，不仅限于回调页面
        injectFormSubmitInterceptor()
        
        // 尝试查找页面内容
        webView.evaluateJavaScript("document.body.innerHTML") { [weak self] (result, error) in
            guard let self = self else { return }
            
            if let htmlContent = result as? String {
                print("页面HTML长度: \(htmlContent.count) 字符")
                
                // 检查页面是否包含支付表单的关键字
                let containsPaymentForm = htmlContent.contains("pp_TxnRefNo") || 
                                         htmlContent.contains("pp_Amount") ||
                                         htmlContent.contains("jazzcash") ||
                                         htmlContent.contains("payment")
                
                if containsPaymentForm {
                    print("检测到支付表单")
                    // 再次注入表单监听器，确保捕获所有表单
                    self.injectFormSubmitInterceptor()
                }
            }
            
            // 也尝试获取纯文本内容
            webView.evaluateJavaScript("document.body.innerText") { [weak self] (result, error) in
                guard let self = self else { return }
                
                if let textContent = result as? String, !textContent.isEmpty {
                    print("页面文本内容长度: \(textContent.count) 字符")
                    
                    // 检查是否是JSON响应
                    if textContent.contains("{") && textContent.contains("}") {
                        print("检测到可能的JSON响应")
                        self.processCallbackData(textContent)
                    }
                }
            }
        }
    }
    
    // 页面加载失败
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        print("页面加载失败: \(error.localizedDescription)")
        print("详细错误信息: \(error)")
        
        // 如果是回调URL处理中的取消错误，忽略它
        if manuallyHandlingCallback {
            print("忽略回调处理中的导航取消")
            return
        }
        
        // 隐藏进度条，显示错误信息
        progressView.isHidden = true
        activityIndicator.stopAnimating()
        
        // 检查错误类型并提供更友好的错误信息
        let errorCode = (error as NSError).code
        var errorMessage = "加载支付页面失败"
        
        switch errorCode {
        case NSURLErrorNotConnectedToInternet:
            errorMessage = "网络连接失败，请检查您的网络设置后重试。"
        case NSURLErrorTimedOut:
            errorMessage = "连接超时，请检查网络后重试。"
        case NSURLErrorCannotFindHost, NSURLErrorCannotConnectToHost:
            errorMessage = "无法连接到支付服务器，请稍后重试。"
        case NSURLErrorServerCertificateUntrusted, NSURLErrorServerCertificateHasUnknownRoot:
            errorMessage = "服务器证书不受信任，请联系支持团队。"
        default:
            errorMessage = "加载支付页面失败: \(error.localizedDescription)"
        }
        
        showLoadingError(message: errorMessage)
    }
    
    // 页面加载开始时失败
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        print("页面加载开始时失败: \(error.localizedDescription)")
        print("详细错误信息: \(error)")
        
        // 如果是回调URL处理中的取消错误，忽略它
        if manuallyHandlingCallback {
            print("忽略回调处理中的导航取消")
            return
        }
        
        // 隐藏进度条，显示错误信息
        progressView.isHidden = true
        activityIndicator.stopAnimating()
        
        // 检查错误类型并提供更友好的错误信息
        let errorCode = (error as NSError).code
        var errorMessage = "加载支付页面失败"
        
        switch errorCode {
        case NSURLErrorNotConnectedToInternet:
            errorMessage = "网络连接失败，请检查您的网络设置后重试。"
        case NSURLErrorTimedOut:
            errorMessage = "连接超时，请检查网络后重试。"
        case NSURLErrorCannotFindHost, NSURLErrorCannotConnectToHost:
            errorMessage = "无法连接到支付服务器，请稍后重试。"
        case NSURLErrorServerCertificateUntrusted, NSURLErrorServerCertificateHasUnknownRoot:
            errorMessage = "服务器证书不受信任，请联系支持团队。"
        default:
            errorMessage = "加载支付页面失败: \(error.localizedDescription)"
        }
        
        showLoadingError(message: errorMessage)
    }
    
    // 收到服务器重定向
    func webView(_ webView: WKWebView, didReceiveServerRedirectForProvisionalNavigation navigation: WKNavigation!) {
        print("收到服务器重定向: \(webView.url?.absoluteString ?? "")")
        
        // 检查是否是回调URL
        if let url = webView.url?.absoluteString, url.contains(CALLBACK_URL_PREFIX) {
            print("重定向到了回调URL: \(url)")
            isCallbackPage = true
        }
    }
    
    // 决定是否允许导航
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        if let url = navigationAction.request.url?.absoluteString {
            print("导航请求: \(url)")
            
            // 检查是否是回调 URL
            if url.contains(CALLBACK_URL_PREFIX) {
                print("检测到回调 URL，将处理回调")
                isCallbackPage = true
                
                // 允许导航，我们将在didStartProvisionalNavigation中处理
                decisionHandler(.allow)
                return
            }
        }
        
        // 允许其他所有导航
        decisionHandler(.allow)
    }
}

// MARK: - WKScriptMessageHandler

extension JazzCashViewController: WKScriptMessageHandler {
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        if message.name == "paymentCallback" {
            guard let messageBody = message.body as? String else {
                print("从JavaScript收到无效消息格式")
                return
            }
            
            print("从JavaScript收到消息: \(messageBody)")
            
            // 根据消息前缀处理不同类型的数据
            if messageBody.hasPrefix("ERROR_DATA:") {
                let errorData = String(messageBody.dropFirst("ERROR_DATA:".count))
                print("收到错误数据: \(errorData)")
                processCallbackData(errorData)
            } else if messageBody.hasPrefix("PAYMENT_RESPONSE:") {
                let paymentData = String(messageBody.dropFirst("PAYMENT_RESPONSE:".count))
                print("收到支付响应数据: \(paymentData)")
                processCallbackData(paymentData)
            } else if messageBody.hasPrefix("FORM_DATA:") {
                let formData = String(messageBody.dropFirst("FORM_DATA:".count))
                print("收到表单数据(不处理): \(formData)")
                // 不处理普通表单数据
            } else {
                // 尝试处理无前缀的数据(兼容旧代码)
                print("收到未分类消息: \(messageBody)")
                processCallbackData(messageBody)
            }
        }
    }
} 
