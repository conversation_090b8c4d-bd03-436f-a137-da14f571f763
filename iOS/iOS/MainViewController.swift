import UIKit

/**
 * 主视图控制器
 * 
 * 该类负责：
 * 1. 显示支付渠道和支付方式选择界面
 * 2. 处理用户输入的金额和描述
 * 3. 根据用户选择启动相应的支付流程
 * 4. 接收支付结果并显示
 */
class MainViewController: UIViewController {
    
    // MARK: - UI 元素
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "巴基斯坦支付集成"
        label.font = UIFont.boldSystemFont(ofSize: 24)
        label.textAlignment = .center
        label.textColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0) // #3F51B5
        return label
    }()
    
    private lazy var paymentChannelLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "选择支付渠道："
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0) // #3F51B5
        return label
    }()
    
    private lazy var channelSegmentedControl: UISegmentedControl = {
        let items = ["JazzCash", "Easypaisa"]
        let segmentedControl = UISegmentedControl(items: items)
        segmentedControl.translatesAutoresizingMaskIntoConstraints = false
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.addTarget(self, action: #selector(channelChanged), for: .valueChanged)
        return segmentedControl
    }()
    
    private lazy var amountTextField: UITextField = {
        let textField = UITextField()
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.placeholder = "支付金额（PKR）"
        textField.text = "100"
        textField.borderStyle = .roundedRect
        textField.keyboardType = .decimalPad
        textField.layer.borderColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0).cgColor
        textField.layer.borderWidth = 1.0
        textField.layer.cornerRadius = 8.0
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 10, height: textField.frame.height))
        textField.leftView = paddingView
        textField.leftViewMode = .always
        return textField
    }()
    
    private lazy var descriptionTextField: UITextField = {
        let textField = UITextField()
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.placeholder = "交易描述"
        textField.text = "测试交易"
        textField.borderStyle = .roundedRect
        textField.layer.borderColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0).cgColor
        textField.layer.borderWidth = 1.0
        textField.layer.cornerRadius = 8.0
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 10, height: textField.frame.height))
        textField.leftView = paddingView
        textField.leftViewMode = .always
        return textField
    }()
    
    private lazy var txnTypeLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "交易类型："
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0) // #3F51B5
        return label
    }()
    
    private lazy var txnTypeSegmentedControl: UISegmentedControl = {
        let items = ["MPAY", "MWALLET", "MIGS"]
        let segmentedControl = UISegmentedControl(items: items)
        segmentedControl.translatesAutoresizingMaskIntoConstraints = false
        segmentedControl.selectedSegmentIndex = 0
        return segmentedControl
    }()
    
    private lazy var paymentMethodLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "选择支付方式："
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.textColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0) // #3F51B5
        return label
    }()
    
    private lazy var apiButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("API支付（仅钱包）", for: .normal)
        button.backgroundColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0) // #3F51B5
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(onApiPaymentClick), for: .touchUpInside)
        return button
    }()
    
    private lazy var redirectButton: UIButton = {
        let button = UIButton(type: .system)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.setTitle("页面重定向支付（银行卡）", for: .normal)
        button.backgroundColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0) // #3F51B5
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: #selector(onRedirectPaymentClick), for: .touchUpInside)
        return button
    }()
    
    private lazy var resultLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "支付结果将在这里显示"
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var cardView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        view.layer.shadowOpacity = 0.1
        return view
    }()
    
    // MARK: - 属性
    
    private var currentTxnRefNo: String? = nil
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        title = "支付集成"
        view.backgroundColor = UIColor(red: 0.96, green: 0.96, blue: 0.96, alpha: 1.0) // #F5F5F5
        
        setupUI()
        setupGestures()
        setupNotifications()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 私有方法
    
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(cardView)
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(paymentChannelLabel)
        cardView.addSubview(channelSegmentedControl)
        cardView.addSubview(amountTextField)
        cardView.addSubview(descriptionTextField)
        cardView.addSubview(txnTypeLabel)
        cardView.addSubview(txnTypeSegmentedControl)
        cardView.addSubview(paymentMethodLabel)
        cardView.addSubview(apiButton)
        cardView.addSubview(redirectButton)
        cardView.addSubview(resultLabel)
        
        NSLayoutConstraint.activate([
            // 滚动视图
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            
            // 内容视图
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 卡片视图
            cardView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            cardView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            cardView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            cardView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            
            // 标题标签
            titleLabel.topAnchor.constraint(equalTo: cardView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            
            // 支付渠道标签
            paymentChannelLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 20),
            paymentChannelLabel.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            paymentChannelLabel.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            
            // 渠道分段控件
            channelSegmentedControl.topAnchor.constraint(equalTo: paymentChannelLabel.bottomAnchor, constant: 8),
            channelSegmentedControl.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            channelSegmentedControl.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            
            // 金额输入框
            amountTextField.topAnchor.constraint(equalTo: channelSegmentedControl.bottomAnchor, constant: 20),
            amountTextField.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            amountTextField.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            amountTextField.heightAnchor.constraint(equalToConstant: 44),
            
            // 描述输入框
            descriptionTextField.topAnchor.constraint(equalTo: amountTextField.bottomAnchor, constant: 16),
            descriptionTextField.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            descriptionTextField.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            descriptionTextField.heightAnchor.constraint(equalToConstant: 44),
            
            // 交易类型标签
            txnTypeLabel.topAnchor.constraint(equalTo: descriptionTextField.bottomAnchor, constant: 16),
            txnTypeLabel.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            txnTypeLabel.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            
            // 交易类型分段控件
            txnTypeSegmentedControl.topAnchor.constraint(equalTo: txnTypeLabel.bottomAnchor, constant: 8),
            txnTypeSegmentedControl.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            txnTypeSegmentedControl.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            
            // 支付方式标签
            paymentMethodLabel.topAnchor.constraint(equalTo: txnTypeSegmentedControl.bottomAnchor, constant: 24),
            paymentMethodLabel.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            paymentMethodLabel.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            
            // API支付按钮
            apiButton.topAnchor.constraint(equalTo: paymentMethodLabel.bottomAnchor, constant: 16),
            apiButton.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            apiButton.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            apiButton.heightAnchor.constraint(equalToConstant: 50),
            
            // 重定向支付按钮
            redirectButton.topAnchor.constraint(equalTo: apiButton.bottomAnchor, constant: 16),
            redirectButton.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            redirectButton.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            redirectButton.heightAnchor.constraint(equalToConstant: 50),
            
            // 结果标签
            resultLabel.topAnchor.constraint(equalTo: redirectButton.bottomAnchor, constant: 24),
            resultLabel.leadingAnchor.constraint(equalTo: cardView.leadingAnchor, constant: 20),
            resultLabel.trailingAnchor.constraint(equalTo: cardView.trailingAnchor, constant: -20),
            resultLabel.bottomAnchor.constraint(equalTo: cardView.bottomAnchor, constant: -20)
        ])
    }
    
    private func setupGestures() {
        // 添加点击空白处收起键盘的手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        view.addGestureRecognizer(tapGesture)
    }
    
    private func setupNotifications() {
        // 注册支付结果通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePaymentResult(_:)),
            name: NSNotification.Name("JazzCashPaymentResult"),
            object: nil
        )
        
        // 注册API支付结果通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleApiPaymentResult(_:)),
            name: NSNotification.Name("JazzCashApiPaymentResult"),
            object: nil
        )
        
        // 注册Easypaisa支付结果通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleEasypaisaPaymentResult(_:)),
            name: NSNotification.Name("EasypaisaPaymentResult"),
            object: nil
        )
        
        // 注册Easypaisa API支付结果通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleEasypaisaApiPaymentResult(_:)),
            name: NSNotification.Name("EasypaisaApiPaymentResult"),
            object: nil
        )
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    @objc private func channelChanged() {
        // 根据选择的支付渠道更新UI
        updateUIForSelectedChannel()
    }
    
    private func updateUIForSelectedChannel() {
        let selectedChannel = channelSegmentedControl.selectedSegmentIndex == 0 ? "JazzCash" : "Easypaisa"
        
        // 两种支付渠道都支持API支付
        apiButton.isEnabled = true
        apiButton.alpha = 1.0
        
        // 交易类型控件只在JazzCash时显示
        txnTypeLabel.isHidden = selectedChannel != "JazzCash"
        txnTypeSegmentedControl.isHidden = selectedChannel != "JazzCash"
        
        // 更新按钮颜色
        if selectedChannel == "JazzCash" {
            apiButton.backgroundColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0) // JazzCash蓝色
            redirectButton.backgroundColor = UIColor(red: 0.24, green: 0.32, blue: 0.71, alpha: 1.0)
        } else {
            apiButton.backgroundColor = UIColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 1.0) // Easypaisa绿色
            redirectButton.backgroundColor = UIColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 1.0)
        }
    }
    
    private func validateInputs() -> Bool {
        guard let amount = amountTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !amount.isEmpty else {
            showError(title: "输入错误", message: "请输入金额")
            return false
        }
        
        if let amountValue = Double(amount), amountValue <= 0 {
            showError(title: "输入错误", message: "金额必须大于0")
            return false
        }
        
        guard let description = descriptionTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !description.isEmpty else {
            showError(title: "输入错误", message: "请输入描述")
            return false
        }
        
        return true
    }
    
    @objc private func onApiPaymentClick() {
        // 验证输入
        guard validateInputs() else { return }
        
        // 获取输入值
        let amount = amountTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let description = descriptionTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        
        // 获取选择的支付渠道
        let selectedChannel = channelSegmentedControl.selectedSegmentIndex == 0 ? "JazzCash" : "Easypaisa"
        
        if selectedChannel == "JazzCash" {
            // 创建JazzCash API支付视图控制器
            let apiPaymentVC = JazzCashApiPaymentViewController()
            apiPaymentVC.amount = amount
            apiPaymentVC.descriptionText = description
            
            // 导航到API支付页面
            navigationController?.pushViewController(apiPaymentVC, animated: true)
        } else {
            // 创建Easypaisa API支付视图控制器
            let easypaisaApiVC = EasypaisaApiPaymentViewController()
            easypaisaApiVC.amount = amount
            easypaisaApiVC.descriptionText = description
            
            // 导航到Easypaisa API支付页面
            navigationController?.pushViewController(easypaisaApiVC, animated: true)
        }
    }
    
    @objc private func onRedirectPaymentClick() {
        // 验证输入
        guard validateInputs() else { return }
        
        // 获取输入值
        let amount = amountTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let description = descriptionTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        
        // 获取选择的支付渠道
        let selectedChannel = channelSegmentedControl.selectedSegmentIndex == 0 ? "JazzCash" : "Easypaisa"
        
        if selectedChannel == "JazzCash" {
            // 生成交易参考号
            currentTxnRefNo = PaymentUtils.JazzCash.generateTxnRefNo()
            
            // 生成交易日期时间
            let txnDateTime = PaymentUtils.JazzCash.generateTxnDateTime()
            
            // 生成交易过期时间
            let txnExpiryDateTime = PaymentUtils.JazzCash.generateExpiryDateTime()
            
            // 将金额转换为分（乘以100）
            let amountInCents = String(Int((Double(amount) ?? 0) * 100))
            
            // 获取选择的交易类型
            let txnType: String
            switch txnTypeSegmentedControl.selectedSegmentIndex {
            case 0:
                txnType = "MPAY"  // 移动支付
            case 1:
                txnType = "MWALLET"  // 钱包支付
            case 2:
                txnType = "MIGS"  // 信用卡支付
            default:
                txnType = "MIGS"  // 默认为信用卡支付，与Android对齐
            }
            
            // 生成不包含小数点的账单参考号
            let billReference = "billRef" + String(Int(Date().timeIntervalSince1970))
            
            // 准备支付请求数据
            var postData = PaymentUtils.JazzCash.preparePostData(
                amount: amountInCents,
                billReference: billReference,
                description: description,
                txnDateTime: txnDateTime,
                txnExpiryDateTime: txnExpiryDateTime,
                txnRefNo: currentTxnRefNo!
            )
            
            // 添加交易类型
            postData["pp_TxnType"] = txnType
            
            // 创建支付视图控制器
            let paymentVC = JazzCashViewController()
            paymentVC.postData = postData
            paymentVC.txnRefNo = currentTxnRefNo
            paymentVC.txnType = txnType
            
            // 导航到支付页面
            navigationController?.pushViewController(paymentVC, animated: true)
        } else {
            // Easypaisa页面重定向支付
            let mobileNumber = "03123456789" // 在实际应用中，应该从用户输入获取
            let email = "<EMAIL>" // 在实际应用中，应该从用户输入获取
            
            // 创建Easypaisa支付视图控制器
            let easypaisaVC = EasypaisaViewController()
            easypaisaVC.amount = amount
            easypaisaVC.descriptionText = description
            easypaisaVC.mobileNumber = mobileNumber
            easypaisaVC.email = email
            
            // 导航到Easypaisa支付页面
            navigationController?.pushViewController(easypaisaVC, animated: true)
        }
    }
    
    @objc private func handlePaymentResult(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let success = userInfo["success"] as? Bool,
              let txnRefNo = userInfo["txnRefNo"] as? String else {
            return
        }
        
        if success {
            let responseCode = userInfo["responseCode"] as? String ?? "未知"
            let responseMessage = userInfo["responseMessage"] as? String ?? "交易成功"
            
            // 构建成功消息，格式更友好
            let successMessage = """
            JazzCash支付成功
            交易参考号: \(txnRefNo)
            响应码: \(responseCode)
            消息: \(responseMessage)
            """
            
            resultLabel.text = successMessage
            resultLabel.textColor = .systemGreen
            
            // 显示成功提示
            showToast(message: "支付成功！", type: .success)
        } else {
            let error = userInfo["error"] as? String ?? "支付失败或被取消"
            
            // 构建失败消息，格式更友好
            let failMessage = """
            JazzCash支付失败
            交易参考号: \(txnRefNo)
            错误: \(error)
            """
            
            resultLabel.text = failMessage
            resultLabel.textColor = .systemRed
            
            // 显示失败提示
            showToast(message: "支付失败：\(error)", type: .error)
        }
        
        // 滚动到结果显示区域
        scrollToResultArea()
    }
    
    // 添加一个辅助方法用于滚动到结果区域
    private func scrollToResultArea() {
        let resultFrame = resultLabel.convert(resultLabel.bounds, to: scrollView)
        scrollView.scrollRectToVisible(resultFrame, animated: true)
    }
    
    // 添加Toast消息提示
    private enum ToastType {
        case success
        case error
        case info
    }
    
    private func showToast(message: String, type: ToastType) {
        // 创建toast视图
        let toastView = UIView()
        toastView.translatesAutoresizingMaskIntoConstraints = false
        toastView.layer.cornerRadius = 10
        toastView.clipsToBounds = true
        
        // 根据类型设置颜色
        switch type {
        case .success:
            toastView.backgroundColor = UIColor(red: 0.2, green: 0.7, blue: 0.2, alpha: 0.9)
        case .error:
            toastView.backgroundColor = UIColor(red: 0.9, green: 0.2, blue: 0.2, alpha: 0.9)
        case .info:
            toastView.backgroundColor = UIColor(red: 0.2, green: 0.4, blue: 0.9, alpha: 0.9)
        }
        
        // 创建消息标签
        let messageLabel = UILabel()
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        messageLabel.textColor = .white
        messageLabel.text = message
        messageLabel.numberOfLines = 0
        messageLabel.textAlignment = .center
        
        // 添加到视图层次结构
        toastView.addSubview(messageLabel)
        view.addSubview(toastView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            messageLabel.topAnchor.constraint(equalTo: toastView.topAnchor, constant: 12),
            messageLabel.bottomAnchor.constraint(equalTo: toastView.bottomAnchor, constant: -12),
            messageLabel.leadingAnchor.constraint(equalTo: toastView.leadingAnchor, constant: 16),
            messageLabel.trailingAnchor.constraint(equalTo: toastView.trailingAnchor, constant: -16),
            
            toastView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            toastView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            toastView.widthAnchor.constraint(lessThanOrEqualTo: view.widthAnchor, constant: -40)
        ])
        
        // 动画显示
        toastView.alpha = 0
        UIView.animate(withDuration: 0.3, animations: {
            toastView.alpha = 1
        }) { _ in
            // 2秒后消失
            UIView.animate(withDuration: 0.3, delay: 2.0, options: [], animations: {
                toastView.alpha = 0
            }) { _ in
                toastView.removeFromSuperview()
            }
        }
    }
    
    @objc private func handleApiPaymentResult(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let success = userInfo["success"] as? Bool,
              let txnRefNo = userInfo["txnRefNo"] as? String else {
            return
        }
        
        if success {
            let message = userInfo["message"] as? String ?? "交易成功"
            
            resultLabel.text = "JazzCash API支付成功\n交易参考号: \(txnRefNo)\n消息: \(message)"
            resultLabel.textColor = .systemGreen
        } else {
            let message = userInfo["message"] as? String ?? "支付失败或被取消"
            
            resultLabel.text = "JazzCash API支付失败\n交易参考号: \(txnRefNo)\n消息: \(message)"
            resultLabel.textColor = .systemRed
        }
    }
    
    @objc private func handleEasypaisaPaymentResult(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let success = userInfo["success"] as? Bool,
              let txnRefNo = userInfo["txnRefNo"] as? String else {
            return
        }
        
        if success {
            let message = userInfo["message"] as? String ?? "交易成功"
            
            resultLabel.text = "Easypaisa支付成功\n交易参考号: \(txnRefNo)\n消息: \(message)"
            resultLabel.textColor = .systemGreen
        } else {
            let message = userInfo["message"] as? String ?? "支付失败或被取消"
            
            resultLabel.text = "Easypaisa支付失败\n交易参考号: \(txnRefNo)\n消息: \(message)"
            resultLabel.textColor = .systemRed
        }
    }
    
    @objc private func handleEasypaisaApiPaymentResult(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let success = userInfo["success"] as? Bool,
              let txnRefNo = userInfo["txnRefNo"] as? String else {
            return
        }
        
        if success {
            let message = userInfo["message"] as? String ?? "交易成功"
            
            resultLabel.text = "Easypaisa API支付成功\n交易参考号: \(txnRefNo)\n消息: \(message)"
            resultLabel.textColor = .systemGreen
        } else {
            let message = userInfo["message"] as? String ?? "支付失败或被取消"
            
            resultLabel.text = "Easypaisa API支付失败\n交易参考号: \(txnRefNo)\n消息: \(message)"
            resultLabel.textColor = .systemRed
        }
    }
    
    private func showError(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default, handler: nil))
        present(alert, animated: true, completion: nil)
    }
} 