# 巴基斯坦支付流程说明

本文档详细说明了巴基斯坦两大支付平台 JazzCash 和 Easypaisa 的支付流程、特点及实现差异。

## 目录

- [支付平台概述](#支付平台概述)
- [JazzCash 支付流程](#jazzcash-支付流程)
  - [网页支付流程](#jazzcash-网页支付流程)
  - [API 支付流程](#jazzcash-api-支付流程)
- [Easypaisa 支付流程](#easypaisa-支付流程)
  - [网页支付流程](#easypaisa-网页支付流程)
  - [API 支付流程](#easypaisa-api-支付流程)
- [两种支付平台的对比](#两种支付平台的对比)
- [实现差异](#实现差异)
- [最佳实践](#最佳实践)

## 支付平台概述

### JazzCash

JazzCash 是巴基斯坦领先的移动金融服务提供商，由 Mobilink 和 Warid Telecom 联合创建。它提供多种支付解决方案，包括移动钱包、银行卡支付、二维码支付等。

### Easypaisa

Easypaisa 是巴基斯坦最早的移动支付平台之一，由 Telenor Pakistan 和 Tameer Microfinance Bank 联合创建。它提供类似的支付解决方案，在巴基斯坦拥有广泛的用户基础。

## JazzCash 支付流程

### JazzCash 网页支付流程

JazzCash 网页支付采用重定向方式，流程如下：

1. **准备支付参数**：
   - 生成唯一交易参考号（txnRefNo）
   - 设置交易时间（txnDateTime）和过期时间（txnExpiryDateTime）
   - 准备其他必要参数（金额、描述等）

2. **生成安全哈希**：
   - 将参数按字母顺序排序
   - 连接参数值并添加完整性盐值（INTEGRITY_SALT）
   - 使用 HMAC-SHA256 算法生成哈希值

3. **构建 POST 表单**：
   - 创建包含所有参数的 HTML 表单
   - 设置表单提交到 JazzCash 支付网关

4. **加载表单到 WebView**：
   - 在 WebView 中加载 HTML 表单
   - 表单自动提交到 JazzCash 支付网关

5. **用户完成支付**：
   - 用户在 JazzCash 支付页面输入支付信息
   - 完成支付后，JazzCash 将用户重定向回应用的回调 URL

6. **处理支付结果**：
   - 解析回调 URL 中的参数
   - 根据响应代码（pp_ResponseCode）判断支付结果
   - 通知用户支付结果

### JazzCash API 支付流程

JazzCash API 支付需要通过后端服务器调用 JazzCash API，流程如下：

1. **收集用户信息**：
   - 在应用中收集用户的手机号和身份证号（CNIC）
   - 确认支付金额和描述

2. **发送请求到后端服务器**：
   - 将用户信息和支付详情发送到后端服务器
   - 后端服务器调用 JazzCash API 发起支付请求

3. **用户接收支付通知**：
   - JazzCash 向用户手机发送支付通知
   - 用户在 JazzCash 应用中确认支付

4. **查询支付状态**：
   - 应用向后端服务器查询支付状态
   - 后端服务器从 JazzCash 获取支付结果
   - 应用显示支付结果给用户

## Easypaisa 支付流程

### Easypaisa 网页支付流程

Easypaisa 网页支付流程与 JazzCash 类似，但参数和安全机制有所不同：

1. **准备支付参数**：
   - 生成唯一订单号（orderId）
   - 准备其他必要参数（金额、描述、手机号、邮箱等）

2. **生成安全哈希**：
   - 连接特定参数值（merchantId + storeId + orderId + amount + mobileAccountNo）
   - 使用商户密钥（MERCHANT_KEY）和 HMAC-SHA256 算法生成哈希值

3. **构建 POST 表单**：
   - 创建包含所有参数的 HTML 表单
   - 设置表单提交到 Easypaisa 支付网关

4. **加载表单到 WebView**：
   - 在 WebView 中加载 HTML 表单
   - 表单自动提交到 Easypaisa 支付网关

5. **用户完成支付**：
   - 用户在 Easypaisa 支付页面输入支付信息
   - 完成支付后，Easypaisa 将用户重定向回应用的回调 URL

6. **处理支付结果**：
   - 解析回调 URL 中的参数
   - 根据响应代码（responseCode）判断支付结果
   - 通知用户支付结果

### Easypaisa API 支付流程

Easypaisa API 支付流程如下：

1. **收集用户信息**：
   - 在应用中收集用户的手机号和身份证号（CNIC）
   - 确认支付金额和描述

2. **发送请求到后端服务器**：
   - 将用户信息和支付详情发送到后端服务器
   - 后端服务器调用 Easypaisa API 发起支付请求

3. **用户接收支付通知**：
   - Easypaisa 向用户手机发送支付通知
   - 用户在 Easypaisa 应用中确认支付

4. **查询支付状态**：
   - 应用向后端服务器查询支付状态
   - 后端服务器从 Easypaisa 获取支付结果
   - 应用显示支付结果给用户

## 两种支付平台的对比

| 特性 | JazzCash | Easypaisa |
|------|----------|-----------|
| **市场份额** | 约 50% | 约 45% |
| **支付方式** | 移动钱包、银行卡、二维码 | 移动钱包、银行卡、二维码 |
| **安全机制** | HMAC-SHA256，按字母顺序排序参数 | HMAC-SHA256，特定参数组合 |
| **参数格式** | pp_前缀（如 pp_Amount） | 无特定前缀 |
| **成功响应码** | 000 | 0000 |
| **品牌颜色** | 蓝色 | 绿色 |
| **集成复杂度** | 中等 | 中等 |
| **文档质量** | 较好 | 一般 |
| **测试环境** | 提供沙箱环境 | 提供沙箱环境 |

## 实现差异

### 安全哈希生成

**JazzCash**:
```swift
private static func generateSecureHash(data: [String: String]) -> String {
    // 按键排序
    let sortedData = data.sorted { $0.key < $1.key }
    
    // 构建要哈希的字符串
    let values = sortedData.map { $0.value }.joined(separator: "&")
    
    // 添加 integrity salt
    let toBeHashed = "\(INTEGRITY_SALT)&\(values)"
    
    // 使用 HMAC-SHA256 进行哈希
    // ...
}
```

**Easypaisa**:
```swift
private static func generateEasypaisaHash(data: [String: String]) -> String {
    // 构建要哈希的字符串
    let valueString = "\(data["merchantId"] ?? "")\(data["storeId"] ?? "")\(data["orderId"] ?? "")\(data["transactionAmount"] ?? "")\(data["mobileAccountNo"] ?? "")"
    
    // 使用 HMAC-SHA256 进行哈希
    // ...
}
```

### UI 差异

**JazzCash**:
- 使用蓝色作为主题色
- 参数名称使用 pp_ 前缀

**Easypaisa**:
- 使用绿色作为主题色
- 参数名称没有特定前缀

### 响应处理

**JazzCash**:
```swift
// 解析支付结果
if let responseCode = responseData["pp_ResponseCode"], responseCode == "000" {
    // 支付成功
    completePayment(success: true, message: responseMessage)
} else {
    // 支付失败
    completePayment(success: false, message: responseMessage)
}
```

**Easypaisa**:
```swift
// 解析支付结果
if let responseCode = responseData["responseCode"], responseCode == "0000" {
    // 支付成功
    completePayment(success: true, message: responseMessage)
} else {
    // 支付失败
    completePayment(success: false, message: responseMessage)
}
```

## 最佳实践

1. **错误处理**：
   - 实现网络错误检测和友好提示
   - 添加超时处理，避免用户长时间等待
   - 提供重试机制

2. **用户体验**：
   - 显示明确的加载指示器
   - 提供清晰的支付指引
   - 实时反馈支付状态

3. **安全性**：
   - 验证服务器返回的支付结果
   - 使用 HTTPS 进行通信
   - 不在客户端存储敏感信息

4. **测试**：
   - 在测试环境充分测试各种场景
   - 测试网络异常情况下的应用行为
   - 测试不同设备和系统版本 