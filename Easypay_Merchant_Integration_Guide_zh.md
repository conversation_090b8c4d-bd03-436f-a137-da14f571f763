# Easypay 商户集成指南（生产环境）

## 目录

1. [简介](#简介)
2. [插件集成](#插件集成)
   - [插件集成流程](#插件集成流程)
   - [插件集成步骤](#插件集成步骤)
   - [插件集成代码示例](#插件集成代码示例)
3. [开放API](#开放api)
   - [发起交易](#发起交易)
   - [查询交易](#查询交易)
   - [发起信用卡交易](#发起信用卡交易)
4. [即时支付通知（IPN）](#即时支付通知ipn)
5. [加密算法](#加密算法)
6. [附录](#附录)
   - [.NET 示例代码](#net-示例代码)
   - [Perl 示例代码](#perl-示例代码)
   - [Ruby 示例代码](#ruby-示例代码)
   - [Python 示例代码](#python-示例代码)
7. [沙箱环境](#沙箱环境)
8. [自定义页眉和页脚](#自定义页眉和页脚)

---

## 1. 简介

Easypay 解决方案是一种电子支付解决方案，使互联网用户能够在线进行金融交易。它可以轻松无缝地集成到任何在线网站和/或购物车中，使用户能够通过 Easypay 在线支付。用户可以使用其账户中的余额在线购买商品。Easypay 为本地内容、软件/应用开发者、电商商户提供了一个可行的在线支付方式。它类似于 PayPal，允许在线购物、收发款项，并通过用户的 PayPal 账户支付。

Easypay 解决方案基于最新的技术和工具集构建，具备安全支付系统所需的多项特性。用户无需特殊技术或营业执照，只需一个有效的邮箱即可注册 Easypay 账户。

此外，Easypay 还为代理商提供了管理/CRM 解决方案，可用于系统管理和客户支持。系统定义了不同的角色和权限，便于管理和操作。

### 1.1 目标读者与阅读建议

| 角色             | 说明                                                         |
|------------------|--------------------------------------------------------------|
| 业务             | 组织内使用或定义当前软件系统的任何角色                        |
| 开发             | 组织内创建和/或设计当前软件系统的任何角色，包括程序员、架构师、DBA |
| 测试             | 组织内确认当前或需求功能准确性的任何角色                      |
| 业务分析         | 分析当前功能和业务需求以提出新功能或增强功能的任何角色         |
| 项目/程序管理    | 决定和管理项目计划的任何角色                                 |
| 商户             | 希望在网站集成 Easypay 支付方式的任何商户                     |

---

## 2. 插件集成

Easypay 为商户提供了插件，可将 Easypay 解决方案集成到其购物网站。该插件易于集成，兼容所有类型的购物车/网站和不同浏览器、平台。

注册商户可在其在线零售网站使用该插件。Easypay 为注册商户分配唯一 ID。商户可通过该唯一 ID/店铺 ID 轻松集成插件。

集成后，插件会在结算页面展示认证表单和金额输入框，供客户支付。

### 2.1 插件集成流程

以下为通过插件进行在线支付的完整流程：

#### 2.1.1 商户账户验证

客户在商户网站选择"通过 Easypay 支付"方式。Easypay 验证商户账户和店铺信息（如店铺 ID、Easypay 账户是否有效）。如验证失败，将错误信息发送到 postBackURL。

#### 2.1.2 跳转到安全结算页面

验证通过后，客户被重定向到 Easypay 安全结算页面，页面包含：
- 手机号输入框
- 邮箱输入框
- 金额标签（显示商品金额）
- 验证码
- 提交按钮

商户网站需在跳转时通过 POST 方式向 Easypay URL 发送如下参数：店铺 ID、金额、postBackURL、订单参考号、过期时间等。

#### 2.1.3 Easypay 生成安全/认证 token

客户点击"提交"后，Easypay 进行多项校验，校验通过则生成安全 token 并发送到 postBackURL。校验失败则返回错误信息。

#### 2.1.4 商户处理安全 token

商户网站需处理收到的 token，例如：
```java
protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
    String token = request.getParameter("auth_token");
    if (!token.equals("")) {
        String postBackURL = "http://www.my-online-store.com/transaction/TransactionStatus.jsp";
        try {
            response.getWriter().write(postBackURL);
        } catch(Exception e) { e.printStackTrace(); }
    }
}
```

#### 2.1.5 带 token 的二次重定向

客户收到 token 后再次向 Easypay 发送 postBackURL 和 token，Easypay 验证 token 并向新的 postBackURL 发送交易状态、描述、订单号等参数。

#### 2.1.6 客户确认结算

Easypay 在收到客户确认后，向客户手机号和邮箱发送通知。

### 2.2 插件集成步骤

商户集成 Easypay 插件的流程如下：
- 商户通过 Easypay 代理注册账户，收到包含唯一店铺 ID 和 URL 的欢迎邮件。
- 商户登录 Easypay 门户，进入"集成指南"菜单，按步骤集成插件。
- 商户在结算页嵌入 Easypay 插件，实现"通过 Easypay 支付"功能。

#### 两步集成流程：
1. 商户向 Easypay 提交如下参数（POST 到生产环境 URL：https://easypay.easypaisa.com.pk/easypay/Index.jsf）：
   - amount（金额，必填，PKR）
   - storeId（店铺 ID，必填）
   - postBackURL（回调 URL，必填）
   - orderRefNum（订单号，必填）
   - expiryDate（过期时间，选填）
   - merchantHashedReq（加密请求，选填）
   - autoRedirect（自动重定向，必填）
   - paymentMethod（支付方式，选填）
   - emailAddr（邮箱，选填）
   - mobileNum（手机号，选填）
   - bankIdentifier（银行标识，选填）

| 参数名           | 必填 | 说明                         | 可能值/格式           |
|------------------|------|------------------------------|-----------------------|
| amount           | M    | 交易总金额（PKR）            | 数字                  |
| storeId          | M    | 店铺 ID                      | 数字                  |
| postBackURL      | M    | 回调 URL                     | 字符串                |
| orderRefNum      | M    | 订单参考号                   | 字符串                |
| expiryDate       | O    | 交易过期时间                 | YYYYMMDD HHMMSS       |
| merchantHashedReq| O    | 请求加密哈希                 | 字符串                |
| autoRedirect     | M    | 是否自动重定向               | 0/1                   |
| paymentMethod    | O    | 指定支付方式                 | OTC/MA/CC/QR          |
| emailAddr        | O    | 客户邮箱                     | <EMAIL>          |
| mobileNum        | O    | 客户手机号                   | ***********           |
| bankIdentifier   | O    | 银行标识                     | 6位字母数字           |

2. 客户完成表单后，点击"提交"按钮，Easypay 向 postBackURL 发送 auth_token。商户需再次向 Easypay 发送 auth_token 和 postBackURL（POST 到 https://easypay.easypaisa.com.pk/easypay/Confirm.jsf）。Easypay 验证 token 后，向新的 postBackURL 发送交易状态、描述、订单号。

### 2.3 插件集成代码示例

**第一步重定向表单示例：**
```html
<form action="https://easypay.easypaisa.com.pk/easypay/Index.jsf" method="POST" target="_blank">
  <input name="storeId" value="43" hidden="true"/>
  <input name="amount" value="10" hidden="true"/>
  <input name="postBackURL" value="http://www.my.online-store.com/transaction/MessageHandler" hidden="true"/>
  <input name="orderRefNum" value="1101" hidden="true"/>
  <input type="hidden" name="expiryDate" value="******** 201521">
  <input type="hidden" name="merchantHashedReq" value="askldjflaksdjflkasdf======asdfas dfkjaskdf">
  <input type="hidden" name="autoRedirect" value="0">
  <input type="hidden" name="paymentMethod" value="0">
  <input type="hidden" name="emailAddr" value="<EMAIL>">
  <input type="hidden" name="mobileNum" value="***********">
  <input type="hidden" name="bankIdentifier" value="UBL456">
  <input type="image" src="checkout-button-with-logo.png" border="0" name="pay">
</form>
```

**第二步重定向表单示例：**
```html
<form action="https://easypay.easypaisa.com.pk/easypay/Confirm.jsf" method="POST" target="_blank">
  <input name="auth_token" value="<?php echo $_GET['auth_token'] ?>" hidden="true"/>
  <input name="postBackURL" value="http://www.my.online-store.com/transaction/MessageHandler1" hidden="true"/>
  <input value="confirm" type="submit" name="pay"/>
</form>
```

**交易状态回调参数示例：**
- status：交易状态（Success/Failure）
- desc：状态码（0000/0001）
- orderRefNum：订单参考号

---

## 3. 开放API

Easypay 提供开放 API，支持 B2B 集成，允许外部合作伙伴直接调用 Easypay API 发起和查询交易，无需跳转到 Easypay 页面。

支持的 API：
1. 发起交易
2. 查询交易状态
3. 发起信用卡交易
4. 发起二维码交易（详见《Easypay QR 支付集成文档》）

通信协议为 HTTPS 上的 SOAP。WebService WSDL 地址：
https://easypay.easypaisa.com.pk/easypay-service/PartnerBusinessService/META-INF/wsdl/partner/transaction/PartnerBusinessService.wsdl

### 3.1 发起交易

**请求参数：**
| 字段名           | 说明                       | 必填 | 类型   |
|------------------|----------------------------|------|--------|
| username         | Easypay 提供的用户名       | M    | String |
| password         | Easypay 提供的加密密码     | M    | String |
| channel          | 交易渠道（如 Internet）    | M    | String |
| orderId          | 商户系统生成的订单号       | M    | String |
| storeId          | 商户注册时生成的店铺 ID    | M    | Int    |
| transactionAmount| 交易总金额                 | M    | String |
| transactionType  | 交易类型（OTC/MA/CC）      | M    | String |
| msisdn           | 客户手机号（OTC 必填）     | O/M  | String |
| mobileAccountNo  | 客户钱包号（MA 必填）      | O/M  | String |
| emailAddress     | 客户邮箱                   | O    | String |

**响应参数：**
| 字段名                   | 说明                       | 类型   |
|--------------------------|----------------------------|--------|
| responseCode             | Easypay 响应码             | String |
| orderId                  | 商户订单号                 | String |
| storeId                  | 店铺 ID                    | Int    |
| paymentToken             | OTC 交易 token             | String |
| transactionId            | MA 交易号                  | String |
| transactionDateTime      | 交易时间                   | Datetime |
| paymentTokenExiryDateTime| token 过期时间             | Datetime |

**请求/响应示例略**

### 3.2 查询交易

**请求参数：**
| 字段名      | 说明                 | 必填 | 类型   |
|-------------|----------------------|------|--------|
| username    | Easypay 用户名       | M    | String |
| password    | Easypay 密码         | M    | String |
| orderId     | 商户订单号           | M    | String |
| accountNum  | 商户账户号           | M    | String |

**响应参数略**

### 3.3 发起信用卡交易

**请求参数：**
| 字段名         | 说明                 | 必填 | 类型   |
|----------------|----------------------|------|--------|
| username       | Easypay 用户名       | M    | String |
| password       | Easypay 密码         | M    | String |
| orderId        | 商户订单号           | M    | String |
| storeId        | 店铺 ID              | M    | Int    |
| transactionAmount| 交易金额           | M    | String |
| transactionType| 交易类型（CC）       | M    | String |
| msisdn         | 客户手机号           | M    | String |
| emailAddress   | 客户邮箱             | O    | String |
| cardType       | 信用卡类型           | M    | String |
| pan            | 卡号                 | M    | String |
| expiryYear     | 有效期年份           | M    | String |
| expiryMonth    | 有效期月份           | M    | String |
| cvv2           | 卡背面 CVV           | M    | String |

**响应参数略**

---

## 4. 即时支付通知（IPN）

IPN 用于通知商户客户通过 Easypay 渠道完成的交易详情。商户可在 Easypay 门户自定义 IPN 消息内容和监听 URL。每当交易创建或状态变为"已支付"时，Easypay 会向商户的 IPN 监听 URL 发送通知。

**流程简述：**
1. 客户在商户网站发起交易
2. 客户选择支付方式（OTC/MA/CC）
3. Easypay 处理支付并更新状态
4. Easypay 向商户 IPN 监听 URL 发送 REST API URL
5. 商户访问该 URL 获取交易详情

**商户需做：**
- 在 Easypay 门户配置监听 URL 和所需 IPN 属性
- 创建监听器接收 GET 请求变量 url
- 通过 url 获取 REST API 返回的 IPN 属性

---

## 5. 加密算法

为防止参数在传输过程中被篡改，商户可用 Telenor 提供的密钥对请求参数加密。加密流程：
1. 将所有请求参数放入 Map
2. 获取所有字段名并按字母排序
3. 拼接为字符串：amount=10.0&autoRedirect=0&...&storeId=28
4. 用 AES/ECB/PKCS5Padding 算法和密钥加密，Base64 编码

---

## 6. 附录

### 6.1 .NET 示例代码
（略，见原文代码块）

### 6.2 Perl 示例代码
（略，见原文代码块）

### 6.3 Ruby 示例代码
（略，见原文代码块）

### 6.4 Python 示例代码
（略，见原文代码块）

---

## 7. 沙箱环境

Easypay 提供沙箱环境供商户集成、测试和监控交易。沙箱环境与生产环境类似，仅需上线时更换 URL。

- 商户登录 URL: https://easypaystg.easypaisa.com.pk/easypay-merchant/faces/pg/site/Login.jsf
- 插件首页: https://easypaystg.easypaisa.com.pk/easypay/Index.jsf
- 插件确认页: https://easypaystg.easypaisa.com.pk/easypay/Confirm.jsf
- WSDL: https://easypaystg.easypaisa.com.pk/easypay-service/PartnerBusinessService/META-INF/wsdl/partner/transaction/PartnerBusinessService.wsdl
- IPN REST API: https://easypaystg.easypaisa.com.pk/easypay-service/rest/v1/order-status/1000223344/998877

---

## 8. 自定义页眉和页脚

Easypay 管理员可为每个商户店铺设置自定义结算页眉和页脚（仅支持 .png 图片）。

**操作步骤：**
1. 登录 Easypay 管理后台
2. 进入"管理商户"
3. 选择商户，显示店铺列表
4. 点击"上传页眉和页脚"
5. 选择图片并关闭对话框

---

> 本文档为 Easypay 官方商户集成指南的中文翻译，仅供参考。 