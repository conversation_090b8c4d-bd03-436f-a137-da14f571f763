# 支付流程图

本文档通过流程图展示 JazzCash 和 Easypaisa 的支付流程。

## JazzCash 页面重定向支付流程

```mermaid
sequenceDiagram
    participant 客户端
    participant 服务器
    participant JazzCash
    
    客户端->>服务器: 发起支付请求
    服务器->>服务器: 生成交易参考号和时间戳
    服务器->>服务器: 生成安全哈希
    服务器->>客户端: 返回支付参数
    客户端->>JazzCash: 发送POST请求到支付网关
    JazzCash->>客户端: 显示支付页面
    客户端->>JazzCash: 用户输入支付信息
    JazzCash->>JazzCash: 处理支付
    JazzCash->>客户端: 重定向到回调URL
    客户端->>服务器: 发送支付结果
    服务器->>JazzCash: 验证支付状态
    JazzCash->>服务器: 返回状态确认
    服务器->>客户端: 返回最终支付结果
```

## JazzCash API 支付流程

```mermaid
sequenceDiagram
    participant 客户端
    participant 服务器
    participant JazzCash
    participant 用户手机
    
    客户端->>客户端: 收集用户信息(手机号、身份证号)
    客户端->>服务器: 发送支付请求
    服务器->>服务器: 生成交易参考号和时间戳
    服务器->>服务器: 生成安全哈希
    服务器->>JazzCash: 调用钱包支付API
    JazzCash->>用户手机: 发送支付确认请求
    用户手机->>JazzCash: 用户确认支付
    JazzCash->>服务器: 发送支付回调
    服务器->>客户端: 通过WebSocket发送通知
    客户端->>服务器: 查询支付状态
    服务器->>客户端: 返回最终支付结果
```

## Easypaisa 页面重定向支付流程

```mermaid
sequenceDiagram
    participant 客户端
    participant 服务器
    participant Easypaisa
    
    客户端->>服务器: 发起支付请求
    服务器->>服务器: 生成交易参考号和时间戳
    服务器->>服务器: 生成安全哈希
    服务器->>客户端: 返回支付参数
    客户端->>Easypaisa: 发送POST请求到支付网关
    Easypaisa->>客户端: 显示支付页面
    客户端->>Easypaisa: 用户输入支付信息
    Easypaisa->>Easypaisa: 处理支付
    Easypaisa->>客户端: 重定向到回调URL
    客户端->>服务器: 发送支付结果
    服务器->>Easypaisa: 验证支付状态
    Easypaisa->>服务器: 返回状态确认
    服务器->>客户端: 返回最终支付结果
```

## Easypaisa API 支付流程

```mermaid
sequenceDiagram
    participant 客户端
    participant 服务器
    participant Easypaisa
    participant 用户手机
    
    客户端->>客户端: 收集用户信息(手机号、邮箱)
    客户端->>服务器: 发送支付请求
    服务器->>服务器: 生成交易参考号和时间戳
    服务器->>服务器: 生成安全哈希
    服务器->>Easypaisa: 调用钱包支付API
    Easypaisa->>用户手机: 发送支付确认请求
    用户手机->>Easypaisa: 用户确认支付
    Easypaisa->>服务器: 发送支付回调
    服务器->>客户端: 通过WebSocket发送通知
    客户端->>服务器: 查询支付状态
    服务器->>客户端: 返回最终支付结果
```

## 移动应用集成流程

```mermaid
flowchart TD
    A[开始] --> B{选择支付方式}
    B -->|JazzCash| C[收集用户信息]
    B -->|Easypaisa| D[收集用户信息]
    
    C --> E[调用JazzCash支付接口]
    D --> F[调用Easypaisa支付接口]
    
    E --> G{支付结果}
    F --> G
    
    G -->|成功| H[更新订单状态]
    G -->|失败| I[显示错误信息]
    
    H --> J[完成支付流程]
    I --> K[重试或选择其他支付方式]
    
    K --> B
```

## 服务器端处理流程

```mermaid
flowchart TD
    A[接收支付请求] --> B[验证请求参数]
    B --> C{参数验证}
    
    C -->|失败| D[返回错误信息]
    C -->|成功| E[生成交易参考号]
    
    E --> F[生成安全哈希]
    F --> G[调用支付网关API]
    
    G --> H{API响应}
    H -->|成功| I[存储交易信息]
    H -->|失败| J[记录错误日志]
    
    I --> K[返回支付参数给客户端]
    J --> D
    
    L[接收支付回调] --> M[验证回调参数]
    M --> N{验证结果}
    
    N -->|失败| O[记录错误日志]
    N -->|成功| P[更新交易状态]
    
    P --> Q[发送通知给客户端]
    O --> R[返回错误响应]
    
    S[接收状态查询] --> T[查询交易状态]
    T --> U[返回最新状态]
``` 