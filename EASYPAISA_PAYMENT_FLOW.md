# Easypaisa 支付流程

本文档详细说明了 Easypaisa 支付的集成方式和流程。

## 1. 页面重定向方式

这种方式通过将用户重定向到 Easypaisa 支付页面来完成支付。

### 特点
- 支持银行卡支付
- 支持 Easypaisa 钱包支付
- 支持代金券支付
- 用户界面由 Easypaisa 提供

### 流程
1. **初始化支付**
   - 应用生成交易参考号、交易时间和过期时间
   - 准备支付请求数据，包括金额、描述等
   - 生成安全哈希（HMAC-SHA256）

2. **发送支付请求**
   - 应用将支付请求数据通过 POST 方法发送到 Easypaisa 支付网关
   - 用户被重定向到 Easypaisa 支付页面

3. **用户完成支付**
   - 用户在 Easypaisa 页面上输入银行卡信息或选择 Easypaisa 钱包支付
   - Easypaisa 处理支付请求

4. **接收支付结果**
   - Easypaisa 将用户重定向回应用的回调 URL
   - 应用解析回调参数，获取支付结果
   - 应用显示支付结果给用户

5. **验证支付状态**
   - 应用与服务器通信，验证支付状态
   - 服务器可以通过 Easypaisa API 查询交易状态

## 2. API 方式

这种方式通过直接调用 Easypaisa API 来完成支付，无需重定向到 Easypaisa 页面。

### 特点
- 仅支持 Easypaisa 钱包支付
- 不支持银行卡直接支付
- 代金券只能与钱包结合使用
- 用户界面由应用自己提供
- 更安全，商户凭据保存在服务器端

### 流程
1. **收集用户信息**
   - 应用收集用户手机号和邮箱
   - 应用收集支付金额和描述

2. **发送支付请求到应用服务器**
   - 应用将用户信息和支付信息发送到自己的服务器
   - 服务器生成交易参考号、交易时间和过期时间

3. **服务器调用 Easypaisa API**
   - 服务器准备 API 请求数据，包括商户信息
   - 服务器生成安全哈希（HMAC-SHA256）
   - 服务器通过 REST API 发送请求到 Easypaisa

4. **Easypaisa 处理支付请求**
   - Easypaisa 验证请求并处理支付
   - Easypaisa 向用户手机发送支付确认请求
   - 用户在手机上确认支付

5. **接收支付结果**
   - Easypaisa 通过回调通知服务器支付结果
   - 服务器处理通知并更新交易状态
   - 服务器将支付结果通知应用（可选，如使用WebSocket）

6. **确认支付状态**
   - 如果应用收到服务器的实时通知，直接显示结果
   - 如果未收到通知，应用可以主动查询服务器获取最新状态
   - 如果服务器也未收到回调，可以询问用户支付是否完成

### 回调与状态查询的关系

在理想情况下，支付完成后 Easypaisa 会立即发送回调通知到服务器，服务器可以实时获知支付状态。然而，在实际环境中可能会因为网络问题或其他原因导致：

1. **回调延迟**：Easypaisa 的回调可能会延迟几秒甚至几分钟才到达服务器。
2. **回调丢失**：在极少数情况下，回调可能完全丢失，导致服务器无法获知支付状态。
3. **网络问题**：用户完成支付后，可能因为网络问题无法接收到状态更新。

为了解决这些问题，采用多重确认机制：

- **服务器回调处理**：优先通过 Easypaisa 的回调确定支付状态
- **主动状态查询**：如果回调延迟或丢失，可以主动调用 Easypaisa 查询 API 获取状态
- **用户确认**：当系统无法确定状态时，可以询问用户支付是否完成

这种多重确认机制保证了支付流程的稳定性和可靠性。在实际应用中，大多数情况下回调能够及时到达，状态查询只是一种备用机制。

## 两种方式的对比

| 特性 | 页面重定向方式 | API 方式 |
|------|--------------|---------|
| 支持银行卡支付 | ✅ | ❌ |
| 支持钱包支付 | ✅ | ✅ |
| 支持代金券 | ✅ | ⚠️ 仅与钱包结合使用 |
| 用户界面 | Easypaisa 提供 | 应用自己提供 |
| 安全性 | 较低，商户凭据在客户端 | 较高，商户凭据在服务器端 |
| 集成复杂度 | 较低 | 较高 |
| 用户体验 | 需要跳转到外部页面 | 全程在应用内完成 |

## 选择建议

1. 如果需要支持银行卡支付，必须使用**页面重定向方式**。
2. 如果只需要支持 Easypaisa 钱包支付，且希望提供更好的用户体验和更高的安全性，推荐使用**API 方式**。
3. 对于生产环境，无论选择哪种方式，都应该使用 HTTPS 进行通信，并妥善保护商户凭据。

## 支付流程概述

Easypaisa 支付集成流程包括以下步骤：

1. **准备支付数据**：生成交易参数，包括金额、交易参考号、时间戳等
2. **生成安全哈希**：使用 HMAC-SHA256 算法对排序后的参数生成哈希值
3. **发送支付请求**：将数据发送到 Easypaisa 支付网关
4. **用户完成支付**：用户在 Easypaisa 界面上进行支付操作
5. **接收支付回调**：Easypaisa 将支付结果发送到指定的回调 URL
6. **验证支付状态**：应用向服务器验证支付状态
7. **更新订单状态**：根据验证结果更新订单状态

## 详细流程

### 1. 准备支付数据

在 `EasypaisaActivity.java` 的 `preparePostData()` 方法中，我们准备了以下支付数据：

- `storeId`：商户ID
- `amount`：交易金额
- `postBackURL`：回调URL
- `orderRefNum`：交易参考号（必须唯一）
- `expiryDate`：交易过期时间
- 其他必要参数

### 2. 生成安全哈希

在 `generateHashRequest()` 方法中，我们执行以下步骤：

1. 按照Easypaisa的要求构建哈希字符串：`amount&storeId&postBackURL&orderRefNum&integritySalt`
2. 使用 HMAC-SHA256 算法生成哈希值
3. 将哈希值添加到请求参数中

### 3. 发送支付请求

在 Android 实现中，我们使用 WebView 将请求发送到 Easypaisa 支付网关：

```java
mWebView.postUrl(PAYMENT_URL, postData.getBytes());
```

在 iOS 实现中，我们使用 WKWebView 发送请求：

```swift
webView.load(request)
```

### 4. 用户完成支付

用户在 WebView 中看到 Easypaisa 支付界面，并进行支付操作。

### 5. 接收支付回调

Easypaisa 完成支付后，会重定向到我们指定的回调 URL。在客户端实现中，我们通过监听 WebView 的 URL 变化来捕获这个回调：

```java
@Override
public boolean shouldOverrideUrlLoading(WebView view, String url) {
    if (url.startsWith(RETURN_URL)) {
        processCallbackUrl(url);
        return true;
    }
    return false;
}
```

### 6. 验证支付状态

在服务器端，我们通过调用 Easypaisa 查询 API 来验证支付状态：

```java
public Map<String, Object> inquireTransaction(String txnRefNo) {
    // 准备请求数据
    Map<String, String> requestData = new HashMap<>();
    requestData.put("storeId", merchantId);
    requestData.put("orderId", txnRefNo);
    
    // 生成安全哈希
    String hashRequest = generateHashRequest(requestData);
    requestData.put("merchantHashedReq", hashRequest);
    
    // 发送请求
    ResponseEntity<String> response = restTemplate.exchange(
            EASYPAISA_INQUIRY_URL,
            HttpMethod.POST,
            entity,
            String.class
    );
    
    // 解析响应
    Map<String, Object> responseMap = gson.fromJson(response.getBody(), Map.class);
    
    // 返回结果
    return responseMap;
}
```

### 7. 更新订单状态

在服务器端，`EasypaisaCallbackController` 处理支付回调并更新订单状态：

```java
private String processCallback(Map<String, String> params, Model model) {
    String txnRefNo = params.get("orderRefNum");
    String responseCode = params.get("responseCode");
    
    boolean success = "0000".equals(responseCode);
    
    // 处理回调
    Map<String, Object> result = easypaisaApiService.handleApiCallback(params);
    
    // 根据支付结果返回不同的视图
    if (success) {
        return "payment-success";
    } else {
        return "payment-error";
    }
}
```

## 服务器端实现

服务器端需要实现以下功能：

1. **处理支付回调**：接收 Easypaisa 的支付结果回调
2. **验证支付状态**：验证支付是否成功
3. **提供 API 接口**：供客户端查询支付状态

在我们的示例中，我们使用了 Spring Boot 实现了这些功能。

## 调试技巧

1. 使用日志跟踪支付流程的每一步
2. 检查生成的安全哈希是否正确
3. 验证交易参考号是否唯一
4. 确保回调 URL 能够正确接收支付结果
5. 测试网络错误和超时情况下的处理逻辑

## 安全注意事项

1. 不要在客户端存储敏感信息，如商户密码
2. 始终在服务器端验证支付状态
3. 使用 HTTPS 进行所有通信
4. 实现交易防重复机制
5. 记录所有交易日志以便追踪问题

## Easypaisa 和 JazzCash 的比较

| 特性 | Easypaisa | JazzCash |
|------|-----------|----------|
| 市场占有率 | 较高 | 较高 |
| 支持银行卡支付 | ✅ | ✅ |
| 支持钱包支付 | ✅ | ✅ |
| 支持代金券 | ✅ | ✅ |
| 支持国际支付 | ❌ | ✅ |
| 集成复杂度 | 中等 | 中等 |
| 文档完整性 | 较好 | 较好 |
| 手续费 | 根据交易类型和金额而定 | 根据交易类型和金额而定 |

## 集成建议

1. **同时集成多种支付方式**：建议同时集成 Easypaisa 和 JazzCash，为用户提供更多选择。
2. **使用服务器端验证**：无论使用哪种支付方式，都应该在服务器端验证支付状态。
3. **实现完整的错误处理**：处理各种可能的错误情况，提供友好的错误提示。
4. **记录详细日志**：记录支付流程的每一步，以便排查问题。
5. **实现交易防重复机制**：防止用户重复支付。 