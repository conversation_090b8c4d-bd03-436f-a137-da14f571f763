#!/usr/bin/python3
"""
简单的MCP服务器 - 直接实现，不依赖HTTP服务器
用于测试Cherry Studio连接
"""

import json
import sys
import datetime
from typing import Dict, Any, Optional

class SimpleMcpServer:
    def __init__(self):
        self.request_id = 1
        
    def log_error(self, message: str):
        """记录错误到stderr"""
        print(f"[MCP] {message}", file=sys.stderr, flush=True)
    
    def handle_initialize(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理初始化请求"""
        return {
            "protocolVersion": "2025-03-26",
            "capabilities": {
                "tools": {
                    "listChanged": True
                },
                "resources": {
                    "subscribe": True,
                    "listChanged": True
                }
            },
            "serverInfo": {
                "name": "Simple Demo MCP Server",
                "version": "1.0.0"
            }
        }
    
    def handle_tools_list(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具列表请求"""
        tools = [
            {
                "name": "get_current_time",
                "description": "获取当前时间",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "add_numbers",
                "description": "计算两个数字的和",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "a": {"type": "number", "description": "第一个数字"},
                        "b": {"type": "number", "description": "第二个数字"}
                    },
                    "required": ["a", "b"]
                }
            }
        ]
        return {"tools": tools}
    
    def handle_tools_call(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具调用请求"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if tool_name == "get_current_time":
            current_time = datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
            return {
                "content": [
                    {
                        "type": "text",
                        "text": current_time
                    }
                ]
            }
        elif tool_name == "add_numbers":
            a = arguments.get("a", 0)
            b = arguments.get("b", 0)
            result = a + b
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"{a} + {b} = {result}"
                    }
                ]
            }
        else:
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"未知工具: {tool_name}"
                    }
                ],
                "isError": True
            }
    
    def handle_resources_list(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理资源列表请求"""
        resources = [
            {
                "uri": "demo://time",
                "name": "当前时间",
                "description": "获取当前系统时间",
                "mimeType": "text/plain"
            }
        ]
        return {"resources": resources}
    
    def handle_request(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理请求"""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")
        
        try:
            if method == "initialize":
                result = self.handle_initialize(params)
            elif method == "tools/list":
                result = self.handle_tools_list(params)
            elif method == "tools/call":
                result = self.handle_tools_call(params)
            elif method == "resources/list":
                result = self.handle_resources_list(params)
            else:
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": result
            }
            
        except Exception as e:
            self.log_error(f"处理请求时出错: {e}")
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
    
    def run(self):
        """运行服务器"""
        self.log_error("Simple MCP Server 启动")
        
        try:
            for line in sys.stdin:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    request = json.loads(line)
                    self.log_error(f"收到请求: {request.get('method', 'unknown')}")
                    
                    # 检查是否为通知（没有id字段）
                    if "id" not in request:
                        self.log_error(f"收到通知: {request.get('method')}")
                        continue
                    
                    response = self.handle_request(request)
                    if response:
                        print(json.dumps(response, ensure_ascii=False))
                        sys.stdout.flush()
                        self.log_error(f"发送响应: {response.get('result', {}).get('tools', 'N/A') if 'result' in response else 'error'}")
                    
                except json.JSONDecodeError as e:
                    self.log_error(f"JSON解析错误: {e}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": "Parse error"
                        }
                    }
                    print(json.dumps(error_response))
                    sys.stdout.flush()
                    
                except Exception as e:
                    self.log_error(f"处理请求时出错: {e}")
                    
        except KeyboardInterrupt:
            self.log_error("收到中断信号，退出")
        except Exception as e:
            self.log_error(f"服务器运行时出错: {e}")

def main():
    """主函数"""
    server = SimpleMcpServer()
    server.run()

if __name__ == "__main__":
    main()
