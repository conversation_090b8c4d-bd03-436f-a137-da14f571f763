<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP聊天演示</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: #4285f4;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
        }
        .chat-box {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 15px;
            display: flex;
        }
        .user-message {
            justify-content: flex-end;
        }
        .assistant-message {
            justify-content: flex-start;
        }
        .system-message {
            justify-content: center;
        }
        .message-content {
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }
        .user-message .message-content {
            background-color: #dcf8c6;
        }
        .assistant-message .message-content {
            background-color: #e5e5ea;
        }
        .system-message .message-content {
            background-color: #ffebcc;
            font-style: italic;
            font-size: 0.9em;
        }
        .input-area {
            display: flex;
            padding: 15px;
            border-top: 1px solid #eaeaea;
        }
        #message-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
            font-size: 16px;
        }
        #send-button {
            margin-left: 10px;
            padding: 10px 20px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 16px;
        }
        #send-button:hover {
            background-color: #3367d6;
        }
        .tool-call {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            padding: 8px 12px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 14px;
        }
        .tool-result {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
            padding: 8px 12px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 14px;
        }
        .error {
            color: #f5222d;
            background-color: #fff1f0;
            border-left: 4px solid #f5222d;
            padding: 8px 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            MCP聊天演示
        </div>
        <div class="chat-box" id="chat-box">
            <div class="message system-message">
                <div class="message-content">
                    欢迎使用MCP聊天演示！您可以询问关于时间、计算、用户信息、翻译和天气的问题。
                </div>
            </div>
        </div>
        <div class="input-area">
            <input type="text" id="message-input" placeholder="输入您的问题..." autocomplete="off">
            <button id="send-button">发送</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatBox = document.getElementById('chat-box');
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');

            // 发送消息
            function sendMessage() {
                const message = messageInput.value.trim();
                if (message === '') return;

                // 添加用户消息到聊天框
                addMessage(message, 'user');
                
                // 清空输入框
                messageInput.value = '';
                
                // 显示加载中
                const loadingId = showLoading();
                
                // 调用后端API
                fetch('/api/mcp-chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                })
                .then(response => response.json())
                .then(data => {
                    // 移除加载中
                    removeLoading(loadingId);
                    
                    // 处理工具调用
                    if (data.toolCalls && data.toolCalls.length > 0) {
                        data.toolCalls.forEach(toolCall => {
                            // 显示工具调用
                            addToolCall(toolCall.name, toolCall.arguments);
                            
                            // 显示工具结果
                            if (toolCall.result) {
                                addToolResult(toolCall.name, toolCall.result);
                            }
                        });
                    }
                    
                    // 添加助手回复
                    if (data.response) {
                        addMessage(data.response, 'assistant');
                    }
                    
                    // 如果有错误
                    if (data.error) {
                        addError(data.error);
                    }
                })
                .catch(error => {
                    // 移除加载中
                    removeLoading(loadingId);
                    console.error('Error:', error);
                    addError('请求失败，请重试');
                });
            }

            // 添加消息到聊天框
            function addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = text;
                
                messageDiv.appendChild(contentDiv);
                chatBox.appendChild(messageDiv);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
            }
            
            // 添加工具调用信息
            function addToolCall(name, args) {
                const toolCallDiv = document.createElement('div');
                toolCallDiv.className = 'message system-message';
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                
                const toolCallInfo = document.createElement('div');
                toolCallInfo.className = 'tool-call';
                toolCallInfo.innerHTML = `<strong>调用工具:</strong> ${name}\n<strong>参数:</strong> ${JSON.stringify(args, null, 2)}`;
                
                contentDiv.appendChild(toolCallInfo);
                toolCallDiv.appendChild(contentDiv);
                chatBox.appendChild(toolCallDiv);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
            }
            
            // 添加工具结果信息
            function addToolResult(name, result) {
                const toolResultDiv = document.createElement('div');
                toolResultDiv.className = 'message system-message';
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                
                const resultInfo = document.createElement('div');
                resultInfo.className = 'tool-result';
                resultInfo.innerHTML = `<strong>工具结果:</strong> ${name}\n${result}`;
                
                contentDiv.appendChild(resultInfo);
                toolResultDiv.appendChild(contentDiv);
                chatBox.appendChild(toolResultDiv);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
            }
            
            // 添加错误信息
            function addError(errorMessage) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'message system-message';
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                
                const errorInfo = document.createElement('div');
                errorInfo.className = 'error';
                errorInfo.textContent = `错误: ${errorMessage}`;
                
                contentDiv.appendChild(errorInfo);
                errorDiv.appendChild(contentDiv);
                chatBox.appendChild(errorDiv);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
            }
            
            // 显示加载中
            function showLoading() {
                const loadingId = 'loading-' + Date.now();
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'message system-message';
                loadingDiv.id = loadingId;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = '正在思考...';
                
                loadingDiv.appendChild(contentDiv);
                chatBox.appendChild(loadingDiv);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
                
                return loadingId;
            }
            
            // 移除加载中
            function removeLoading(loadingId) {
                const loadingDiv = document.getElementById(loadingId);
                if (loadingDiv) {
                    loadingDiv.remove();
                }
            }

            // 点击发送按钮
            sendButton.addEventListener('click', sendMessage);

            // 按Enter键发送消息
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html> 