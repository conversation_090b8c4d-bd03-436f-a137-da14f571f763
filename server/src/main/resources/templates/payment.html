<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JazzCash 模拟支付页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .payment-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .payment-logo {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .payment-details {
            border-top: 1px solid #eee;
            border-bottom: 1px solid #eee;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .payment-amount {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            color: #2c3e50;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #219653;
        }
        .btn-cancel {
            background-color: #e74c3c;
            margin-top: 10px;
        }
        .btn-cancel:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="payment-header">
            <h1>JazzCash 支付</h1>
            <p>安全、快捷的在线支付方式</p>
        </div>
        
        <div class="payment-details">
            <div class="detail-row">
                <span class="detail-label">交易编号:</span>
                <span th:text="${txnRefNo}">T20250528190930</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">商户名称:</span>
                <span>测试商户</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">日期:</span>
                <span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}">2025-05-28 19:09:30</span>
            </div>
        </div>
        
        <div class="payment-amount">
            <span>支付金额: </span>
            <span th:text="${amount + ' PKR'}">1000 PKR</span>
        </div>
        
        <form id="paymentForm" th:action="${callbackUrl}" method="post">
            <input type="hidden" name="pp_TxnRefNo" th:value="${txnRefNo}">
            <input type="hidden" name="pp_Amount" th:value="${amount}">
            <input type="hidden" name="pp_ResponseCode" id="responseCode" value="000">
            <input type="hidden" name="pp_ResponseMessage" id="responseMessage" value="交易成功">
            <input type="hidden" name="pp_Language" value="EN">
            <input type="hidden" name="pp_MerchantID" value="YOUR_MERCHANT_ID">
            <input type="hidden" name="pp_BillReference" value="billRef">
            <input type="hidden" name="pp_TxnCurrency" value="PKR">
            <input type="hidden" name="pp_TxnDateTime" th:value="${#dates.format(#dates.createNow(), 'yyyyMMddHHmmss')}">
            <input type="hidden" name="pp_TxnType" value="">
            <input type="hidden" name="pp_Version" value="1.1">
            <input type="hidden" name="ppmpf_1" value="1">
            <input type="hidden" name="ppmpf_2" value="2">
            <input type="hidden" name="ppmpf_3" value="3">
            <input type="hidden" name="ppmpf_4" value="4">
            <input type="hidden" name="ppmpf_5" value="5">
            
            <button type="button" class="btn" onclick="submitPayment(true)">确认支付</button>
            <button type="button" class="btn btn-cancel" onclick="submitPayment(false)">取消支付</button>
        </form>
    </div>
    
    <script>
        function submitPayment(isSuccess) {
            // 设置响应码和消息
            if (isSuccess) {
                document.getElementById('responseCode').value = '000';
                document.getElementById('responseMessage').value = '交易成功';
            } else {
                document.getElementById('responseCode').value = '110';
                document.getElementById('responseMessage').value = '用户取消交易';
            }
            
            // 提交表单
            document.getElementById('paymentForm').submit();
        }
    </script>
</body>
</html> 