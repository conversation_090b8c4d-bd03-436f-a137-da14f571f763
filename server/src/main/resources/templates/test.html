<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JazzCash 支付测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #2c3e50;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JazzCash 支付测试工具</h1>
        
        <div class="card">
            <h2>手动触发回调</h2>
            <p>使用此表单模拟 JazzCash 支付回调</p>
            
            <form id="callbackForm">
                <div class="form-group">
                    <label for="txnRefNo">交易参考号</label>
                    <input type="text" id="txnRefNo" name="pp_TxnRefNo" value="T20250528190930">
                </div>
                
                <div class="form-group">
                    <label for="responseCode">响应码</label>
                    <select id="responseCode" name="pp_ResponseCode">
                        <option value="000">000 - 成功</option>
                        <option value="110">110 - 失败</option>
                        <option value="112">112 - 交易超时</option>
                        <option value="124">124 - 交易被拒绝</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="responseMessage">响应消息</label>
                    <input type="text" id="responseMessage" name="pp_ResponseMessage" value="交易成功">
                </div>
                
                <div class="form-group">
                    <label for="amount">金额</label>
                    <input type="text" id="amount" name="pp_Amount" value="1000">
                </div>
                
                <button type="button" onclick="triggerCallback()">触发回调</button>
            </form>
            
            <div id="callbackResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="card">
            <h2>查询交易</h2>
            
            <div class="form-group">
                <label for="queryTxnRefNo">交易参考号</label>
                <input type="text" id="queryTxnRefNo" value="T20250528190930">
            </div>
            
            <button type="button" onclick="queryTransaction()">查询</button>
            
            <div id="queryResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="card">
            <h2>所有交易</h2>
            <button type="button" onclick="listTransactions()">列出所有交易</button>
            <div id="transactionsResult" class="result" style="display:none;"></div>
        </div>
    </div>
    
    <script>
        // 触发回调
        function triggerCallback() {
            const form = document.getElementById('callbackForm');
            const formData = new FormData(form);
            const params = new URLSearchParams();
            
            for (let pair of formData.entries()) {
                params.append(pair[0], pair[1]);
            }
            
            fetch('/test/trigger-callback', {
                method: 'POST',
                body: params,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('callbackResult');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<h3>回调结果</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                resultDiv.className = data.success ? 'result success' : 'result error';
            })
            .catch(error => {
                const resultDiv = document.getElementById('callbackResult');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<h3>错误</h3><p>' + error + '</p>';
                resultDiv.className = 'result error';
            });
        }
        
        // 查询交易
        function queryTransaction() {
            const txnRefNo = document.getElementById('queryTxnRefNo').value;
            
            fetch('/test/transaction/' + txnRefNo)
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('queryResult');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<h3>交易详情</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                resultDiv.className = data.success ? 'result success' : 'result error';
            })
            .catch(error => {
                const resultDiv = document.getElementById('queryResult');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<h3>错误</h3><p>' + error + '</p>';
                resultDiv.className = 'result error';
            });
        }
        
        // 列出所有交易
        function listTransactions() {
            fetch('/test/transactions')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('transactionsResult');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<h3>所有交易</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                const resultDiv = document.getElementById('transactionsResult');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<h3>错误</h3><p>' + error + '</p>';
                resultDiv.className = 'result error';
            });
        }
    </script>
</body>
</html> 