# Server Port Settings
server.port=8080

# SSL Settings
#server.ssl.key-store=classpath:keystore.p12
#server.ssl.key-store-password=19831004
#server.ssl.keyStoreType=PKCS12
#server.ssl.keyAlias=tomcat

# Logging Settings
logging.level.root=INFO
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.ai=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.mvc=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# CORS Settings
spring.mvc.cors.allowed-origins=*
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE
spring.mvc.cors.allowed-headers=*

# 静态资源配置 - 禁用默认的/**映射
spring.web.resources.add-mappings=false
spring.mvc.static-path-pattern=/static/**

# 调试配置
spring.mvc.log-request-details=true
spring.mvc.log-resolved-exception=true
server.error.include-stacktrace=always
server.error.include-exception=true
server.error.include-message=always

# JazzCash Settings
jazzcash.merchant.id=MC158532
jazzcash.password=0b64sxf81t
jazzcash.integrity.salt=8zzx29y512
jazzcash.return.url=https://app-api.eswap.com/swap/pay/jazzcash/notify

# OpenAI API Settings
spring.ai.openai.api-key=sk-ngjujmixtgbvndhdouduvobphamphllqxpgbzinqqqmxngit
spring.ai.openai.api-url=https://api.siliconflow.cn

# MCP安全配置
mcp.security.enabled=false
mcp.security.api-key=your-secret-api-key-here
mcp.security.username=admin
mcp.security.password=password123

# MCP传输配置
mcp.transport.stdio.enabled=true
mcp.transport.sse.enabled=true
mcp.transport.http.enabled=true

# MCP日志配置
logging.level.com.example.demo.mcp=DEBUG