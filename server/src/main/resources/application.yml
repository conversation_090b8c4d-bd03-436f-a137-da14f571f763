server:
  port: 8081

spring:
  application:
    name: ai-proxy-service
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  
  h2:
    console:
      enabled: true
      path: /h2-console

# AI模型配置
ai:
  model:
    # 这里可以配置不同的AI模型提供商
    provider: openai  # openai, claude, qwen等
    api-key: ${AI_API_KEY:your-api-key}
    base-url: ${AI_BASE_URL:https://api.openai.com/v1}
    model-name: ${AI_MODEL_NAME:gpt-3.5-turbo}

# MCP服务器配置
mcp:
  server:
    path: ${MCP_SERVER_PATH:../server/target/classes}
    main-class: com.example.demo.DemoApplication
    stdio-mode: true

# 日志配置
logging:
  level:
    com.example.aiproxy: DEBUG
    org.springframework.web: DEBUG
