<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>MCP简易聊天</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .header {
            background-color: #4285f4;
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 20px -20px;
            text-align: center;
        }
        .chat-box {
            height: 300px;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 20px;
            overflow-y: auto;
            background-color: #f9f9f9;
        }
        .input-area {
            display: flex;
        }
        #message-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #send-button {
            margin-left: 10px;
            padding: 10px 20px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .user-message {
            text-align: right;
            margin-bottom: 10px;
        }
        .user-message span {
            background-color: #dcf8c6;
            padding: 8px 12px;
            border-radius: 10px;
            display: inline-block;
            max-width: 70%;
            text-align: left;
        }
        .bot-message {
            text-align: left;
            margin-bottom: 10px;
        }
        .bot-message span {
            background-color: #e5e5ea;
            padding: 8px 12px;
            border-radius: 10px;
            display: inline-block;
            max-width: 70%;
        }
        .system-message {
            text-align: center;
            margin-bottom: 10px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>MCP简易聊天</h2>
        </div>
        <div class="chat-box" id="chat-box">
            <div class="system-message">欢迎使用MCP聊天！您可以询问时间、天气、计算等问题。</div>
        </div>
        <div class="input-area">
            <input type="text" id="message-input" placeholder="输入您的问题...">
            <button id="send-button">发送</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatBox = document.getElementById('chat-box');
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');

            // 发送消息
            function sendMessage() {
                const message = messageInput.value.trim();
                if (message === '') return;

                // 添加用户消息
                addUserMessage(message);
                
                // 清空输入框
                messageInput.value = '';
                
                // 添加系统消息
                addSystemMessage('正在处理您的请求...');
                
                // 调用API
                fetch('/api/mcp-chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                })
                .then(response => response.json())
                .then(data => {
                    // 移除系统消息
                    removeSystemMessage();
                    
                    if (data.error) {
                        addSystemMessage('错误: ' + data.error);
                    } else if (data.response) {
                        addBotMessage(data.response);
                    }
                })
                .catch(error => {
                    removeSystemMessage();
                    addSystemMessage('请求失败: ' + error.message);
                });
            }

            // 添加用户消息
            function addUserMessage(message) {
                const div = document.createElement('div');
                div.className = 'user-message';
                
                const span = document.createElement('span');
                span.textContent = message;
                
                div.appendChild(span);
                chatBox.appendChild(div);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // 添加机器人消息
            function addBotMessage(message) {
                const div = document.createElement('div');
                div.className = 'bot-message';
                
                const span = document.createElement('span');
                span.textContent = message;
                
                div.appendChild(span);
                chatBox.appendChild(div);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // 添加系统消息
            function addSystemMessage(message) {
                const div = document.createElement('div');
                div.className = 'system-message';
                div.id = 'system-message';
                div.textContent = message;
                
                chatBox.appendChild(div);
                
                // 滚动到底部
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // 移除系统消息
            function removeSystemMessage() {
                const systemMessage = document.getElementById('system-message');
                if (systemMessage) {
                    systemMessage.remove();
                }
            }

            // 点击发送按钮
            sendButton.addEventListener('click', sendMessage);

            // 按Enter键发送消息
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html> 