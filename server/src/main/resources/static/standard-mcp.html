<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>标准MCP协议演示</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .header {
            background-color: #2196F3;
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 20px -20px;
            text-align: center;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            background-color: #2196F3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #1976D2;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        input[type="text"], input[type="number"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            flex: 1;
        }
        .result-area {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #2196F3;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fff;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>标准MCP协议演示</h2>
            <p>基于JSON-RPC 2.0的Model Context Protocol实现</p>
        </div>

        <!-- 初始化部分 -->
        <div class="section">
            <h3>1. 连接初始化</h3>
            <div class="button-group">
                <button onclick="initializeMcp()">初始化MCP连接</button>
                <button onclick="getServerInfo()">获取服务器信息</button>
                <button onclick="checkHealth()">健康检查</button>
            </div>
            <div class="result-area" id="init-result">点击"初始化MCP连接"开始...</div>
        </div>

        <!-- 工具管理部分 -->
        <div class="section">
            <h3>2. 工具管理</h3>
            <div class="button-group">
                <button onclick="listTools()">获取工具列表</button>
                <button onclick="callTimeTool()">获取当前时间</button>
            </div>
            
            <div class="input-group">
                <label>数字相加:</label>
                <input type="number" id="num1" placeholder="第一个数字" value="23">
                <input type="number" id="num2" placeholder="第二个数字" value="45">
                <button onclick="callAddTool()">计算</button>
            </div>
            
            <div class="input-group">
                <label>用户信息:</label>
                <input type="text" id="userId" placeholder="用户ID" value="12345">
                <button onclick="callUserTool()">查询</button>
            </div>
            
            <div class="input-group">
                <label>文本翻译:</label>
                <input type="text" id="translateText" placeholder="要翻译的文本" value="Hello World">
                <input type="text" id="targetLang" placeholder="目标语言" value="中文">
                <button onclick="callTranslateTool()">翻译</button>
            </div>
            
            <div class="input-group">
                <label>天气查询:</label>
                <input type="text" id="cityName" placeholder="城市名称" value="北京">
                <button onclick="callWeatherTool()">查询天气</button>
            </div>
            
            <div class="result-area" id="tools-result">点击相应按钮执行工具调用...</div>
        </div>

        <!-- 资源管理部分 -->
        <div class="section">
            <h3>3. 资源管理</h3>
            <div class="button-group">
                <button onclick="listResources()">获取资源列表</button>
                <button onclick="readTimeResource()">读取时间资源</button>
                <button onclick="readWeatherResource()">读取天气资源</button>
            </div>
            
            <div class="input-group">
                <label>自定义资源URI:</label>
                <input type="text" id="resourceUri" placeholder="资源URI" value="demo://time">
                <button onclick="readCustomResource()">读取资源</button>
            </div>
            
            <div class="result-area" id="resources-result">点击相应按钮执行资源操作...</div>
        </div>

        <!-- 协议对比部分 -->
        <div class="section">
            <h3>4. 协议对比说明</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>Function Call方式 (当前旧实现)</h4>
                    <ul>
                        <li>直接调用大模型API</li>
                        <li>使用OpenAI Function Calling</li>
                        <li>工具定义硬编码在请求中</li>
                        <li>紧耦合的实现方式</li>
                        <li>依赖特定的大模型API格式</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h4>标准MCP协议 (新实现)</h4>
                    <ul>
                        <li>基于JSON-RPC 2.0标准</li>
                        <li>客户端-服务器架构</li>
                        <li>标准化的工具、资源、提示管理</li>
                        <li>松耦合、可扩展的设计</li>
                        <li>与大模型API解耦</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示结果的辅助函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : 'info');
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        // 清空结果区域
        function clearResult(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // 初始化MCP连接
        async function initializeMcp() {
            clearResult('init-result');
            showResult('init-result', '正在初始化MCP连接...');
            
            try {
                const response = await fetch('/api/standard-mcp/initialize', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const result = await response.json();
                
                if (result.success) {
                    showResult('init-result', '✓ MCP连接初始化成功', 'success');
                    showResult('init-result', `服务器信息: ${JSON.stringify(result.serverInfo, null, 2)}`);
                } else {
                    showResult('init-result', `✗ 初始化失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('init-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 获取服务器信息
        async function getServerInfo() {
            try {
                const response = await fetch('/mcp/info');
                const result = await response.json();
                showResult('init-result', `服务器信息: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                showResult('init-result', `✗ 获取服务器信息失败: ${error.message}`, 'error');
            }
        }

        // 健康检查
        async function checkHealth() {
            try {
                const response = await fetch('/mcp/health');
                const result = await response.json();
                showResult('init-result', `✓ 健康检查: ${result.status}`, 'success');
            } catch (error) {
                showResult('init-result', `✗ 健康检查失败: ${error.message}`, 'error');
            }
        }

        // 获取工具列表
        async function listTools() {
            clearResult('tools-result');
            showResult('tools-result', '正在获取工具列表...');
            
            try {
                const response = await fetch('/api/standard-mcp/tools');
                const result = await response.json();
                
                if (result.success) {
                    showResult('tools-result', '✓ 工具列表获取成功', 'success');
                    showResult('tools-result', `可用工具: ${JSON.stringify(result.tools, null, 2)}`);
                } else {
                    showResult('tools-result', `✗ 获取失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('tools-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 调用时间工具
        async function callTimeTool() {
            showResult('tools-result', '正在获取当前时间...');
            
            try {
                const response = await fetch('/api/standard-mcp/tools/time', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showResult('tools-result', `✓ 当前时间: ${JSON.stringify(result.result, null, 2)}`, 'success');
                } else {
                    showResult('tools-result', `✗ 调用失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('tools-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 调用加法工具
        async function callAddTool() {
            const num1 = document.getElementById('num1').value;
            const num2 = document.getElementById('num2').value;
            
            showResult('tools-result', `正在计算 ${num1} + ${num2}...`);
            
            try {
                const response = await fetch(`/api/standard-mcp/tools/add?a=${num1}&b=${num2}`, { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showResult('tools-result', `✓ 计算结果: ${JSON.stringify(result.result, null, 2)}`, 'success');
                } else {
                    showResult('tools-result', `✗ 计算失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('tools-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 调用用户信息工具
        async function callUserTool() {
            const userId = document.getElementById('userId').value;
            
            showResult('tools-result', `正在查询用户 ${userId} 的信息...`);
            
            try {
                const response = await fetch(`/api/standard-mcp/tools/user/${userId}`, { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showResult('tools-result', `✓ 用户信息: ${JSON.stringify(result.result, null, 2)}`, 'success');
                } else {
                    showResult('tools-result', `✗ 查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('tools-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 调用翻译工具
        async function callTranslateTool() {
            const text = document.getElementById('translateText').value;
            const targetLang = document.getElementById('targetLang').value;
            
            showResult('tools-result', `正在翻译 "${text}" 到 ${targetLang}...`);
            
            try {
                const response = await fetch(`/api/standard-mcp/tools/translate?text=${encodeURIComponent(text)}&targetLanguage=${encodeURIComponent(targetLang)}`, { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showResult('tools-result', `✓ 翻译结果: ${JSON.stringify(result.result, null, 2)}`, 'success');
                } else {
                    showResult('tools-result', `✗ 翻译失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('tools-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 调用天气工具
        async function callWeatherTool() {
            const city = document.getElementById('cityName').value;
            
            showResult('tools-result', `正在查询 ${city} 的天气...`);
            
            try {
                const response = await fetch(`/api/standard-mcp/tools/weather?city=${encodeURIComponent(city)}`, { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showResult('tools-result', `✓ 天气信息: ${JSON.stringify(result.result, null, 2)}`, 'success');
                } else {
                    showResult('tools-result', `✗ 查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('tools-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 获取资源列表
        async function listResources() {
            clearResult('resources-result');
            showResult('resources-result', '正在获取资源列表...');
            
            try {
                const response = await fetch('/api/standard-mcp/resources');
                const result = await response.json();
                
                if (result.success) {
                    showResult('resources-result', '✓ 资源列表获取成功', 'success');
                    showResult('resources-result', `可用资源: ${JSON.stringify(result.resources, null, 2)}`);
                } else {
                    showResult('resources-result', `✗ 获取失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('resources-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 读取时间资源
        async function readTimeResource() {
            showResult('resources-result', '正在读取时间资源...');
            
            try {
                const response = await fetch('/api/standard-mcp/resources/read?uri=demo://time');
                const result = await response.json();
                
                if (result.success) {
                    showResult('resources-result', `✓ 时间资源: ${JSON.stringify(result.content, null, 2)}`, 'success');
                } else {
                    showResult('resources-result', `✗ 读取失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('resources-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 读取天气资源
        async function readWeatherResource() {
            showResult('resources-result', '正在读取天气资源...');
            
            try {
                const response = await fetch('/api/standard-mcp/resources/read?uri=demo://weather');
                const result = await response.json();
                
                if (result.success) {
                    showResult('resources-result', `✓ 天气资源: ${JSON.stringify(result.content, null, 2)}`, 'success');
                } else {
                    showResult('resources-result', `✗ 读取失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('resources-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 读取自定义资源
        async function readCustomResource() {
            const uri = document.getElementById('resourceUri').value;
            
            showResult('resources-result', `正在读取资源 ${uri}...`);
            
            try {
                const response = await fetch(`/api/standard-mcp/resources/read?uri=${encodeURIComponent(uri)}`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('resources-result', `✓ 资源内容: ${JSON.stringify(result.content, null, 2)}`, 'success');
                } else {
                    showResult('resources-result', `✗ 读取失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('resources-result', `✗ 请求失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
