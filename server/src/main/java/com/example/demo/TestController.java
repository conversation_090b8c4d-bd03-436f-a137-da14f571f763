package com.example.demo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * 用于测试 JazzCash 支付回调和手动触发回调
 */
@Controller
@RequestMapping("/test")
public class TestController {
    private static final Logger logger = LoggerFactory.getLogger(TestController.class);
    
    @Autowired
    private JazzCashCallbackController callbackController;
    
    /**
     * 显示测试页面
     */
    @GetMapping("")
    public String testPage(Model model) {
        logger.info("访问测试页面");
        return "test";
    }
    
    /**
     * 手动触发回调
     */
    @PostMapping("/trigger-callback")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> triggerCallback(@RequestParam Map<String, String> params) {
        logger.info("手动触发回调，参数: {}", params);
        
        // 如果没有提供交易参考号，生成一个
        if (!params.containsKey("pp_TxnRefNo")) {
            params.put("pp_TxnRefNo", "T" + System.currentTimeMillis());
        }
        
        // 如果没有提供响应码，设置为成功
        if (!params.containsKey("pp_ResponseCode")) {
            params.put("pp_ResponseCode", "000");
        }
        
        // 如果没有提供响应消息，设置默认消息
        if (!params.containsKey("pp_ResponseMessage")) {
            params.put("pp_ResponseMessage", "测试支付成功");
        }
        
        // 如果没有提供金额，设置默认金额
        if (!params.containsKey("pp_Amount")) {
            params.put("pp_Amount", "1000");
        }
        
        // 调用回调控制器处理回调
        return callbackController.handleCallback(params, null);
    }
    
    /**
     * 查询交易状态
     */
    @GetMapping("/transaction/{txnRefNo}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getTransaction(@PathVariable String txnRefNo) {
        logger.info("查询交易: {}", txnRefNo);
        return callbackController.getTransactionStatus(txnRefNo);
    }
    
    /**
     * 列出所有交易
     */
    @GetMapping("/transactions")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> listTransactions() {
        logger.info("列出所有交易");
        return callbackController.listTransactions();
    }
    
    /**
     * 模拟支付页面
     */
    @GetMapping("/payment-page")
    public String paymentPage(Model model) {
        logger.info("访问模拟支付页面");
        
        // 添加一些默认值到模型
        model.addAttribute("txnRefNo", "T" + System.currentTimeMillis());
        model.addAttribute("amount", "1000");
        model.addAttribute("callbackUrl", "http://localhost:8080/jazzcash/callback");
        
        return "payment";
    }
} 