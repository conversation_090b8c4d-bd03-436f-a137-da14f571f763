package com.example.demo;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JazzCash 支付回调处理控制器
 * 
 * 该控制器负责处理 JazzCash 支付网关的回调请求，
 * 并验证支付状态、更新订单信息。
 */
@RestController
@RequestMapping("/jazzcash")
public class JazzCashCallbackController {
    private static final Logger logger = LoggerFactory.getLogger(JazzCashCallbackController.class);
    
    // 存储交易信息的内存缓存
    private static final ConcurrentHashMap<String, Map<String, String>> transactionCache = new ConcurrentHashMap<>();
    
    /**
     * 处理 JazzCash 支付回调
     * 
     * @param params 所有请求参数
     * @param request HTTP请求对象
     * @return 响应实体
     */
    @PostMapping("/callback")
    public ResponseEntity<Map<String, Object>> handleCallback(
            @RequestParam Map<String, String> params,
            HttpServletRequest request) {
        
        logger.info("==================== JazzCash 回调开始 ====================");
        logger.info("收到 JazzCash 回调请求: {}", params);
        
        // 记录请求头信息
        logger.info("请求头信息:");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            logger.info("    {} = {}", headerName, request.getHeader(headerName));
        }
        
        // 记录请求方法和URL
        logger.info("请求方法: {}", request.getMethod());
        logger.info("请求URL: {}", request.getRequestURL() + 
                (request.getQueryString() != null ? "?" + request.getQueryString() : ""));
        
        // 提取关键参数
        String txnRefNo = params.get("pp_TxnRefNo");
        String responseCode = params.get("pp_ResponseCode");
        String responseMessage = params.get("pp_ResponseMessage");
        String amount = params.get("pp_Amount");
        
        logger.info("交易参考号: {}, 响应码: {}, 响应消息: {}, 金额: {}", 
                txnRefNo, responseCode, responseMessage, amount);
        
        // 验证支付状态
        boolean isPaymentSuccessful = "000".equals(responseCode);
        logger.info("支付是否成功: {}", isPaymentSuccessful);
        
        // 存储交易信息
        transactionCache.put(txnRefNo, new HashMap<>(params));
        logger.info("交易信息已存储到缓存，当前缓存大小: {}", transactionCache.size());
        
        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("success", isPaymentSuccessful);
        response.put("message", isPaymentSuccessful ? "支付成功" : "支付失败: " + responseMessage);
        response.put("transactionId", txnRefNo);
        
        logger.info("支付状态: {}, 响应: {}", isPaymentSuccessful ? "成功" : "失败", response);
        logger.info("==================== JazzCash 回调结束 ====================");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理 GET 请求的回调 - JazzCash 有时会使用 GET 请求发送回调
     */
    @GetMapping("/callback")
    public ResponseEntity<Map<String, Object>> handleCallbackGet(
            @RequestParam Map<String, String> params,
            HttpServletRequest request) {
        
        logger.info("==================== JazzCash GET 回调开始 ====================");
        logger.info("收到 JazzCash GET 回调请求");
        
        // 调用 POST 处理方法处理请求
        return handleCallback(params, request);
    }
    
    /**
     * 获取交易状态
     * 
     * @param txnRefNo 交易参考号
     * @return 交易状态信息
     */
    @GetMapping("/status/{txnRefNo}")
    public ResponseEntity<Map<String, Object>> getTransactionStatus(@PathVariable String txnRefNo) {
        logger.info("查询交易状态: {}", txnRefNo);
        
        Map<String, String> transaction = transactionCache.get(txnRefNo);
        Map<String, Object> response = new HashMap<>();
        
        if (transaction != null) {
            String responseCode = transaction.get("pp_ResponseCode");
            boolean isSuccess = "000".equals(responseCode);
            
            response.put("success", isSuccess);
            response.put("status", isSuccess ? "COMPLETED" : "FAILED");
            response.put("message", transaction.get("pp_ResponseMessage"));
            response.put("amount", transaction.get("pp_Amount"));
            response.put("transactionId", txnRefNo);
            response.put("details", transaction);
            
            logger.info("找到交易: {}, 状态: {}", txnRefNo, response.get("status"));
        } else {
            response.put("success", false);
            response.put("status", "NOT_FOUND");
            response.put("message", "找不到交易记录");
            
            logger.warn("找不到交易: {}", txnRefNo);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 列出所有交易
     * 
     * @return 所有交易的列表
     */
    @GetMapping("/transactions")
    public ResponseEntity<Map<String, Object>> listTransactions() {
        logger.info("列出所有交易, 总数: {}", transactionCache.size());
        
        Map<String, Object> response = new HashMap<>();
        response.put("count", transactionCache.size());
        response.put("transactions", transactionCache);
        
        return ResponseEntity.ok(response);
    }
} 