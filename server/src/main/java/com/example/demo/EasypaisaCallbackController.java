package com.example.demo;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.Map;

/**
 * Easypaisa 回调控制器
 * 
 * 处理来自Easypaisa的支付回调
 */
@Controller
@RequestMapping("/easypaisa/callback")
public class EasypaisaCallbackController {
    private static final Logger logger = LoggerFactory.getLogger(EasypaisaCallbackController.class);
    
    @Autowired
    private EasypaisaApiService easypaisaApiService;
    
    /**
     * 处理来自Easypaisa的支付回调（GET方式）
     * 
     * @param params 回调参数
     * @param model 视图模型
     * @param request HTTP请求
     * @return 视图名称
     */
    @GetMapping("")
    public String handleCallbackGet(
            @RequestParam Map<String, String> params,
            Model model,
            HttpServletRequest request) {
        
        logger.info("收到Easypaisa GET回调: {}", params);
        
        return processCallback(params, model);
    }
    
    /**
     * 处理来自Easypaisa的支付回调（POST方式）
     * 
     * @param params 回调参数
     * @param model 视图模型
     * @param request HTTP请求
     * @return 视图名称
     */
    @PostMapping("")
    public String handleCallbackPost(
            @RequestParam Map<String, String> params,
            Model model,
            HttpServletRequest request) {
        
        logger.info("收到Easypaisa POST回调: {}", params);
        
        return processCallback(params, model);
    }
    
    /**
     * 处理回调参数并返回相应视图
     * 
     * @param params 回调参数
     * @param model 视图模型
     * @return 视图名称
     */
    private String processCallback(Map<String, String> params, Model model) {
        try {
            // 获取交易参考号和响应码
            String txnRefNo = params.get("orderRefNum");
            String responseCode = params.get("responseCode");
            
            if (txnRefNo == null || txnRefNo.isEmpty()) {
                logger.error("回调数据中没有交易参考号");
                model.addAttribute("success", false);
                model.addAttribute("message", "回调数据中没有交易参考号");
                return "payment-error";
            }
            
            boolean success = "0000".equals(responseCode);
            
            // 处理回调
            Map<String, Object> result = easypaisaApiService.handleApiCallback(params);
            
            // 准备视图数据
            model.addAttribute("success", success);
            model.addAttribute("txnRefNo", txnRefNo);
            model.addAttribute("responseCode", responseCode);
            model.addAttribute("responseMessage", params.get("responseDesc"));
            
            // 根据支付结果返回不同的视图
            if (success) {
                model.addAttribute("message", "支付成功");
                return "payment-success";
            } else {
                model.addAttribute("message", "支付失败");
                return "payment-error";
            }
        } catch (Exception e) {
            logger.error("处理回调时出错", e);
            model.addAttribute("success", false);
            model.addAttribute("message", "处理回调时出错: " + e.getMessage());
            return "payment-error";
        }
    }
} 