package com.example.demo;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Easypaisa API 控制器
 * 
 * 处理Easypaisa API相关的请求和回调
 */
@RestController
@RequestMapping("/easypaisa/api")
public class EasypaisaApiController {
    private static final Logger logger = LoggerFactory.getLogger(EasypaisaApiController.class);
    
    @Autowired
    private EasypaisaApiService easypaisaApiService;
    
    /**
     * 创建钱包支付交易
     * 
     * @param requestBody 请求体，包含手机号、邮箱、金额和描述
     * @return 支付结果
     */
    @PostMapping("/create-payment")
    public ResponseEntity<Map<String, Object>> createPayment(@RequestBody Map<String, String> requestBody) {
        logger.info("收到创建支付请求: {}", requestBody);
        
        String mobileNumber = requestBody.get("mobileNumber");
        String email = requestBody.get("email");
        String amount = requestBody.get("amount");
        String description = requestBody.get("description");
        
        // 参数验证
        Map<String, Object> validationResult = validatePaymentParameters(mobileNumber, email, amount, description);
        if (validationResult != null) {
            return ResponseEntity.badRequest().body(validationResult);
        }
        
        // 创建支付交易
        Map<String, Object> result = easypaisaApiService.createWalletPayment(mobileNumber, email, amount, description);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 处理Easypaisa API回调
     * 
     * @param params 回调参数
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/callback")
    public ResponseEntity<Map<String, Object>> handleCallback(
            @RequestParam Map<String, String> params,
            HttpServletRequest request) {
        
        logger.info("收到Easypaisa API回调: {}", params);
        
        Map<String, Object> result = easypaisaApiService.handleApiCallback(params);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 查询交易状态
     * 
     * @param txnRefNo 交易参考号
     * @return 交易状态
     */
    @GetMapping("/transaction/{txnRefNo}")
    public ResponseEntity<Map<String, Object>> getTransactionStatus(@PathVariable String txnRefNo) {
        logger.info("查询交易状态: {}", txnRefNo);
        
        Map<String, Object> result = easypaisaApiService.inquireTransaction(txnRefNo);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 列出所有交易
     * 
     * @return 所有交易的列表
     */
    @GetMapping("/transactions")
    public ResponseEntity<Map<String, Object>> listTransactions() {
        logger.info("列出所有交易");
        
        Map<String, Object> result = easypaisaApiService.getAllTransactions();
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 验证支付参数
     * 
     * @param mobileNumber 手机号
     * @param email 邮箱
     * @param amount 金额
     * @param description 描述
     * @return 如果验证失败，返回错误信息；如果验证成功，返回null
     */
    private Map<String, Object> validatePaymentParameters(String mobileNumber, String email, String amount, String description) {
        Map<String, Object> errors = new HashMap<>();
        
        if (mobileNumber == null || mobileNumber.isEmpty()) {
            errors.put("mobileNumber", "手机号不能为空");
        } else if (!mobileNumber.matches("\\d{11}")) {
            errors.put("mobileNumber", "手机号格式不正确，应为11位数字");
        }
        
        if (email == null || email.isEmpty()) {
            errors.put("email", "邮箱不能为空");
        } else if (!email.matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
            errors.put("email", "邮箱格式不正确");
        }
        
        if (amount == null || amount.isEmpty()) {
            errors.put("amount", "金额不能为空");
        } else {
            try {
                double amountValue = Double.parseDouble(amount);
                if (amountValue <= 0) {
                    errors.put("amount", "金额必须大于0");
                }
            } catch (NumberFormatException e) {
                errors.put("amount", "金额必须是有效数字");
            }
        }
        
        if (description == null || description.isEmpty()) {
            errors.put("description", "描述不能为空");
        }
        
        if (!errors.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errors", errors);
            return result;
        }
        
        return null;
    }
} 