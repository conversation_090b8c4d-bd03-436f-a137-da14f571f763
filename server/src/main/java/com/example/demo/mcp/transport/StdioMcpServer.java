package com.example.demo.mcp.transport;

import com.example.demo.mcp.protocol.*;
import com.example.demo.mcp.server.StandardMcpServer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Map;

/**
 * STDIO传输方式的MCP服务器
 * 通过标准输入输出与客户端通信
 */
@Component
public class StdioMcpServer implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(StdioMcpServer.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private StandardMcpServer mcpServer;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Override
    public void run(String... args) throws Exception {
        // 检查是否启用STDIO模式
        if (args.length > 0 && "stdio".equals(args[0])) {
            logError("启动STDIO MCP服务器");
            runStdioServer();
        } else {
            logError("未启用STDIO模式，参数: " + String.join(", ", args));
        }
    }
    
    private void runStdioServer() {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(System.in))) {
            logError("MCP STDIO服务器启动");
            
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }
                
                try {
                    // 解析JSON-RPC请求
                    @SuppressWarnings("unchecked")
                    Map<String, Object> requestMap = objectMapper.readValue(line, Map.class);
                    McpRequest request = objectMapper.convertValue(requestMap, McpRequest.class);
                    
                    logError("收到请求: " + request.getMethod());
                    
                    // 检查是否为通知（没有id字段）
                    if (request.getId() == null) {
                        logError("收到通知: " + request.getMethod());
                        handleNotification(request);
                        continue;
                    }
                    
                    // 处理请求
                    McpResponse response;

                    // 特殊处理initialize请求
                    if ("initialize".equals(request.getMethod())) {
                        response = handleInitialize(request);
                    } else {
                        response = mcpServer.handleRequest(request);
                    }

                    // 输出响应
                    String responseJson = objectMapper.writeValueAsString(response);
                    System.out.println(responseJson);
                    System.out.flush();

                    logError("发送响应: " + (response.getError() == null ? "成功" : "错误"));
                    
                } catch (Exception e) {
                    logger.error("处理STDIO请求时出错", e);
                    logError("处理请求时出错: " + e.getMessage());
                    
                    // 发送错误响应
                    McpResponse errorResponse = new McpResponse();
                    errorResponse.setError(new McpResponse.McpError(-32603, "Internal error: " + e.getMessage()));
                    
                    try {
                        String errorJson = objectMapper.writeValueAsString(errorResponse);
                        System.out.println(errorJson);
                        System.out.flush();
                    } catch (Exception ex) {
                        logger.error("发送错误响应失败", ex);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("STDIO服务器运行时出错", e);
            logError("服务器运行时出错: " + e.getMessage());
        } finally {
            logError("STDIO服务器退出");
            // 优雅关闭Spring应用
            SpringApplication.exit(applicationContext, () -> 0);
        }
    }
    
    private McpResponse handleInitialize(McpRequest request) {
        logError("处理初始化请求");

        McpResponse response = new McpResponse();
        response.setJsonrpc("2.0");
        response.setId(request.getId());

        // 创建初始化响应
        Map<String, Object> result = Map.of(
            "protocolVersion", "2025-03-26",
            "capabilities", Map.of(
                "tools", Map.of("listChanged", true),
                "resources", Map.of("listChanged", true),
                "logging", Map.of()
            ),
            "serverInfo", Map.of(
                "name", "Demo MCP Server",
                "version", "1.0.0"
            )
        );

        response.setResult(result);
        logError("初始化响应已准备");
        return response;
    }

    private void handleNotification(McpRequest request) {
        // 处理通知消息
        switch (request.getMethod()) {
            case "notifications/initialized":
                logError("客户端初始化完成");
                break;
            case "notifications/cancelled":
                logError("请求被取消");
                break;
            default:
                logError("未知通知类型: " + request.getMethod());
        }
    }
    
    private void logError(String message) {
        // 输出到stderr，避免与JSON-RPC通信混淆
        System.err.println("[MCP] " + message);
        System.err.flush();
    }
}
