package com.example.demo.mcp.server;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP服务器能力声明
 */
public class McpServerCapabilities {
    
    @JsonProperty("resources")
    private ResourceCapabilities resources;
    
    @JsonProperty("tools")
    private ToolCapabilities tools;
    
    @JsonProperty("prompts")
    private PromptCapabilities prompts;
    
    @JsonProperty("logging")
    private LoggingCapabilities logging;
    
    public McpServerCapabilities() {
        this.resources = new ResourceCapabilities();
        this.tools = new ToolCapabilities();
        this.prompts = new PromptCapabilities();
        // 暂时不包含logging能力以避免序列化问题
        // this.logging = new LoggingCapabilities();
    }
    
    public ResourceCapabilities getResources() {
        return resources;
    }
    
    public void setResources(ResourceCapabilities resources) {
        this.resources = resources;
    }
    
    public ToolCapabilities getTools() {
        return tools;
    }
    
    public void setTools(ToolCapabilities tools) {
        this.tools = tools;
    }
    
    public PromptCapabilities getPrompts() {
        return prompts;
    }
    
    public void setPrompts(PromptCapabilities prompts) {
        this.prompts = prompts;
    }
    
    public LoggingCapabilities getLogging() {
        return logging;
    }
    
    public void setLogging(LoggingCapabilities logging) {
        this.logging = logging;
    }
    
    /**
     * 资源能力
     */
    public static class ResourceCapabilities {
        @JsonProperty("subscribe")
        private boolean subscribe = true;
        
        @JsonProperty("listChanged")
        private boolean listChanged = true;
        
        public boolean isSubscribe() {
            return subscribe;
        }
        
        public void setSubscribe(boolean subscribe) {
            this.subscribe = subscribe;
        }
        
        public boolean isListChanged() {
            return listChanged;
        }
        
        public void setListChanged(boolean listChanged) {
            this.listChanged = listChanged;
        }
    }
    
    /**
     * 工具能力
     */
    public static class ToolCapabilities {
        @JsonProperty("listChanged")
        private boolean listChanged = true;
        
        public boolean isListChanged() {
            return listChanged;
        }
        
        public void setListChanged(boolean listChanged) {
            this.listChanged = listChanged;
        }
    }
    
    /**
     * 提示能力
     */
    public static class PromptCapabilities {
        @JsonProperty("listChanged")
        private boolean listChanged = true;
        
        public boolean isListChanged() {
            return listChanged;
        }
        
        public void setListChanged(boolean listChanged) {
            this.listChanged = listChanged;
        }
    }
    
    /**
     * 日志能力
     */
    public static class LoggingCapabilities {
        // 目前为空，预留扩展
        // 添加一个空的getter方法以避免序列化错误
        public String getPlaceholder() {
            return null;
        }
    }
}
