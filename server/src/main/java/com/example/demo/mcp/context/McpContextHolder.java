package com.example.demo.mcp.context;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP上下文持有者
 * 用于在请求处理过程中存储和获取上下文信息
 */
public class McpContextHolder {
    private static final ThreadLocal<Map<String, Object>> contextHolder = new ThreadLocal<>();

    /**
     * 获取当前上下文
     * @return 当前上下文Map，如果不存在则创建新的
     */
    public static Map<String, Object> getContext() {
        Map<String, Object> context = contextHolder.get();
        if (context == null) {
            context = new HashMap<>();
            contextHolder.set(context);
        }
        return context;
    }

    /**
     * 设置上下文
     * @param context 上下文Map
     */
    public static void setContext(Map<String, Object> context) {
        contextHolder.set(context);
    }

    /**
     * 清除上下文
     */
    public static void clearContext() {
        contextHolder.remove();
    }

    /**
     * 设置上下文属性
     * @param key 属性键
     * @param value 属性值
     */
    public static void setAttribute(String key, Object value) {
        getContext().put(key, value);
    }

    /**
     * 获取上下文属性
     * @param key 属性键
     * @return 属性值，如果不存在则返回null
     */
    public static Object getAttribute(String key) {
        return getContext().get(key);
    }

    /**
     * 移除上下文属性
     * @param key 属性键
     */
    public static void removeAttribute(String key) {
        getContext().remove(key);
    }
} 