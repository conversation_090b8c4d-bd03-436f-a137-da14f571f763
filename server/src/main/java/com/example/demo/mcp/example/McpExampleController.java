package com.example.demo.mcp.example;

import com.example.demo.mcp.context.McpContextHolder;
import com.example.demo.mcp.annotation.WithMcpContext;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP示例控制器
 * 演示如何使用MCP上下文
 */
@RestController
@RequestMapping("/api/mcp-example")
public class McpExampleController {

    /**
     * 设置上下文值
     * 该方法使用MCP上下文，方法执行后不清除上下文
     */
    @WithMcpContext(shared = true)
    @PostMapping("/context")
    public Map<String, Object> setContext(@RequestBody Map<String, String> payload) {
        // 获取当前请求ID
        String requestId = (String) McpContextHolder.getAttribute("requestId");
        
        // 将所有提交的参数放入上下文
        payload.forEach(McpContextHolder::setAttribute);
        
        // 返回成功信息和请求ID
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("requestId", requestId);
        response.put("message", "上下文已设置");
        return response;
    }

    /**
     * 获取上下文值
     * 该方法使用MCP上下文，继承之前设置的上下文
     */
    @WithMcpContext(shared = true)
    @GetMapping("/context")
    public Map<String, Object> getContext() {
        // 获取当前请求ID
        String requestId = (String) McpContextHolder.getAttribute("requestId");
        
        // 获取请求路径和时间戳（由拦截器设置）
        String path = (String) McpContextHolder.getAttribute("path");
        Long timestamp = (Long) McpContextHolder.getAttribute("timestamp");
        
        // 创建响应
        Map<String, Object> response = new HashMap<>();
        response.put("requestId", requestId);
        
        // 使用HashMap代替Map.of
        Map<String, Object> requestInfo = new HashMap<>();
        requestInfo.put("path", path);
        requestInfo.put("timestamp", timestamp);
        response.put("requestInfo", requestInfo);
        
        // 添加所有上下文键值对
        Map<String, Object> contextMap = new HashMap<>();
        for (String key : new String[]{"name", "age", "role"}) {
            Object value = McpContextHolder.getAttribute(key);
            if (value != null) {
                contextMap.put(key, value);
            }
        }
        response.put("context", contextMap);
        
        return response;
    }

    /**
     * 清除上下文
     * 该方法使用MCP上下文，方法执行后自动清除上下文
     */
    @WithMcpContext(shared = false)
    @DeleteMapping("/context")
    public Map<String, Object> clearContext() {
        // 获取当前请求ID
        String requestId = (String) McpContextHolder.getAttribute("requestId");
        
        // 清除上下文
        McpContextHolder.clearContext();
        
        // 返回成功信息
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("requestId", requestId);
        response.put("message", "上下文已清除");
        return response;
    }

    /**
     * 不使用MCP上下文的方法
     */
    @GetMapping("/no-context")
    public Map<String, Object> noContext() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "此方法不使用MCP上下文");
        return response;
    }
} 