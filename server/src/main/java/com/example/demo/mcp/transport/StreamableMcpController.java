package com.example.demo.mcp.transport;

import com.example.demo.mcp.protocol.*;
import com.example.demo.mcp.server.StandardMcpServer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * HTTP流式传输的MCP控制器
 * 支持流式响应和长时间运行的操作
 */
@RestController
@RequestMapping("/mcp/stream")
public class StreamableMcpController {
    
    private static final Logger logger = LoggerFactory.getLogger(StreamableMcpController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    
    @Autowired
    private StandardMcpServer mcpServer;
    
    /**
     * 流式处理MCP请求
     */
    @PostMapping(value = "/request", produces = MediaType.APPLICATION_NDJSON_VALUE)
    public ResponseEntity<StreamingResponseBody> handleStreamRequest(
            @RequestBody Map<String, Object> requestBody) {
        
        StreamingResponseBody stream = outputStream -> {
            try {
                // 转换为MCP请求
                McpRequest request = objectMapper.convertValue(requestBody, McpRequest.class);
                logger.info("收到流式MCP请求: {}", request.getMethod());
                
                // 发送开始标记
                writeJsonLine(outputStream, Map.of(
                    "type", "start",
                    "requestId", request.getId(),
                    "method", request.getMethod()
                ));
                
                // 异步处理请求
                CompletableFuture<McpResponse> future = CompletableFuture.supplyAsync(() -> {
                    return mcpServer.handleRequest(request);
                }, executorService);
                
                // 发送进度更新
                int progress = 0;
                while (!future.isDone() && progress < 100) {
                    Thread.sleep(100); // 模拟处理时间
                    progress += 10;
                    
                    writeJsonLine(outputStream, Map.of(
                        "type", "progress",
                        "requestId", request.getId(),
                        "progress", progress
                    ));
                }
                
                // 获取最终结果
                McpResponse response = future.get();
                
                // 发送最终响应
                writeJsonLine(outputStream, Map.of(
                    "type", "response",
                    "requestId", request.getId(),
                    "response", response
                ));
                
                // 发送完成标记
                writeJsonLine(outputStream, Map.of(
                    "type", "complete",
                    "requestId", request.getId()
                ));
                
            } catch (Exception e) {
                logger.error("流式处理请求时出错", e);
                try {
                    writeJsonLine(outputStream, Map.of(
                        "type", "error",
                        "error", e.getMessage()
                    ));
                } catch (IOException ioException) {
                    logger.error("发送错误消息失败", ioException);
                }
            }
        };
        
        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_NDJSON)
            .body(stream);
    }
    
    /**
     * 批量处理MCP请求
     */
    @PostMapping(value = "/batch", produces = MediaType.APPLICATION_NDJSON_VALUE)
    public ResponseEntity<StreamingResponseBody> handleBatchRequests(
            @RequestBody Map<String, Object>[] requestBodies) {
        
        StreamingResponseBody stream = outputStream -> {
            try {
                writeJsonLine(outputStream, Map.of(
                    "type", "batch_start",
                    "total", requestBodies.length
                ));
                
                for (int i = 0; i < requestBodies.length; i++) {
                    try {
                        McpRequest request = objectMapper.convertValue(requestBodies[i], McpRequest.class);
                        
                        writeJsonLine(outputStream, Map.of(
                            "type", "item_start",
                            "index", i,
                            "requestId", request.getId(),
                            "method", request.getMethod()
                        ));
                        
                        McpResponse response = mcpServer.handleRequest(request);
                        
                        writeJsonLine(outputStream, Map.of(
                            "type", "item_complete",
                            "index", i,
                            "requestId", request.getId(),
                            "response", response
                        ));
                        
                    } catch (Exception e) {
                        logger.error("处理批量请求项 {} 时出错", i, e);
                        writeJsonLine(outputStream, Map.of(
                            "type", "item_error",
                            "index", i,
                            "error", e.getMessage()
                        ));
                    }
                }
                
                writeJsonLine(outputStream, Map.of(
                    "type", "batch_complete"
                ));
                
            } catch (Exception e) {
                logger.error("批量处理请求时出错", e);
                try {
                    writeJsonLine(outputStream, Map.of(
                        "type", "batch_error",
                        "error", e.getMessage()
                    ));
                } catch (IOException ioException) {
                    logger.error("发送批量错误消息失败", ioException);
                }
            }
        };
        
        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_NDJSON)
            .body(stream);
    }
    
    /**
     * 长时间运行的工具调用
     */
    @PostMapping(value = "/long-running", produces = MediaType.APPLICATION_NDJSON_VALUE)
    public ResponseEntity<StreamingResponseBody> handleLongRunningTool(
            @RequestBody Map<String, Object> requestBody) {
        
        StreamingResponseBody stream = outputStream -> {
            try {
                McpRequest request = objectMapper.convertValue(requestBody, McpRequest.class);
                
                writeJsonLine(outputStream, Map.of(
                    "type", "start",
                    "message", "开始执行长时间运行的工具"
                ));
                
                // 模拟长时间运行的操作
                for (int i = 0; i <= 100; i += 5) {
                    Thread.sleep(200); // 模拟处理时间
                    
                    writeJsonLine(outputStream, Map.of(
                        "type", "progress",
                        "progress", i,
                        "message", "处理中... " + i + "%"
                    ));
                }
                
                // 执行实际的工具调用
                McpResponse response = mcpServer.handleRequest(request);
                
                writeJsonLine(outputStream, Map.of(
                    "type", "result",
                    "response", response
                ));
                
                writeJsonLine(outputStream, Map.of(
                    "type", "complete",
                    "message", "工具执行完成"
                ));
                
            } catch (Exception e) {
                logger.error("长时间运行工具执行失败", e);
                try {
                    writeJsonLine(outputStream, Map.of(
                        "type", "error",
                        "error", e.getMessage()
                    ));
                } catch (IOException ioException) {
                    logger.error("发送错误消息失败", ioException);
                }
            }
        };
        
        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_NDJSON)
            .body(stream);
    }
    
    /**
     * 获取流式传输状态
     */
    @GetMapping("/status")
    public ResponseEntity<Object> getStatus() {
        return ResponseEntity.ok(Map.of(
            "transport", "streamable-http",
            "activeThreads", executorService.toString(),
            "supportedFeatures", new String[]{
                "streaming-responses",
                "batch-processing", 
                "long-running-operations",
                "progress-tracking"
            }
        ));
    }
    
    /**
     * 写入JSON行到输出流
     */
    private void writeJsonLine(OutputStream outputStream, Object data) throws IOException {
        String json = objectMapper.writeValueAsString(data);
        outputStream.write(json.getBytes(StandardCharsets.UTF_8));
        outputStream.write('\n');
        outputStream.flush();
    }
}
