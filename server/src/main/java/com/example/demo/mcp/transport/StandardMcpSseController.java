package com.example.demo.mcp.transport;

import com.example.demo.mcp.protocol.*;
import com.example.demo.mcp.server.StandardMcpServer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 标准MCP SSE控制器
 * 完全符合MCP SSE传输规范
 */
@RestController
@RequestMapping("/mcp")
public class StandardMcpSseController {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardMcpSseController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicLong connectionIdGenerator = new AtomicLong(1);
    
    // 存储活跃的SSE连接
    private final Map<String, SseEmitter> activeConnections = new ConcurrentHashMap<>();
    
    @Autowired
    private StandardMcpServer mcpServer;
    
    /**
     * 标准MCP SSE端点
     * 符合MCP规范的SSE传输实现
     */
    @GetMapping(value = "/sse-standard", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @CrossOrigin(origins = "*")
    public SseEmitter connectStandard() {
        String connectionId = "std-" + connectionIdGenerator.getAndIncrement();
        
        SseEmitter emitter = new SseEmitter(0L); // 无超时
        activeConnections.put(connectionId, emitter);
        
        logger.info("标准MCP SSE连接建立: {}", connectionId);
        
        // 连接处理
        emitter.onCompletion(() -> {
            activeConnections.remove(connectionId);
            logger.info("标准MCP SSE连接完成: {}", connectionId);
        });
        
        emitter.onTimeout(() -> {
            activeConnections.remove(connectionId);
            logger.info("标准MCP SSE连接超时: {}", connectionId);
        });
        
        emitter.onError((ex) -> {
            activeConnections.remove(connectionId);
            logger.error("标准MCP SSE连接错误: " + connectionId, ex);
        });
        
        // 不立即发送任何消息，等待客户端请求
        return emitter;
    }
    
    /**
     * 处理MCP请求 - 标准SSE方式
     */
    @PostMapping(value = "/sse-standard", 
                 consumes = MediaType.APPLICATION_JSON_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    @CrossOrigin(origins = "*")
    public ResponseEntity<Object> handleStandardRequest(@RequestBody Map<String, Object> requestBody) {
        
        try {
            // 转换为MCP请求
            McpRequest request = objectMapper.convertValue(requestBody, McpRequest.class);
            logger.info("收到标准SSE MCP请求: {} (ID: {})", request.getMethod(), request.getId());
            
            // 处理请求
            McpResponse response;
            if ("initialize".equals(request.getMethod())) {
                response = handleInitialize(request);
            } else {
                response = mcpServer.handleRequest(request);
            }
            
            // 通过SSE广播响应
            broadcastToAllConnections("message", response);
            
            // 同时直接返回响应
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("处理标准SSE MCP请求时出错", e);
            return ResponseEntity.badRequest().body(Map.of("error", "处理请求失败: " + e.getMessage()));
        }
    }
    
    /**
     * 处理初始化请求
     */
    private McpResponse handleInitialize(McpRequest request) {
        logger.info("处理SSE初始化请求");
        
        McpResponse response = new McpResponse();
        response.setJsonrpc("2.0");
        response.setId(request.getId());
        
        // 创建初始化响应
        Map<String, Object> result = Map.of(
            "protocolVersion", "2025-03-26",
            "capabilities", Map.of(
                "tools", Map.of("listChanged", true),
                "resources", Map.of("listChanged", true),
                "logging", Map.of()
            ),
            "serverInfo", Map.of(
                "name", "Demo MCP Server",
                "version", "1.0.0"
            )
        );
        
        response.setResult(result);
        logger.info("SSE初始化响应已准备");
        return response;
    }
    
    /**
     * 广播消息到所有连接
     */
    private void broadcastToAllConnections(String eventName, Object data) {
        if (activeConnections.isEmpty()) {
            logger.debug("没有活跃的SSE连接");
            return;
        }
        
        for (Map.Entry<String, SseEmitter> entry : activeConnections.entrySet()) {
            try {
                entry.getValue().send(SseEmitter.event()
                    .name(eventName)
                    .data(objectMapper.writeValueAsString(data)));
                    
            } catch (IOException e) {
                logger.error("广播到连接 {} 失败", entry.getKey(), e);
                activeConnections.remove(entry.getKey());
            }
        }
    }
    
    /**
     * 获取标准SSE状态
     */
    @GetMapping("/sse-standard/status")
    @CrossOrigin(origins = "*")
    public Map<String, Object> getStandardStatus() {
        return Map.of(
            "activeConnections", activeConnections.size(),
            "connectionIds", activeConnections.keySet(),
            "transport", "sse-standard",
            "protocol", "mcp"
        );
    }
    
    /**
     * 健康检查端点
     */
    @GetMapping("/sse-standard/health")
    @CrossOrigin(origins = "*")
    public Map<String, Object> health() {
        return Map.of(
            "status", "healthy",
            "timestamp", System.currentTimeMillis(),
            "connections", activeConnections.size()
        );
    }
}
