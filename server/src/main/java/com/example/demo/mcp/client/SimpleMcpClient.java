package com.example.demo.mcp.client;

import com.example.demo.mcp.protocol.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 简单的MCP客户端实现
 * 用于与标准MCP服务器通信
 */
@Component
public class SimpleMcpClient {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleMcpClient.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RestTemplate restTemplate = new RestTemplate();
    private final AtomicLong requestIdCounter = new AtomicLong(1);
    
    private String serverUrl = "http://localhost:8080/mcp/";
    private boolean initialized = false;
    
    /**
     * 初始化与服务器的连接
     */
    public Map<String, Object> initialize() {
        try {
            McpRequest request = new McpRequest();
            request.setId(requestIdCounter.getAndIncrement());
            request.setMethod("initialize");
            
            Map<String, Object> params = new HashMap<>();
            params.put("protocolVersion", "2025-03-26");
            
            Map<String, Object> clientInfo = new HashMap<>();
            clientInfo.put("name", "Simple MCP Client");
            clientInfo.put("version", "1.0.0");
            params.put("clientInfo", clientInfo);
            
            Map<String, Object> capabilities = new HashMap<>();
            // 客户端能力声明
            capabilities.put("sampling", new HashMap<>());
            params.put("capabilities", capabilities);
            
            request.setParams(params);
            
            McpResponse response = sendRequest(request);
            if (response.getError() == null) {
                initialized = true;
                logger.info("MCP客户端初始化成功");
                return (Map<String, Object>) response.getResult();
            } else {
                logger.error("MCP客户端初始化失败: {}", response.getError().getMessage());
                return null;
            }
        } catch (Exception e) {
            logger.error("初始化MCP客户端时出错", e);
            return null;
        }
    }
    
    /**
     * 获取可用工具列表
     */
    public Map<String, Object> listTools() {
        if (!initialized) {
            throw new IllegalStateException("客户端未初始化");
        }
        
        McpRequest request = new McpRequest();
        request.setId(requestIdCounter.getAndIncrement());
        request.setMethod("tools/list");
        
        McpResponse response = sendRequest(request);
        if (response.getError() == null) {
            return (Map<String, Object>) response.getResult();
        } else {
            logger.error("获取工具列表失败: {}", response.getError().getMessage());
            return null;
        }
    }
    
    /**
     * 调用工具
     */
    public Map<String, Object> callTool(String toolName, Map<String, Object> arguments) {
        if (!initialized) {
            throw new IllegalStateException("客户端未初始化");
        }
        
        McpRequest request = new McpRequest();
        request.setId(requestIdCounter.getAndIncrement());
        request.setMethod("tools/call");
        
        Map<String, Object> params = new HashMap<>();
        params.put("name", toolName);
        params.put("arguments", arguments != null ? arguments : new HashMap<>());
        request.setParams(params);
        
        McpResponse response = sendRequest(request);
        if (response.getError() == null) {
            return (Map<String, Object>) response.getResult();
        } else {
            logger.error("调用工具失败: {}", response.getError().getMessage());
            return null;
        }
    }
    
    /**
     * 获取可用资源列表
     */
    public Map<String, Object> listResources() {
        if (!initialized) {
            throw new IllegalStateException("客户端未初始化");
        }
        
        McpRequest request = new McpRequest();
        request.setId(requestIdCounter.getAndIncrement());
        request.setMethod("resources/list");
        
        McpResponse response = sendRequest(request);
        if (response.getError() == null) {
            return (Map<String, Object>) response.getResult();
        } else {
            logger.error("获取资源列表失败: {}", response.getError().getMessage());
            return null;
        }
    }
    
    /**
     * 读取资源
     */
    public Map<String, Object> readResource(String uri) {
        if (!initialized) {
            throw new IllegalStateException("客户端未初始化");
        }
        
        McpRequest request = new McpRequest();
        request.setId(requestIdCounter.getAndIncrement());
        request.setMethod("resources/read");
        
        Map<String, Object> params = new HashMap<>();
        params.put("uri", uri);
        request.setParams(params);
        
        McpResponse response = sendRequest(request);
        if (response.getError() == null) {
            return (Map<String, Object>) response.getResult();
        } else {
            logger.error("读取资源失败: {}", response.getError().getMessage());
            return null;
        }
    }
    
    /**
     * 发送请求到MCP服务器
     */
    private McpResponse sendRequest(McpRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            String requestJson = objectMapper.writeValueAsString(request);
            HttpEntity<String> entity = new HttpEntity<>(requestJson, headers);
            
            logger.debug("发送MCP请求: {}", requestJson);
            
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(serverUrl, entity, String.class);
            String responseJson = responseEntity.getBody();
            
            logger.debug("收到MCP响应: {}", responseJson);
            
            return objectMapper.readValue(responseJson, McpResponse.class);
        } catch (Exception e) {
            logger.error("发送MCP请求时出错", e);
            McpResponse errorResponse = new McpResponse();
            errorResponse.setId(request.getId());
            errorResponse.setError(new McpResponse.McpError(-32603, "Client error: " + e.getMessage()));
            return errorResponse;
        }
    }
    
    /**
     * 设置服务器URL
     */
    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
}
