package com.example.demo.mcp.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

/**
 * MCP聊天服务
 * 处理与大模型的交互和工具调用
 */
@Service
public class McpChatService {
    private static final Logger logger = LoggerFactory.getLogger(McpChatService.class);
    private static final String MODEL_API_URL = "https://api.siliconflow.cn/v1/chat/completions";
    private static final String API_KEY = "sk-ngjujmixtgbvndhdouduvobphamphllqxpgbzinqqqmxngit"; // 替换为您的API密钥
    private static final String MODEL_NAME = "deepseek-ai/DeepSeek-V3";

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Autowired
    private DemoMcpService demoMcpService;
    
    // 保存对话历史
    private final List<Map<String, Object>> messages = new ArrayList<>();
    
    /**
     * 处理聊天请求
     * @param userMessage 用户消息
     * @return 响应结果
     */
    public Map<String, Object> processChat(String userMessage) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 初始化系统消息（如果是第一条消息）
            if (messages.isEmpty()) {
                Map<String, Object> systemMessage = new HashMap<>();
                systemMessage.put("role", "system");
                systemMessage.put("content", "我是一个智能助手，可以帮您获取实时信息。我可以查询当前时间、计算数字、获取用户信息、翻译文本和查询天气信息。");
                messages.add(systemMessage);
            }
            
            // 添加用户消息
            Map<String, Object> userMsg = new HashMap<>();
            userMsg.put("role", "user");
            userMsg.put("content", userMessage);
            messages.add(userMsg);
            
            // 调用大模型API
            JsonNode modelResponse = callModelApi();
            
            // 处理模型响应
            if (modelResponse != null && modelResponse.has("choices") && modelResponse.get("choices").isArray()) {
                JsonNode choice = modelResponse.get("choices").get(0);
                
                if (choice.has("message")) {
                    JsonNode message = choice.get("message");
                    
                    // 处理工具调用
                    List<Map<String, Object>> toolCalls = new ArrayList<>();
                    if (message.has("tool_calls") && message.get("tool_calls").isArray()) {
                        JsonNode toolCallsNode = message.get("tool_calls");
                        
                        for (JsonNode toolCall : toolCallsNode) {
                            if (toolCall.has("function")) {
                                JsonNode function = toolCall.get("function");
                                String toolName = function.has("name") ? function.get("name").asText() : "";
                                String arguments = function.has("arguments") ? function.get("arguments").asText() : "{}";
                                
                                // 修复格式错误的参数
                                arguments = fixArgumentsFormat(arguments);
                                
                                Map<String, Object> toolCallInfo = new HashMap<>();
                                toolCallInfo.put("name", toolName);
                                toolCallInfo.put("arguments", parseArguments(arguments));
                                
                                // 执行工具调用
                                String toolResult = executeToolCall(toolName, parseArguments(arguments));
                                toolCallInfo.put("result", toolResult);
                                
                                toolCalls.add(toolCallInfo);
                                
                                // 添加工具调用结果到消息历史
                                String toolCallId = toolCall.has("id") ? toolCall.get("id").asText() : UUID.randomUUID().toString();
                                
                                Map<String, Object> assistantMsg = new HashMap<>();
                                assistantMsg.put("role", "assistant");
                                assistantMsg.put("content", "");
                                
                                Map<String, Object> functionMap = new HashMap<>();
                                functionMap.put("name", toolName);
                                functionMap.put("arguments", arguments);
                                
                                Map<String, Object> toolCallMap = new HashMap<>();
                                toolCallMap.put("id", toolCallId);
                                toolCallMap.put("type", "function");
                                toolCallMap.put("function", functionMap);
                                
                                assistantMsg.put("tool_calls", Collections.singletonList(toolCallMap));
                                messages.add(assistantMsg);
                                
                                Map<String, Object> toolMsg = new HashMap<>();
                                toolMsg.put("role", "tool");
                                toolMsg.put("tool_call_id", toolCallId);
                                toolMsg.put("name", toolName);
                                toolMsg.put("content", toolResult);
                                messages.add(toolMsg);
                            }
                        }
                        
                        // 再次调用模型以获取最终回复
                        JsonNode finalResponse = callModelApi();
                        if (finalResponse != null && finalResponse.has("choices") && finalResponse.get("choices").isArray()) {
                            JsonNode finalChoice = finalResponse.get("choices").get(0);
                            if (finalChoice.has("message") && finalChoice.get("message").has("content")) {
                                String content = finalChoice.get("message").get("content").asText();
                                result.put("response", content);
                                
                                // 添加助手回复到消息历史
                                Map<String, Object> assistantReply = new HashMap<>();
                                assistantReply.put("role", "assistant");
                                assistantReply.put("content", content);
                                messages.add(assistantReply);
                            }
                        }
                    } else if (message.has("content")) {
                        // 直接回复，没有工具调用
                        String content = message.get("content").asText();
                        result.put("response", content);
                        
                        // 添加助手回复到消息历史
                        Map<String, Object> assistantReply = new HashMap<>();
                        assistantReply.put("role", "assistant");
                        assistantReply.put("content", content);
                        messages.add(assistantReply);
                    }
                    
                    result.put("toolCalls", toolCalls);
                }
            }
        } catch (Exception e) {
            logger.error("处理聊天请求时出错", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 调用大模型API
     * @return API响应
     * @throws IOException 如果API调用失败
     */
    private JsonNode callModelApi() throws IOException {
        // 构建请求体
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("model", MODEL_NAME);
        
        // 添加消息
        ArrayNode messagesNode = requestBody.putArray("messages");
        for (Map<String, Object> message : messages) {
            ObjectNode messageNode = objectMapper.createObjectNode();
            messageNode.put("role", (String) message.get("role"));
            messageNode.put("content", (String) message.get("content"));
            
            // 添加工具调用信息（如果有）
            if (message.containsKey("tool_calls")) {
                ArrayNode toolCallsNode = messageNode.putArray("tool_calls");
                List<Map<String, Object>> toolCalls = (List<Map<String, Object>>) message.get("tool_calls");
                
                for (Map<String, Object> toolCall : toolCalls) {
                    ObjectNode toolCallNode = toolCallsNode.addObject();
                    toolCallNode.put("id", (String) toolCall.get("id"));
                    toolCallNode.put("type", (String) toolCall.get("type"));
                    
                    ObjectNode functionNode = toolCallNode.putObject("function");
                    Map<String, Object> function = (Map<String, Object>) toolCall.get("function");
                    functionNode.put("name", (String) function.get("name"));
                    functionNode.put("arguments", (String) function.get("arguments"));
                }
            }
            
            // 添加工具响应信息（如果有）
            if (message.containsKey("tool_call_id")) {
                messageNode.put("tool_call_id", (String) message.get("tool_call_id"));
            }
            
            if (message.containsKey("name")) {
                messageNode.put("name", (String) message.get("name"));
            }
            
            messagesNode.add(messageNode);
        }
        
        // 添加工具定义
        ArrayNode toolsNode = requestBody.putArray("tools");
        
        // 添加get_current_time工具
        ObjectNode getCurrentTimeTool = toolsNode.addObject();
        getCurrentTimeTool.put("type", "function");
        ObjectNode getCurrentTimeFunction = getCurrentTimeTool.putObject("function");
        getCurrentTimeFunction.put("name", "get_current_time");
        getCurrentTimeFunction.put("description", "获取当前的系统时间，返回格式化的时间字符串");
        ObjectNode getCurrentTimeParams = getCurrentTimeFunction.putObject("parameters");
        getCurrentTimeParams.put("type", "object");
        getCurrentTimeParams.putObject("properties");
        getCurrentTimeParams.putArray("required");
        
        // 添加add_numbers工具
        ObjectNode addNumbersTool = toolsNode.addObject();
        addNumbersTool.put("type", "function");
        ObjectNode addNumbersFunction = addNumbersTool.putObject("function");
        addNumbersFunction.put("name", "add_numbers");
        addNumbersFunction.put("description", "计算两个数的和，返回计算结果");
        ObjectNode addNumbersParams = addNumbersFunction.putObject("parameters");
        addNumbersParams.put("type", "object");
        ObjectNode addNumbersProps = addNumbersParams.putObject("properties");
        
        ObjectNode paramA = addNumbersProps.putObject("a");
        paramA.put("type", "integer");
        paramA.put("description", "第一个数");
        
        ObjectNode paramB = addNumbersProps.putObject("b");
        paramB.put("type", "integer");
        paramB.put("description", "第二个数");
        
        ArrayNode addNumbersRequired = addNumbersParams.putArray("required");
        addNumbersRequired.add("a");
        addNumbersRequired.add("b");
        
        // 添加get_user_info工具
        ObjectNode getUserInfoTool = toolsNode.addObject();
        getUserInfoTool.put("type", "function");
        ObjectNode getUserInfoFunction = getUserInfoTool.putObject("function");
        getUserInfoFunction.put("name", "get_user_info");
        getUserInfoFunction.put("description", "根据用户ID获取用户信息");
        ObjectNode getUserInfoParams = getUserInfoFunction.putObject("parameters");
        getUserInfoParams.put("type", "object");
        ObjectNode getUserInfoProps = getUserInfoParams.putObject("properties");
        
        ObjectNode userIdParam = getUserInfoProps.putObject("userId");
        userIdParam.put("type", "string");
        userIdParam.put("description", "用户ID");
        
        ArrayNode getUserInfoRequired = getUserInfoParams.putArray("required");
        getUserInfoRequired.add("userId");
        
        // 添加translate_text工具
        ObjectNode translateTextTool = toolsNode.addObject();
        translateTextTool.put("type", "function");
        ObjectNode translateTextFunction = translateTextTool.putObject("function");
        translateTextFunction.put("name", "translate_text");
        translateTextFunction.put("description", "将文本翻译成指定的语言");
        ObjectNode translateTextParams = translateTextFunction.putObject("parameters");
        translateTextParams.put("type", "object");
        ObjectNode translateTextProps = translateTextParams.putObject("properties");
        
        ObjectNode textParam = translateTextProps.putObject("text");
        textParam.put("type", "string");
        textParam.put("description", "要翻译的文本");
        
        ObjectNode targetLangParam = translateTextProps.putObject("targetLanguage");
        targetLangParam.put("type", "string");
        targetLangParam.put("description", "目标语言，如：中文、英文、日文等");
        
        ArrayNode translateTextRequired = translateTextParams.putArray("required");
        translateTextRequired.add("text");
        translateTextRequired.add("targetLanguage");
        
        // 添加get_weather工具
        ObjectNode getWeatherTool = toolsNode.addObject();
        getWeatherTool.put("type", "function");
        ObjectNode getWeatherFunction = getWeatherTool.putObject("function");
        getWeatherFunction.put("name", "get_weather");
        getWeatherFunction.put("description", "获取指定城市的天气信息");
        ObjectNode getWeatherParams = getWeatherFunction.putObject("parameters");
        getWeatherParams.put("type", "object");
        ObjectNode getWeatherProps = getWeatherParams.putObject("properties");
        
        ObjectNode cityParam = getWeatherProps.putObject("city");
        cityParam.put("type", "string");
        cityParam.put("description", "城市名称");
        
        ArrayNode getWeatherRequired = getWeatherParams.putArray("required");
        getWeatherRequired.add("city");
        
        // 设置工具选择
        requestBody.put("tool_choice", "auto");
        
        // 设置请求头
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", "Bearer " + API_KEY);
        
        // 发送请求
        org.springframework.http.HttpEntity<String> entity = new org.springframework.http.HttpEntity<>(requestBody.toString(), headers);
        
        try {
            String response = restTemplate.postForObject(MODEL_API_URL, entity, String.class);
            return objectMapper.readTree(response);
        } catch (Exception e) {
            logger.error("调用大模型API失败", e);
            throw new IOException("调用大模型API失败: " + e.getMessage());
        }
    }
    
    /**
     * 修复参数格式
     * @param arguments 参数字符串
     * @return 修复后的参数字符串
     */
    private String fixArgumentsFormat(String arguments) {
        // 处理nullnull等特殊情况
        if ("nullnull".equals(arguments) || "null".equals(arguments)) {
            return "{}";
        }
        
        // 尝试解析为JSON
        try {
            objectMapper.readTree(arguments);
            return arguments; // 如果可以解析，则返回原始字符串
        } catch (Exception e) {
            logger.warn("参数格式错误，尝试修复: {}", arguments);
            // 如果无法解析，返回空对象
            return "{}";
        }
    }
    
    /**
     * 解析参数字符串为Map
     * @param arguments 参数字符串
     * @return 参数Map
     */
    private Map<String, Object> parseArguments(String arguments) {
        try {
            return objectMapper.readValue(arguments, Map.class);
        } catch (Exception e) {
            logger.warn("解析参数失败: {}", arguments, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 执行工具调用
     * @param toolName 工具名称
     * @param args 参数
     * @return 执行结果
     */
    private String executeToolCall(String toolName, Map<String, Object> args) {
        try {
            switch (toolName) {
                case "get_current_time":
                    return demoMcpService.getCurrentTime();
                    
                case "add_numbers":
                    int a = args.containsKey("a") ? Integer.parseInt(args.get("a").toString()) : 0;
                    int b = args.containsKey("b") ? Integer.parseInt(args.get("b").toString()) : 0;
                    return String.valueOf(demoMcpService.add(a, b));
                    
                case "get_user_info":
                    String userId = args.containsKey("userId") ? args.get("userId").toString() : "";
                    return objectMapper.writeValueAsString(demoMcpService.getUserInfo(userId));
                    
                case "translate_text":
                    String text = args.containsKey("text") ? args.get("text").toString() : "";
                    String targetLanguage = args.containsKey("targetLanguage") ? args.get("targetLanguage").toString() : "";
                    return demoMcpService.translateText(text, targetLanguage);
                    
                case "get_weather":
                    String city = args.containsKey("city") ? args.get("city").toString() : "";
                    return demoMcpService.getWeather(city);
                    
                default:
                    return "未知工具: " + toolName;
            }
        } catch (Exception e) {
            logger.error("执行工具调用失败: {}", toolName, e);
            return "执行工具调用失败: " + e.getMessage();
        }
    }
} 