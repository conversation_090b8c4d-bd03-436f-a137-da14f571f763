package com.example.demo.mcp.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * HTML控制器
 * 直接返回HTML内容
 */
@Controller
public class HtmlController {

    /**
     * MCP聊天页面
     */
    @GetMapping(value = "/mcp-direct", produces = MediaType.TEXT_HTML_VALUE + ";charset=UTF-8")
    @ResponseBody
    public String mcpChatPage() throws IOException {
        Resource resource = new ClassPathResource("static/mcp-simple.html");
        return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
    }
} 