package com.example.demo.mcp.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

/**
 * API Key认证过滤器
 * 支持通过Header或Query参数传递API Key
 */
public class McpApiKeyFilter extends OncePerRequestFilter {
    
    private static final String API_KEY_HEADER = "X-MCP-API-Key";
    private static final String API_KEY_PARAM = "api_key";
    
    private final String validApiKey;
    
    public McpApiKeyFilter(String validApiKey) {
        this.validApiKey = validApiKey;
    }
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        // 如果没有配置API Key，跳过验证
        if (!StringUtils.hasText(validApiKey)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 如果已经认证，跳过
        if (SecurityContextHolder.getContext().getAuthentication() != null) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 从Header获取API Key
        String apiKey = request.getHeader(API_KEY_HEADER);
        
        // 如果Header中没有，从查询参数获取
        if (!StringUtils.hasText(apiKey)) {
            apiKey = request.getParameter(API_KEY_PARAM);
        }
        
        // 验证API Key
        if (StringUtils.hasText(apiKey) && validApiKey.equals(apiKey)) {
            // 创建认证对象
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(
                    "api-key-user", 
                    null, 
                    Collections.singletonList(new SimpleGrantedAuthority("ROLE_MCP_USER"))
                );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
        
        filterChain.doFilter(request, response);
    }
}
