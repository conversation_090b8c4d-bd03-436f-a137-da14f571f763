package com.example.demo.mcp.controller;

import com.example.demo.mcp.service.DemoMcpService;
import com.example.demo.mcp.service.McpChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * MCP聊天控制器
 * 提供Web页面和API接口
 */
@RestController
@RequestMapping("/api")
public class McpChatController {

    @Autowired
    private McpChatService mcpChatService;

    /**
     * 聊天API
     * @param request 聊天请求
     * @return 聊天响应
     */
    @PostMapping("/mcp-chat")
    public Map<String, Object> chat(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        return mcpChatService.processChat(message);
    }
} 