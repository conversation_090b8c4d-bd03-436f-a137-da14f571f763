package com.example.demo.mcp.controller;

import com.example.demo.mcp.protocol.*;
import com.example.demo.mcp.server.StandardMcpServer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 标准MCP协议控制器
 * 提供基于JSON-RPC 2.0的MCP服务端点
 */
@RestController
@RequestMapping("/mcp")
public class StandardMcpController {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardMcpController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private StandardMcpServer mcpServer;
    
    /**
     * 处理MCP协议请求
     * 基于JSON-RPC 2.0规范
     */
    @PostMapping(value = "/", 
                 consumes = MediaType.APPLICATION_JSON_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    public Object handleMcpRequest(@RequestBody Map<String, Object> requestBody) {
        try {
            // 检查是否为批量请求
            if (requestBody.containsKey("jsonrpc")) {
                // 单个请求
                return handleSingleRequest(requestBody);
            } else {
                // 可能是批量请求或其他格式
                logger.warn("不支持的请求格式: {}", requestBody);
                McpResponse errorResponse = new McpResponse();
                errorResponse.setError(new McpResponse.McpError(-32600, "Invalid Request"));
                return errorResponse;
            }
        } catch (Exception e) {
            logger.error("处理MCP请求时出错", e);
            McpResponse errorResponse = new McpResponse();
            errorResponse.setError(new McpResponse.McpError(-32603, "Internal error: " + e.getMessage()));
            return errorResponse;
        }
    }
    
    /**
     * 处理单个MCP请求
     */
    private Object handleSingleRequest(Map<String, Object> requestBody) {
        try {
            // 检查是否为通知（没有id字段）
            if (!requestBody.containsKey("id")) {
                // 处理通知，不返回响应
                handleNotification(requestBody);
                return null;
            }
            
            // 转换为McpRequest对象
            McpRequest request = objectMapper.convertValue(requestBody, McpRequest.class);
            
            // 验证请求
            if (request.getMethod() == null || request.getMethod().isEmpty()) {
                McpResponse errorResponse = new McpResponse();
                errorResponse.setId(request.getId());
                errorResponse.setError(new McpResponse.McpError(-32600, "Invalid Request: missing method"));
                return errorResponse;
            }
            
            // 处理请求
            return mcpServer.handleRequest(request);
            
        } catch (Exception e) {
            logger.error("处理单个MCP请求时出错", e);
            McpResponse errorResponse = new McpResponse();
            if (requestBody.containsKey("id")) {
                errorResponse.setId(requestBody.get("id"));
            }
            errorResponse.setError(new McpResponse.McpError(-32603, "Internal error: " + e.getMessage()));
            return errorResponse;
        }
    }
    
    /**
     * 处理MCP通知
     */
    private void handleNotification(Map<String, Object> requestBody) {
        try {
            McpNotification notification = objectMapper.convertValue(requestBody, McpNotification.class);
            logger.info("收到MCP通知: method={}, params={}", notification.getMethod(), notification.getParams());
            
            // 根据通知类型处理
            switch (notification.getMethod()) {
                case "notifications/initialized":
                    logger.info("客户端初始化完成");
                    break;
                case "notifications/cancelled":
                    logger.info("请求被取消");
                    break;
                default:
                    logger.warn("未知的通知类型: {}", notification.getMethod());
            }
        } catch (Exception e) {
            logger.error("处理MCP通知时出错", e);
        }
    }
    
    /**
     * 获取服务器信息（用于调试）
     */
    @GetMapping("/info")
    public Map<String, Object> getServerInfo() {
        return Map.of(
            "name", "Standard MCP Server",
            "version", "1.0.0",
            "protocolVersion", "2025-03-26",
            "description", "基于标准MCP协议的服务器实现"
        );
    }
    
    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        return Map.of(
            "status", "healthy",
            "timestamp", System.currentTimeMillis()
        );
    }
}
