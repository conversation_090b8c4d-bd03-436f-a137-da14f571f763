package com.example.demo.mcp.service;

import com.example.demo.mcp.annotation.WithMcpContext;
import com.example.demo.mcp.context.McpContextHolder;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 演示MCP服务
 * 实现各种工具功能
 */
@Service
public class DemoMcpService {

    /**
     * 获取当前时间
     * @return 格式化的时间字符串
     */
    @WithMcpContext
    public String getCurrentTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
        return sdf.format(new Date());
    }

    /**
     * 计算两个数的和
     * @param a 第一个数
     * @param b 第二个数
     * @return 计算结果
     */
    @WithMcpContext
    public int add(int a, int b) {
        return a + b;
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @WithMcpContext
    public Map<String, Object> getUserInfo(String userId) {
        // 模拟用户数据
        Map<String, Map<String, Object>> userDatabase = new HashMap<>();
        
        // 添加一些示例用户
        Map<String, Object> user1 = new HashMap<>();
        user1.put("id", "12345");
        user1.put("name", "张三");
        user1.put("age", 28);
        user1.put("email", "<EMAIL>");
        user1.put("registrationDate", "2020-05-15");
        userDatabase.put("12345", user1);
        
        Map<String, Object> user2 = new HashMap<>();
        user2.put("id", "67890");
        user2.put("name", "李四");
        user2.put("age", 35);
        user2.put("email", "<EMAIL>");
        user2.put("registrationDate", "2019-11-22");
        userDatabase.put("67890", user2);
        
        Map<String, Object> user3 = new HashMap<>();
        user3.put("id", "u8976");
        user3.put("name", "王五");
        user3.put("age", 42);
        user3.put("email", "<EMAIL>");
        user3.put("registrationDate", "2021-03-08");
        userDatabase.put("u8976", user3);
        
        // 返回用户信息或默认消息
        if (userDatabase.containsKey(userId)) {
            return userDatabase.get(userId);
        } else {
            Map<String, Object> notFound = new HashMap<>();
            notFound.put("error", "未找到ID为" + userId + "的用户");
            return notFound;
        }
    }

    /**
     * 翻译文本
     * @param text 要翻译的文本
     * @param targetLanguage 目标语言
     * @return 翻译结果
     */
    @WithMcpContext
    public String translateText(String text, String targetLanguage) {
        // 模拟翻译功能
        Map<String, Map<String, String>> translations = new HashMap<>();
        
        // 英文到中文的翻译
        Map<String, String> enToCn = new HashMap<>();
        enToCn.put("hello", "你好");
        enToCn.put("world", "世界");
        enToCn.put("hello world", "你好世界");
        enToCn.put("good morning", "早上好");
        enToCn.put("thank you", "谢谢");
        enToCn.put("goodbye", "再见");
        translations.put("中文", enToCn);
        
        // 中文到英文的翻译
        Map<String, String> cnToEn = new HashMap<>();
        cnToEn.put("你好", "hello");
        cnToEn.put("世界", "world");
        cnToEn.put("你好世界", "hello world");
        cnToEn.put("早上好", "good morning");
        cnToEn.put("谢谢", "thank you");
        cnToEn.put("再见", "goodbye");
        cnToEn.put("人工智能", "artificial intelligence");
        translations.put("英文", cnToEn);
        
        // 尝试翻译
        if (translations.containsKey(targetLanguage)) {
            Map<String, String> translationMap = translations.get(targetLanguage);
            if (translationMap.containsKey(text.toLowerCase())) {
                return translationMap.get(text.toLowerCase());
            }
        }
        
        // 如果没有找到翻译
        return "无法翻译文本 \"" + text + "\" 到 " + targetLanguage;
    }

    /**
     * 获取天气信息
     * @param city 城市名称
     * @return 天气信息
     */
    @WithMcpContext
    public String getWeather(String city) {
        // 模拟天气数据
        Map<String, String> weatherDatabase = new HashMap<>();
        weatherDatabase.put("北京", "晴天，温度25°C，空气质量良好，适合户外活动");
        weatherDatabase.put("上海", "多云，温度28°C，空气质量良好，有微风");
        weatherDatabase.put("广州", "小雨，温度30°C，空气湿度较高，建议带伞");
        weatherDatabase.put("深圳", "阵雨，温度29°C，局部地区有雷电，出行请注意安全");
        weatherDatabase.put("杭州", "晴天，温度26°C，西湖景区游客较多");
        weatherDatabase.put("成都", "阴天，温度22°C，有轻微雾霾，空气质量一般");

        // 返回天气信息或默认消息
        if (weatherDatabase.containsKey(city)) {
            return city + "今天的天气：" + weatherDatabase.get(city);
        } else {
            return "抱歉，暂无" + city + "的天气信息";
        }
    }

    /**
     * 文件操作工具
     * @param operation 操作类型：read, write, list
     * @param path 文件路径
     * @param content 写入内容（仅写操作需要）
     * @return 操作结果
     */
    @WithMcpContext
    public String fileOperation(String operation, String path, String content) {
        try {
            switch (operation.toLowerCase()) {
                case "read":
                    return "模拟读取文件: " + path + " 内容: [文件内容示例]";
                case "write":
                    return "模拟写入文件: " + path + " 内容: " + content + " - 写入成功";
                case "list":
                    return "模拟列出目录: " + path + " 文件: [file1.txt, file2.txt, dir1/]";
                default:
                    return "不支持的操作: " + operation;
            }
        } catch (Exception e) {
            return "文件操作失败: " + e.getMessage();
        }
    }

    /**
     * 数据库查询工具
     * @param query SQL查询语句
     * @return 查询结果
     */
    @WithMcpContext
    public String databaseQuery(String query) {
        // 模拟数据库查询
        if (query.toLowerCase().contains("select")) {
            return "查询结果: [{'id': 1, 'name': '张三', 'age': 25}, {'id': 2, 'name': '李四', 'age': 30}]";
        } else if (query.toLowerCase().contains("insert")) {
            return "插入成功，影响行数: 1";
        } else if (query.toLowerCase().contains("update")) {
            return "更新成功，影响行数: 2";
        } else if (query.toLowerCase().contains("delete")) {
            return "删除成功，影响行数: 1";
        } else {
            return "不支持的SQL操作";
        }
    }

    /**
     * HTTP请求工具
     * @param method HTTP方法
     * @param url 请求URL
     * @param headers 请求头（JSON格式）
     * @param body 请求体
     * @return 响应结果
     */
    @WithMcpContext
    public String httpRequest(String method, String url, String headers, String body) {
        // 模拟HTTP请求
        return String.format("HTTP %s 请求到 %s\n请求头: %s\n请求体: %s\n响应: {'status': 200, 'data': 'success'}",
                           method, url, headers, body);
    }

    /**
     * 代码执行工具
     * @param language 编程语言
     * @param code 代码内容
     * @return 执行结果
     */
    @WithMcpContext
    public String executeCode(String language, String code) {
        // 模拟代码执行（实际应用中需要沙箱环境）
        switch (language.toLowerCase()) {
            case "python":
                return "Python代码执行结果: " + code + " => 输出: Hello World";
            case "javascript":
                return "JavaScript代码执行结果: " + code + " => 输出: console.log executed";
            case "java":
                return "Java代码执行结果: " + code + " => 输出: System.out.println executed";
            default:
                return "不支持的编程语言: " + language;
        }
    }

    /**
     * 图像处理工具
     * @param operation 操作类型：resize, crop, filter
     * @param imagePath 图像路径
     * @param parameters 操作参数
     * @return 处理结果
     */
    @WithMcpContext
    public String imageProcessing(String operation, String imagePath, String parameters) {
        return String.format("图像处理完成: %s 操作 %s，参数: %s，输出: processed_%s",
                           operation, imagePath, parameters, imagePath);
    }
}