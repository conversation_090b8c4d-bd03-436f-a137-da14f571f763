package com.example.demo.mcp.config;

import com.example.demo.mcp.interceptor.McpContextInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 * 配置静态资源和MCP拦截器
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    @Autowired
    private McpContextInterceptor mcpContextInterceptor;
    
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射 - 只映射/static/**，避免与MCP端点冲突
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");

        // 配置图片资源映射
        registry.addResourceHandler("/image/**")
                .addResourceLocations("classpath:/static/image/");
    }
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(mcpContextInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/public/**", "/error", "/favicon.ico", "/**/*.html", "/**/*.js", "/**/*.css", "/**/*.png", "/**/*.jpg", "/**/*.jpeg", "/**/*.gif");
    }
} 