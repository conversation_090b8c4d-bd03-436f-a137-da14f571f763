package com.example.demo.mcp.transport;

import com.example.demo.mcp.protocol.*;
import com.example.demo.mcp.server.StandardMcpServer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * HTTP MCP控制器
 * 为Cherry Studio提供HTTP传输支持
 */
@RestController
@RequestMapping("/mcp")
public class HttpMcpController {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpMcpController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private StandardMcpServer mcpServer;
    
    /**
     * HTTP MCP端点 - 替代SSE的HTTP实现
     */
    @PostMapping(value = "/http", 
                 consumes = MediaType.APPLICATION_JSON_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    @CrossOrigin(origins = "*")
    public ResponseEntity<Object> handleHttpRequest(@RequestBody Map<String, Object> requestBody) {
        
        try {
            // 转换为MCP请求
            McpRequest request = objectMapper.convertValue(requestBody, McpRequest.class);
            logger.info("收到HTTP MCP请求: {} (ID: {})", request.getMethod(), request.getId());
            
            // 处理请求
            McpResponse response = mcpServer.handleRequest(request);
            
            logger.info("HTTP MCP响应: {} (ID: {})", response.getResult() != null ? "success" : "error", response.getId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("处理HTTP MCP请求时出错", e);
            
            // 返回标准的JSON-RPC错误响应
            McpResponse errorResponse = new McpResponse();
            errorResponse.setJsonrpc("2.0");
            errorResponse.setId(requestBody.get("id"));

            McpResponse.McpError error = new McpResponse.McpError();
            error.setCode(-32603);
            error.setMessage("Internal error: " + e.getMessage());
            errorResponse.setError(error);
            
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 获取服务器信息
     */
    @GetMapping("/http/info")
    @CrossOrigin(origins = "*")
    public Map<String, Object> getServerInfo() {
        return Map.of(
            "name", "Demo MCP Server",
            "version", "1.0.0",
            "protocol", "mcp",
            "transport", "http",
            "capabilities", Map.of(
                "tools", Map.of("listChanged", true),
                "resources", Map.of("listChanged", true)
            )
        );
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/http/health")
    @CrossOrigin(origins = "*")
    public Map<String, Object> health() {
        return Map.of(
            "status", "healthy",
            "timestamp", System.currentTimeMillis(),
            "transport", "http"
        );
    }
    
    /**
     * 获取工具列表的快捷端点
     */
    @GetMapping("/http/tools")
    @CrossOrigin(origins = "*")
    public ResponseEntity<Object> getTools() {
        try {
            McpRequest request = new McpRequest();
            request.setJsonrpc("2.0");
            request.setId("tools-list");
            request.setMethod("tools/list");
            request.setParams(Map.of());
            
            McpResponse response = mcpServer.handleRequest(request);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取工具列表失败", e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
}
