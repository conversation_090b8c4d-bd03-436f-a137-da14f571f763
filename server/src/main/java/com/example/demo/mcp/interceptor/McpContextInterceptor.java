package com.example.demo.mcp.interceptor;

import com.example.demo.mcp.annotation.WithMcpContext;
import com.example.demo.mcp.context.McpContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * MCP上下文拦截器
 * 处理请求前后的上下文设置和清理
 */
@Component
public class McpContextInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(McpContextInterceptor.class);
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            
            // 检查方法是否有WithMcpContext注解
            if (method.isAnnotationPresent(WithMcpContext.class)) {
                // 创建新的上下文
                Map<String, Object> context = new HashMap<>();
                context.put("requestId", UUID.randomUUID().toString());
                context.put("timestamp", System.currentTimeMillis());
                context.put("path", request.getRequestURI());
                
                // 设置上下文
                McpContextHolder.setContext(context);
                logger.debug("已为请求 {} 创建MCP上下文", request.getRequestURI());
            }
        }
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            
            // 检查方法是否有WithMcpContext注解
            if (method.isAnnotationPresent(WithMcpContext.class)) {
                WithMcpContext annotation = method.getAnnotation(WithMcpContext.class);
                
                // 如果不共享上下文，则清除
                if (!annotation.shared()) {
                    McpContextHolder.clearContext();
                    logger.debug("已清除请求 {} 的MCP上下文", request.getRequestURI());
                }
            }
        }
    }
} 