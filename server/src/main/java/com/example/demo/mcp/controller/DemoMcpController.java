package com.example.demo.mcp.controller;

import com.example.demo.mcp.annotation.WithMcpContext;
import com.example.demo.mcp.service.DemoMcpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP演示控制器
 * 提供MCP API端点
 */
@RestController
@RequestMapping("/api/mcp-demo")
public class DemoMcpController {
    
    @Autowired
    private DemoMcpService demoMcpService;
    
    /**
     * 获取当前时间
     * @return 当前时间
     */
    @GetMapping("/time")
    @WithMcpContext
    public ResponseEntity<Map<String, Object>> getCurrentTime() {
        String time = demoMcpService.getCurrentTime();
        Map<String, Object> response = new HashMap<>();
        response.put("result", time);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 计算两个数的和
     * @param a 第一个数
     * @param b 第二个数
     * @return 计算结果
     */
    @GetMapping("/add")
    @WithMcpContext
    public ResponseEntity<Map<String, Object>> add(@RequestParam int a, @RequestParam int b) {
        int result = demoMcpService.add(a, b);
        Map<String, Object> response = new HashMap<>();
        response.put("result", result);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/user/{userId}")
    @WithMcpContext
    public ResponseEntity<Map<String, Object>> getUserInfo(@PathVariable String userId) {
        Map<String, Object> userInfo = demoMcpService.getUserInfo(userId);
        Map<String, Object> response = new HashMap<>();
        response.put("result", userInfo);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 翻译文本
     * @param text 要翻译的文本
     * @param targetLanguage 目标语言
     * @return 翻译结果
     */
    @GetMapping("/translate")
    @WithMcpContext
    public ResponseEntity<Map<String, Object>> translateText(
            @RequestParam String text,
            @RequestParam String targetLanguage) {
        String result = demoMcpService.translateText(text, targetLanguage);
        Map<String, Object> response = new HashMap<>();
        response.put("result", result);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取天气信息
     * @param city 城市名称
     * @return 天气信息
     */
    @GetMapping("/weather")
    @WithMcpContext
    public ResponseEntity<Map<String, Object>> getWeather(@RequestParam String city) {
        String weather = demoMcpService.getWeather(city);
        Map<String, Object> response = new HashMap<>();
        response.put("result", weather);
        return ResponseEntity.ok(response);
    }
} 