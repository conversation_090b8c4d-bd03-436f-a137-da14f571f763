package com.example.demo.mcp;

/**
 * Model Context Protocol (MCP) 核心接口
 * 定义模型上下文交互的基本协议
 */
public interface ModelContextProtocol {
    
    /**
     * 获取上下文信息
     * @param key 上下文键
     * @return 对应的上下文值
     */
    String getContext(String key);
    
    /**
     * 设置上下文信息
     * @param key 上下文键
     * @param value 上下文值
     */
    void setContext(String key, String value);
    
    /**
     * 移除上下文信息
     * @param key 上下文键
     * @return 是否成功移除
     */
    boolean removeContext(String key);
    
    /**
     * 清空所有上下文信息
     */
    void clearContext();
    
    /**
     * 获取当前模型ID
     * @return 模型ID
     */
    String getModelId();
    
    /**
     * 设置当前模型ID
     * @param modelId 模型ID
     */
    void setModelId(String modelId);
    
    /**
     * 检查上下文中是否存在指定的键
     * @param key 上下文键
     * @return 是否存在
     */
    boolean hasContext(String key);
} 