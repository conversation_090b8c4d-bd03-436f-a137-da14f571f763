package com.example.demo.mcp.impl;

import com.example.demo.mcp.ModelContextProtocol;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Model Context Protocol的内存实现
 * 使用ConcurrentHashMap保证线程安全
 */
@Component
public class InMemoryModelContextProtocol implements ModelContextProtocol {

    private final Map<String, String> contextMap = new ConcurrentHashMap<>();
    private String modelId;

    @Override
    public String getContext(String key) {
        return contextMap.get(key);
    }

    @Override
    public void setContext(String key, String value) {
        contextMap.put(key, value);
    }

    @Override
    public boolean removeContext(String key) {
        return contextMap.remove(key) != null;
    }

    @Override
    public void clearContext() {
        contextMap.clear();
    }

    @Override
    public String getModelId() {
        return modelId;
    }

    @Override
    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    @Override
    public boolean hasContext(String key) {
        return contextMap.containsKey(key);
    }
} 