package com.example.demo.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

/**
 * MCP通知消息
 */
public class McpNotification extends McpMessage {
    
    @JsonProperty("method")
    private String method;
    
    @JsonProperty("params")
    private Map<String, Object> params;
    
    public String getMethod() {
        return method;
    }
    
    public void setMethod(String method) {
        this.method = method;
    }
    
    public Map<String, Object> getParams() {
        return params;
    }
    
    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
