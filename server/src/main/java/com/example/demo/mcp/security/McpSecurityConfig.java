package com.example.demo.mcp.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * MCP安全配置
 * 支持多种认证方式：Basic Auth、API Key、JWT
 */
@Configuration
@EnableWebSecurity
public class McpSecurityConfig {
    
    @Value("${mcp.security.enabled:false}")
    private boolean securityEnabled;
    
    @Value("${mcp.security.api-key:}")
    private String apiKey;
    
    @Value("${mcp.security.username:admin}")
    private String username;
    
    @Value("${mcp.security.password:password}")
    private String password;
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        if (!securityEnabled) {
            // 如果安全未启用，允许所有请求
            http.authorizeHttpRequests(authz -> authz.anyRequest().permitAll())
                .csrf(csrf -> csrf.disable())
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
            return http.build();
        }
        
        http.csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // 公开端点
                .requestMatchers("/mcp/health", "/mcp/info").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                // 静态资源
                .requestMatchers("/", "/index.html", "/standard-mcp.html", "/mcp-simple.html").permitAll()
                .requestMatchers("/css/**", "/js/**", "/images/**").permitAll()
                // MCP端点需要认证
                .requestMatchers("/mcp/**").authenticated()
                .requestMatchers("/api/standard-mcp/**").authenticated()
                // 其他请求
                .anyRequest().permitAll()
            )
            .httpBasic(basic -> {}) // 启用Basic认证
            .addFilterBefore(new McpApiKeyFilter(apiKey), UsernamePasswordAuthenticationFilter.class); // 添加API Key过滤器
        
        return http.build();
    }
    
    @Bean
    public UserDetailsService userDetailsService() {
        UserDetails user = User.builder()
            .username(username)
            .password(passwordEncoder().encode(password))
            .roles("MCP_USER")
            .build();
            
        return new InMemoryUserDetailsManager(user);
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
