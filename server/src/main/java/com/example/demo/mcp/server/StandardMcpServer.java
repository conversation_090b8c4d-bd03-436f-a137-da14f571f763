package com.example.demo.mcp.server;

import com.example.demo.mcp.protocol.*;
import com.example.demo.mcp.service.DemoMcpService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 标准MCP服务器实现
 * 基于MCP协议规范
 */
@Component
public class StandardMcpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardMcpServer.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private DemoMcpService demoMcpService;
    
    private final McpServerCapabilities capabilities;
    private final Map<String, McpTool> tools;
    private final Map<String, McpResource> resources;
    
    public StandardMcpServer() {
        this.capabilities = new McpServerCapabilities();
        this.tools = new HashMap<>();
        this.resources = new HashMap<>();
        initializeTools();
        initializeResources();
    }
    
    /**
     * 处理MCP请求
     */
    public McpResponse handleRequest(McpRequest request) {
        McpResponse response = new McpResponse();
        response.setId(request.getId());
        
        try {
            switch (request.getMethod()) {
                case "initialize":
                    response.setResult(handleInitialize(request.getParams()));
                    break;
                case "tools/list":
                    response.setResult(handleToolsList());
                    break;
                case "tools/call":
                    response.setResult(handleToolCall(request.getParams()));
                    break;
                case "resources/list":
                    response.setResult(handleResourcesList());
                    break;
                case "resources/read":
                    response.setResult(handleResourceRead(request.getParams()));
                    break;
                default:
                    response.setError(new McpResponse.McpError(-32601, "Method not found: " + request.getMethod()));
            }
        } catch (Exception e) {
            logger.error("处理MCP请求时出错", e);
            response.setError(new McpResponse.McpError(-32603, "Internal error: " + e.getMessage()));
        }
        
        return response;
    }
    
    /**
     * 处理初始化请求
     */
    private Map<String, Object> handleInitialize(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2025-03-26");
        result.put("capabilities", capabilities);
        
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "Demo MCP Server");
        serverInfo.put("version", "1.0.0");
        result.put("serverInfo", serverInfo);
        
        return result;
    }
    
    /**
     * 处理工具列表请求
     */
    private Map<String, Object> handleToolsList() {
        Map<String, Object> result = new HashMap<>();
        result.put("tools", new ArrayList<>(tools.values()));
        return result;
    }
    
    /**
     * 处理工具调用请求
     */
    private Map<String, Object> handleToolCall(Map<String, Object> params) {
        String toolName = (String) params.get("name");
        @SuppressWarnings("unchecked")
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
        
        if (arguments == null) {
            arguments = new HashMap<>();
        }
        
        String result = executeToolCall(toolName, arguments);
        
        Map<String, Object> response = new HashMap<>();
        response.put("content", Arrays.asList(
            Map.of("type", "text", "text", result)
        ));
        response.put("isError", false);
        
        return response;
    }
    
    /**
     * 处理资源列表请求
     */
    private Map<String, Object> handleResourcesList() {
        Map<String, Object> result = new HashMap<>();
        result.put("resources", new ArrayList<>(resources.values()));
        return result;
    }
    
    /**
     * 处理资源读取请求
     */
    private Map<String, Object> handleResourceRead(Map<String, Object> params) {
        String uri = (String) params.get("uri");
        
        Map<String, Object> result = new HashMap<>();
        result.put("contents", Arrays.asList(
            Map.of("uri", uri, "mimeType", "text/plain", "text", "Resource content for: " + uri)
        ));
        
        return result;
    }
    
    /**
     * 执行工具调用
     */
    private String executeToolCall(String toolName, Map<String, Object> args) {
        try {
            switch (toolName) {
                case "get_current_time":
                    return demoMcpService.getCurrentTime();

                case "add_numbers":
                    int a = args.containsKey("a") ? Integer.parseInt(args.get("a").toString()) : 0;
                    int b = args.containsKey("b") ? Integer.parseInt(args.get("b").toString()) : 0;
                    return String.valueOf(demoMcpService.add(a, b));

                case "get_user_info":
                    String userId = args.containsKey("userId") ? args.get("userId").toString() : "";
                    return objectMapper.writeValueAsString(demoMcpService.getUserInfo(userId));

                case "translate_text":
                    String text = args.containsKey("text") ? args.get("text").toString() : "";
                    String targetLanguage = args.containsKey("targetLanguage") ? args.get("targetLanguage").toString() : "";
                    return demoMcpService.translateText(text, targetLanguage);

                case "get_weather":
                    String city = args.containsKey("city") ? args.get("city").toString() : "";
                    return demoMcpService.getWeather(city);

                case "file_operation":
                    String operation = args.containsKey("operation") ? args.get("operation").toString() : "";
                    String path = args.containsKey("path") ? args.get("path").toString() : "";
                    String content = args.containsKey("content") ? args.get("content").toString() : "";
                    return demoMcpService.fileOperation(operation, path, content);

                case "database_query":
                    String query = args.containsKey("query") ? args.get("query").toString() : "";
                    return demoMcpService.databaseQuery(query);

                case "http_request":
                    String method = args.containsKey("method") ? args.get("method").toString() : "";
                    String url = args.containsKey("url") ? args.get("url").toString() : "";
                    String headers = args.containsKey("headers") ? args.get("headers").toString() : "{}";
                    String body = args.containsKey("body") ? args.get("body").toString() : "";
                    return demoMcpService.httpRequest(method, url, headers, body);

                case "execute_code":
                    String language = args.containsKey("language") ? args.get("language").toString() : "";
                    String code = args.containsKey("code") ? args.get("code").toString() : "";
                    return demoMcpService.executeCode(language, code);

                case "image_processing":
                    String imgOperation = args.containsKey("operation") ? args.get("operation").toString() : "";
                    String imagePath = args.containsKey("imagePath") ? args.get("imagePath").toString() : "";
                    String parameters = args.containsKey("parameters") ? args.get("parameters").toString() : "";
                    return demoMcpService.imageProcessing(imgOperation, imagePath, parameters);

                default:
                    return "未知工具: " + toolName;
            }
        } catch (Exception e) {
            logger.error("执行工具调用失败: {}", toolName, e);
            return "执行工具调用失败: " + e.getMessage();
        }
    }
    
    /**
     * 初始化工具定义
     */
    private void initializeTools() {
        // 获取当前时间工具
        Map<String, Object> timeSchema = Map.of(
            "type", "object",
            "properties", Map.of(),
            "required", Arrays.asList()
        );
        tools.put("get_current_time", new McpTool("get_current_time", "获取当前的系统时间", timeSchema));

        // 数字相加工具
        Map<String, Object> addSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "a", Map.of("type", "integer", "description", "第一个数字"),
                "b", Map.of("type", "integer", "description", "第二个数字")
            ),
            "required", Arrays.asList("a", "b")
        );
        tools.put("add_numbers", new McpTool("add_numbers", "计算两个数字的和", addSchema));

        // 获取用户信息工具
        Map<String, Object> userSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "userId", Map.of("type", "string", "description", "用户ID")
            ),
            "required", Arrays.asList("userId")
        );
        tools.put("get_user_info", new McpTool("get_user_info", "获取用户信息", userSchema));

        // 翻译文本工具
        Map<String, Object> translateSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "text", Map.of("type", "string", "description", "要翻译的文本"),
                "targetLanguage", Map.of("type", "string", "description", "目标语言")
            ),
            "required", Arrays.asList("text", "targetLanguage")
        );
        tools.put("translate_text", new McpTool("translate_text", "翻译文本到指定语言", translateSchema));

        // 获取天气工具
        Map<String, Object> weatherSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "city", Map.of("type", "string", "description", "城市名称")
            ),
            "required", Arrays.asList("city")
        );
        tools.put("get_weather", new McpTool("get_weather", "获取指定城市的天气信息", weatherSchema));

        // 文件操作工具
        Map<String, Object> fileSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "operation", Map.of("type", "string", "description", "操作类型：read, write, list"),
                "path", Map.of("type", "string", "description", "文件路径"),
                "content", Map.of("type", "string", "description", "写入内容（仅写操作需要）")
            ),
            "required", Arrays.asList("operation", "path")
        );
        tools.put("file_operation", new McpTool("file_operation", "文件操作工具", fileSchema));

        // 数据库查询工具
        Map<String, Object> dbSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "query", Map.of("type", "string", "description", "SQL查询语句")
            ),
            "required", Arrays.asList("query")
        );
        tools.put("database_query", new McpTool("database_query", "数据库查询工具", dbSchema));

        // HTTP请求工具
        Map<String, Object> httpSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "method", Map.of("type", "string", "description", "HTTP方法"),
                "url", Map.of("type", "string", "description", "请求URL"),
                "headers", Map.of("type", "string", "description", "请求头（JSON格式）"),
                "body", Map.of("type", "string", "description", "请求体")
            ),
            "required", Arrays.asList("method", "url")
        );
        tools.put("http_request", new McpTool("http_request", "HTTP请求工具", httpSchema));

        // 代码执行工具
        Map<String, Object> codeSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "language", Map.of("type", "string", "description", "编程语言"),
                "code", Map.of("type", "string", "description", "代码内容")
            ),
            "required", Arrays.asList("language", "code")
        );
        tools.put("execute_code", new McpTool("execute_code", "代码执行工具", codeSchema));

        // 图像处理工具
        Map<String, Object> imageSchema = Map.of(
            "type", "object",
            "properties", Map.of(
                "operation", Map.of("type", "string", "description", "操作类型：resize, crop, filter"),
                "imagePath", Map.of("type", "string", "description", "图像路径"),
                "parameters", Map.of("type", "string", "description", "操作参数")
            ),
            "required", Arrays.asList("operation", "imagePath")
        );
        tools.put("image_processing", new McpTool("image_processing", "图像处理工具", imageSchema));
    }
    
    /**
     * 初始化资源定义
     */
    private void initializeResources() {
        resources.put("demo://time", new McpResource("demo://time", "当前时间", "获取当前系统时间", "text/plain"));
        resources.put("demo://weather", new McpResource("demo://weather", "天气信息", "获取天气信息", "application/json"));
    }
}
