package com.example.demo.mcp.controller;

import com.example.demo.mcp.client.SimpleMcpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 标准MCP协议演示控制器
 * 展示如何使用标准MCP协议进行客户端-服务器通信
 */
@RestController
@RequestMapping("/api/standard-mcp")
public class StandardMcpDemoController {
    
    @Autowired
    private SimpleMcpClient mcpClient;
    
    /**
     * 初始化MCP连接
     */
    @PostMapping("/initialize")
    public Map<String, Object> initialize() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> initResult = mcpClient.initialize();
            if (initResult != null) {
                result.put("success", true);
                result.put("serverInfo", initResult);
                result.put("message", "MCP连接初始化成功");
            } else {
                result.put("success", false);
                result.put("message", "MCP连接初始化失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "初始化时出错: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取可用工具列表
     */
    @GetMapping("/tools")
    public Map<String, Object> getTools() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!mcpClient.isInitialized()) {
                mcpClient.initialize();
            }
            
            Map<String, Object> toolsResult = mcpClient.listTools();
            if (toolsResult != null) {
                result.put("success", true);
                result.put("tools", toolsResult.get("tools"));
            } else {
                result.put("success", false);
                result.put("message", "获取工具列表失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工具列表时出错: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 调用工具 - 获取当前时间
     */
    @PostMapping("/tools/time")
    public Map<String, Object> getCurrentTime() {
        return callTool("get_current_time", new HashMap<>());
    }
    
    /**
     * 调用工具 - 数字相加
     */
    @PostMapping("/tools/add")
    public Map<String, Object> addNumbers(@RequestParam int a, @RequestParam int b) {
        Map<String, Object> args = new HashMap<>();
        args.put("a", a);
        args.put("b", b);
        return callTool("add_numbers", args);
    }
    
    /**
     * 调用工具 - 获取用户信息
     */
    @PostMapping("/tools/user/{userId}")
    public Map<String, Object> getUserInfo(@PathVariable String userId) {
        Map<String, Object> args = new HashMap<>();
        args.put("userId", userId);
        return callTool("get_user_info", args);
    }
    
    /**
     * 调用工具 - 翻译文本
     */
    @PostMapping("/tools/translate")
    public Map<String, Object> translateText(@RequestParam String text, @RequestParam String targetLanguage) {
        Map<String, Object> args = new HashMap<>();
        args.put("text", text);
        args.put("targetLanguage", targetLanguage);
        return callTool("translate_text", args);
    }
    
    /**
     * 调用工具 - 获取天气
     */
    @PostMapping("/tools/weather")
    public Map<String, Object> getWeather(@RequestParam String city) {
        Map<String, Object> args = new HashMap<>();
        args.put("city", city);
        return callTool("get_weather", args);
    }
    
    /**
     * 获取可用资源列表
     */
    @GetMapping("/resources")
    public Map<String, Object> getResources() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!mcpClient.isInitialized()) {
                mcpClient.initialize();
            }
            
            Map<String, Object> resourcesResult = mcpClient.listResources();
            if (resourcesResult != null) {
                result.put("success", true);
                result.put("resources", resourcesResult.get("resources"));
            } else {
                result.put("success", false);
                result.put("message", "获取资源列表失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取资源列表时出错: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 读取资源
     */
    @GetMapping("/resources/read")
    public Map<String, Object> readResource(@RequestParam String uri) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!mcpClient.isInitialized()) {
                mcpClient.initialize();
            }
            
            Map<String, Object> resourceResult = mcpClient.readResource(uri);
            if (resourceResult != null) {
                result.put("success", true);
                result.put("content", resourceResult.get("contents"));
            } else {
                result.put("success", false);
                result.put("message", "读取资源失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "读取资源时出错: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 通用工具调用方法
     */
    private Map<String, Object> callTool(String toolName, Map<String, Object> arguments) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!mcpClient.isInitialized()) {
                mcpClient.initialize();
            }
            
            Map<String, Object> toolResult = mcpClient.callTool(toolName, arguments);
            if (toolResult != null) {
                result.put("success", true);
                result.put("result", toolResult);
            } else {
                result.put("success", false);
                result.put("message", "工具调用失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "工具调用时出错: " + e.getMessage());
        }
        
        return result;
    }
}
