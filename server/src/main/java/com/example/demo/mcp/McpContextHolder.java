package com.example.demo.mcp;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * MCP上下文持有者
 * 提供静态方法访问MCP实例
 */
@Component
public class McpContextHolder {

    private static ModelContextProtocol modelContextProtocol;

    @Autowired
    public void setModelContextProtocol(ModelContextProtocol modelContextProtocol) {
        McpContextHolder.modelContextProtocol = modelContextProtocol;
    }

    /**
     * 获取MCP实例
     * @return MCP实例
     */
    public static ModelContextProtocol getMcp() {
        return modelContextProtocol;
    }

    /**
     * 获取上下文值
     * @param key 上下文键
     * @return 上下文值
     */
    public static String get(String key) {
        return modelContextProtocol.getContext(key);
    }

    /**
     * 设置上下文值
     * @param key 上下文键
     * @param value 上下文值
     */
    public static void set(String key, String value) {
        modelContextProtocol.setContext(key, value);
    }

    /**
     * 移除上下文
     * @param key 上下文键
     * @return 是否成功移除
     */
    public static boolean remove(String key) {
        return modelContextProtocol.removeContext(key);
    }

    /**
     * 清空上下文
     */
    public static void clear() {
        modelContextProtocol.clearContext();
    }
} 