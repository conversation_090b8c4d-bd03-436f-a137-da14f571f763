package com.example.demo;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class JazzCashApiService {
    private static final Logger logger = LoggerFactory.getLogger(JazzCashApiService.class);

    // JazzCash API 相关配置
    private static final String JAZZCASH_API_URL = "https://sandbox.jazzcash.com.pk/ApplicationAPI/API/2.0/Purchase/DoMWalletTransaction";
    private static final String JAZZCASH_INQUIRY_URL = "https://sandbox.jazzcash.com.pk/ApplicationAPI/API/2.0/Inquiry/DoTransaction";

    // 商户配置信息，实际应用中应从配置文件或环境变量中读取
    @Value("${jazzcash.merchant.id:MC158532}")
    private String merchantId;

    @Value("${jazzcash.password:0b64sxf81t}")
    private String password;

    @Value("${jazzcash.integrity.salt:8zzx29y512}")
    private String integritySalt;

    @Value("${jazzcash.return.url:http://10.1.128.210:8080/jazzcash/api/callback}")
    private String returnUrl;

    private final RestTemplate restTemplate;
    private final Gson gson;

    // 存储交易信息的内存缓存
    private static final Map<String, Map<String, Object>> transactionCache = new HashMap<>();

    public JazzCashApiService() {
        this.restTemplate = new RestTemplate();
        this.gson = new Gson();
    }

    /**
     * 创建钱包支付交易
     *
     * @param mobileNumber 用户手机号
     * @param cnic 用户身份证号
     * @param amount 支付金额（巴基斯坦卢比）
     * @param description 交易描述
     * @return 支付结果
     */
    public Map<String, Object> createWalletPayment(String mobileNumber, String cnic, String amount, String description) {
        logger.info("创建钱包支付交易 - 手机号: {}, 身份证: {}, 金额: {}", mobileNumber, cnic, amount);

        try {
            // 生成交易参考号
            String txnRefNo = generateTxnRefNo();

            // 生成交易日期时间
            String txnDateTime = generateTxnDateTime();
            String txnExpiryDateTime = generateExpiryDateTime();

            // 生成账单参考号
            String billRef = "billRef" + System.currentTimeMillis();

            // 准备请求数据
            Map<String, String> apiRequestData = new LinkedHashMap<>();
            apiRequestData.put("pp_Version", "2.0");
            apiRequestData.put("pp_TxnType", "MWALLET");
            apiRequestData.put("pp_Language", "EN");
            apiRequestData.put("pp_MerchantID", merchantId);
            apiRequestData.put("pp_MobileNumber", mobileNumber);
            apiRequestData.put("pp_CNIC", cnic);
            apiRequestData.put("pp_Amount", amount);
            apiRequestData.put("pp_TxnCurrency", "PKR");
            apiRequestData.put("pp_TxnDateTime", txnDateTime);
            apiRequestData.put("pp_TxnExpiryDateTime", txnExpiryDateTime);
            apiRequestData.put("pp_TxnRefNo", txnRefNo);
            apiRequestData.put("pp_Description", description);
            apiRequestData.put("pp_BillReference", billRef);
            apiRequestData.put("pp_Password", password);
            apiRequestData.put("pp_ReturnURL", returnUrl);
            apiRequestData.put("pp_SecureHash", "");

            // 计算安全哈希
            String secureHash = generateSecureHashForRequest(apiRequestData);
            apiRequestData.put("pp_SecureHash", secureHash);

            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String jsonPayload = gson.toJson(apiRequestData);
            HttpEntity<String> entity = new HttpEntity<>(jsonPayload, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    JAZZCASH_API_URL,
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            // 解析响应
            String responseBody = response.getBody();
            logger.info("收到 JazzCash API 响应: {}", responseBody);

            Map<String, Object> responseMap = gson.fromJson(responseBody, Map.class);
            responseMap.put("txnRefNo", txnRefNo);

            // 检查响应码
            String responseCode = (String) responseMap.get("pp_ResponseCode");
            boolean success = "000".equals(responseCode);

            if (!success) {
                String userFriendlyMessage = getUserFriendlyErrorMessage(responseCode, (String) responseMap.get("pp_ResponseMessage"));
                responseMap.put("userMessage", userFriendlyMessage);
                responseMap.put("success", false);
                logger.info("支付请求失败，用户友好错误信息: {}", userFriendlyMessage);
            } else {
                responseMap.put("success", true);
                responseMap.put("userMessage", "支付请求已发送，请在您的手机上完成支付");
            }

            return responseMap;
        } catch (Exception e) {
            logger.error("创建钱包支付交易时出错", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            errorResponse.put("userMessage", "支付服务暂时不可用，请稍后再试");
            return errorResponse;
        }
    }



    /**
                     * 查询交易状态
                     *
                     * @param txnRefNo 交易参考号
                     * @return 交易状态
                     */
    public Map<String, Object> inquireTransaction(String txnRefNo) {
        logger.info("查询交易状态: {}", txnRefNo);

        // 首先检查缓存
        if (transactionCache.containsKey(txnRefNo)) {
            Map<String, Object> cachedTransaction = transactionCache.get(txnRefNo);

            // 如果状态是 COMPLETED 或 FAILED，直接返回缓存
            String status = (String) cachedTransaction.getOrDefault("status", "PENDING");
            if ("COMPLETED".equals(status) || "FAILED".equals(status)) {
                logger.info("从缓存中找到已完成交易: {}, 状态: {}", txnRefNo, status);
                return cachedTransaction;
            }

            // 检查交易是否已过期
            Date createdAt = (Date) cachedTransaction.get("createdAt");
            if (createdAt != null) {
                long diffMinutes = (new Date().getTime() - createdAt.getTime()) / (60 * 1000);
                // 如果交易创建超过 30 分钟但仍是 PENDING，标记为 EXPIRED
                if (diffMinutes > 30 && "PENDING".equals(status)) {
                    logger.info("交易已过期: {}", txnRefNo);
                    cachedTransaction.put("status", "EXPIRED");
                    cachedTransaction.put("message", "交易已过期");
                    return cachedTransaction;
                }
            }

            logger.info("从缓存中找到进行中交易: {}", cachedTransaction);
        }

        try {
            // 生成交易日期时间
            String txnDateTime = generateTxnDateTime();

            // 准备请求数据 - 只包含必要的字段
            Map<String, String> apiRequestData = new LinkedHashMap<>();
            apiRequestData.put("pp_Version", "2.0");
            apiRequestData.put("pp_TxnType", "INQUIRY");
            apiRequestData.put("pp_Language", "EN");
            apiRequestData.put("pp_MerchantID", merchantId);
            apiRequestData.put("pp_Password", password);
            apiRequestData.put("pp_TxnRefNo", txnRefNo);
            apiRequestData.put("pp_TxnDateTime", txnDateTime);
            apiRequestData.put("pp_SecureHash", "");

            // 准备用于计算哈希的数据
            String secureHash = generateSecureHashForRequest(apiRequestData);
            
            // 设置安全哈希
            apiRequestData.put("pp_SecureHash", secureHash);
            
            logger.info("准备发送到 JazzCash 的查询请求数据: {}", apiRequestData);

            // 创建 HTTP 请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 将 Map 转换为 JSON
            String jsonPayload = gson.toJson(apiRequestData);
            HttpEntity<String> entity = new HttpEntity<>(jsonPayload, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    JAZZCASH_INQUIRY_URL,
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            // 解析响应
            String responseBody = response.getBody();
            logger.info("收到 JazzCash 状态查询响应: {}", responseBody);

            Map<String, Object> responseMap = gson.fromJson(responseBody, Map.class);

            // 解析响应状态
            String responseCode = (String) responseMap.get("pp_ResponseCode");
            boolean success = "000".equals(responseCode);

            // 如果是SecureHash错误，尝试使用JazzCash返回的哈希值重新发送请求
            if (!success && responseMap.get("pp_ResponseMessage") != null && 
                responseMap.get("pp_ResponseMessage").toString().contains("valid value for pp_SecureHash")) {
                
                // 获取JazzCash返回的哈希值
                String jazzCashHash = (String) responseMap.get("pp_SecureHash");
                if (jazzCashHash != null && !jazzCashHash.isEmpty()) {
                    logger.info("检测到SecureHash错误，尝试使用JazzCash返回的哈希值: {}", jazzCashHash);
                    
                    // 使用JazzCash返回的哈希值
                    apiRequestData.put("pp_SecureHash", jazzCashHash);
                    
                    // 重新发送请求
                    logger.info("使用新的哈希值重新发送查询请求: {}", apiRequestData);
                    jsonPayload = gson.toJson(apiRequestData);
                    entity = new HttpEntity<>(jsonPayload, headers);
                    
                    response = restTemplate.exchange(
                            JAZZCASH_INQUIRY_URL,
                            HttpMethod.POST,
                            entity,
                            String.class
                    );
                    
                    // 解析响应
                    responseBody = response.getBody();
                    logger.info("收到 JazzCash 状态查询第二次响应: {}", responseBody);
                    
                    responseMap = gson.fromJson(responseBody, Map.class);
                    
                    // 更新响应码
                    responseCode = (String) responseMap.get("pp_ResponseCode");
                    success = "000".equals(responseCode);
                }
            }

            // 更新交易状态
            Map<String, Object> transactionData = new HashMap<>();
            transactionData.put("status", success ? "COMPLETED" : "FAILED");
            transactionData.put("success", success);
            transactionData.put("updatedAt", new Date());
            transactionData.putAll(responseMap); // 将响应数据添加到交易信息中

            // 存储交易信息
            transactionCache.put(txnRefNo, transactionData);

            return transactionData;
        } catch (Exception e) {
            logger.error("查询交易状态时出错", e);

            // 出错时返回缓存中的数据（如果有）
            if (transactionCache.containsKey(txnRefNo)) {
                Map<String, Object> cachedTransaction = transactionCache.get(txnRefNo);
                cachedTransaction.put("queryError", e.getMessage());
                return cachedTransaction;
            }

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("status", "ERROR");
            errorResponse.put("error", e.getMessage());
            errorResponse.put("txnRefNo", txnRefNo);
            return errorResponse;
        }
    }
    private String generateSecureHashForRequest(Map<String, String> requestData) {
        try {
            // 准备用于计算哈希的数据 - 按照 ASCII 值排序
            Map<String, String> sortedData = new TreeMap<>(requestData);

            // 过滤出以 "pp_" 开头的非空字段
            StringBuilder concatenatedValues = new StringBuilder();
            for (Map.Entry<String, String> entry : sortedData.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (key.startsWith("pp_") && !key.equals("pp_SecureHash") && value != null && !value.isEmpty()) {
                    concatenatedValues.append(value).append("&");
                }
            }

            // 去掉最后一个多余的 "&"
            if (concatenatedValues.length() > 0) {
                concatenatedValues.setLength(concatenatedValues.length() - 1);
            }

            // 添加 Integrity Salt 作为前缀
            String toBeHashed = integritySalt + "&" + concatenatedValues.toString();

            // 计算 HMAC-SHA256 哈希值
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(integritySalt.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSha256.init(secretKey);
            byte[] hashBytes = hmacSha256.doFinal(toBeHashed.getBytes(StandardCharsets.UTF_8));
            String secureHash = Hex.encodeHexString(hashBytes).toUpperCase();

            return secureHash;
        } catch (Exception e) {
            logger.error("Error generating secure hash", e);
            return "";
        }
    }


    /**
     * 处理 JazzCash API 回调
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    public Map<String, Object> handleApiCallback(Map<String, String> callbackData) {
        logger.info("处理 JazzCash API 回调: {}", callbackData);

        String txnRefNo = callbackData.get("pp_TxnRefNo");
        String responseCode = callbackData.get("pp_ResponseCode");
        boolean success = "000".equals(responseCode);

        // 存储交易信息
        Map<String, Object> transactionData = new HashMap<>(callbackData);
        transactionData.put("status", success ? "COMPLETED" : "FAILED");
        transactionData.put("success", success);
        transactionData.put("callbackReceived", true);
        transactionData.put("callbackTime", new Date());
        transactionCache.put(txnRefNo, transactionData);

        // 发送通知到客户端（如果实现了 WebSocket 等实时通知机制）
        sendNotificationToClient(txnRefNo, success);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("message", callbackData.get("pp_ResponseMessage"));
        response.put("transactionId", txnRefNo);

        return response;
    }

    /**
     * 发送通知到客户端
     *
     * 注：这里可以实现 WebSocket 等实时通知机制
     *
     * @param txnRefNo 交易参考号
     * @param success 是否成功
     */
    private void sendNotificationToClient(String txnRefNo, boolean success) {
        // 这里可以实现 WebSocket 等实时通知机制
        logger.info("发送通知到客户端: 交易 {}, 状态: {}", txnRefNo, success ? "成功" : "失败");

        // 示例：如果项目中使用了 WebSocketServer，可以这样调用
        try {
            // WebSocketServer.sendMessage(txnRefNo, "{\"type\":\"payment_update\",\"txnRefNo\":\"" + txnRefNo + "\",\"success\":" + success + "}");
        } catch (Exception e) {
            logger.error("发送 WebSocket 通知失败", e);
        }
    }

    /**
     * 生成交易参考号
     *
     * @return 唯一交易参考号
     */
//    private String generateTxnRefNo() {
//        // 格式: TR + 随机数，总长度为20位
//        // 例如: TR20220518150213
//        String prefix = "TR";
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Karachi")); // 设置为巴基斯坦时间
//        String dateStr = sdf.format(new Date());
//        int randomNum = 10000 + new Random().nextInt(90000);
//        return prefix + dateStr + randomNum;
//    }

    /**
     * 生成交易日期时间（巴基斯坦时间 PKT）
     *
     * @return 当前日期时间，格式：yyyyMMddHHmmss
     */
    private String generateTxnDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Karachi")); // 设置为巴基斯坦时间
        return sdf.format(new Date());
    }

    /**
     * 生成交易过期日期时间（巴基斯坦时间 PKT，当前时间 + 1 天）
     *
     * @return 过期日期时间，格式：yyyyMMddHHmmss
     */
    private String generateExpiryDateTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1); // 加 1 天
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Karachi")); // 设置为巴基斯坦时间
        return sdf.format(calendar.getTime());
    }
    private String generateTxnRefNo() {
        // 格式: TR + 当前时间戳（yyyyMMddHHmmss）+ 5位随机数字 + 4位随机字母
        // 确保总长度为 20 个字符
        String prefix = "TR";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Karachi")); // 设置为巴基斯坦时间
        String dateStr = sdf.format(new Date());
//        int randomNum = 10000 + new Random().nextInt(90000); // 生成5位随机数字
        String randomLetters = generateRandomLetters(4); // 生成4位随机字母

        // 拼接并确保总长度为 20 个字符
        return prefix + dateStr + randomLetters;
    }

    // 辅助方法：生成指定长度的随机字母
    private String generateRandomLetters(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            char randomChar = (char) ('A' + random.nextInt(26)); // 随机生成大写字母
            sb.append(randomChar);
        }
        return sb.toString();
    }


//    /**
//     * 生成请求的安全哈希
//     *
//     * @param requestData 请求数据
//     * @return 安全哈希字符串
//     */
//    private String generateSecureHashForRequest(Map<String, String> requestData) {
//        try {
//            // 准备用于计算哈希的数据 - 按照JazzCash文档示例的字段名和顺序
//            Map<String, String> hashData = new TreeMap<>(String.CASE_INSENSITIVE_ORDER); // 使用不区分大小写的TreeMap
//
//            // 添加所有以pp_开头的非空字段（不包括pp_SecureHash和pp_ReturnURL）
//            for (Map.Entry<String, String> entry : requestData.entrySet()) {
//                String key = entry.getKey();
//                String value = entry.getValue();
//                // 只包含非空值且不是SecureHash和ReturnURL的字段
//                if (key.toLowerCase().startsWith("pp_") &&
//                    !key.equalsIgnoreCase("pp_SecureHash") &&
//                    !key.equalsIgnoreCase("pp_ReturnURL") &&
//                    value != null && !value.isEmpty()) {
//                    // 转换为小写字段名，以匹配文档示例
//                    String lowercaseKey = key.toLowerCase();
//
//                    // 特殊处理某些字段名
//                    if (lowercaseKey.equals("pp_billreference")) {
//                        lowercaseKey = "pp_billref";
//                    } else if (lowercaseKey.equals("pp_mobilenumber")) {
//                        lowercaseKey = "pp_mobile";
//                    }
//
//                    hashData.put(lowercaseKey, value);
//                }
//            }
//
//            // 打印排序后的数据，便于调试
//            logger.info("按字母顺序排列的哈希字段及其值: {}", hashData);
//
//            // 构建要哈希的字符串 - 只连接值（不包括键），用&分隔
//            StringBuilder valuesBuilder = new StringBuilder();
//            int count = 0;
//            int total = hashData.size();
//            for (String value : hashData.values()) {
//                valuesBuilder.append(value);
//                count++;
//                // 只在非最后一个值后添加&
//                if (count < total) {
//                    valuesBuilder.append("&");
//                }
//            }
//
//            // 获取值字符串
//            String values = valuesBuilder.toString();
//
//            // 添加integrity salt作为前缀
//            String toBeHashed = integritySalt + "&" + values;
//
//            logger.info("要哈希的完整字符串: {}", toBeHashed);
//
//            // 使用HMAC-SHA256算法
//            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
//            SecretKeySpec secretKey = new SecretKeySpec(integritySalt.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
//            hmacSha256.init(secretKey);
//            byte[] hash = hmacSha256.doFinal(toBeHashed.getBytes(StandardCharsets.UTF_8));
//            String secureHash = new String(Hex.encodeHex(hash)).toUpperCase();
//            logger.info("生成的安全哈希: {}", secureHash);
//
//            return secureHash;
//        } catch (Exception e) {
//            logger.error("生成安全哈希时出错", e);
//            return "";
//        }
//    }

    /**
     * 获取所有交易
     *
     * @return 所有交易的列表
     */
    public Map<String, Object> getAllTransactions() {
        Map<String, Object> response = new HashMap<>();
        response.put("count", transactionCache.size());
        response.put("transactions", transactionCache);
        return response;
    }

    /**
     * 获取用户友好的错误信息
     *
     * @param responseCode JazzCash 响应码
     * @param originalMessage 原始错误信息
     * @return 用户友好的错误信息
     */
    private String getUserFriendlyErrorMessage(String responseCode, String originalMessage) {
        // 根据 JazzCash 错误码提供用户友好的错误消息
        switch (responseCode) {
            case "110":
                return "商户信息验证失败，请联系客服";
            case "111":
                return "商户账户状态异常，请联系客服";
            case "112":
                return "商户余额不足，请联系客服";
            case "113":
                return "商户权限不足，请联系客服";
            case "114":
                return "商户 IP 受限，请联系客服";
            case "115":
                return "交易类型不支持，请尝试其他支付方式";
            case "116":
                return "交易币种不支持，请尝试其他币种";
            case "117":
                return "交易金额超限，请减少金额或联系客服";
            case "118":
                return "交易已过期，请重新发起交易";
            case "119":
                return "交易已完成，请勿重复支付";
            case "120":
                return "手机号码格式错误，请检查后重试";
            case "121":
                return "身份证号码格式错误，请检查后重试";
            case "122":
                return "账户余额不足，请充值后重试";
            case "123":
                return "交易金额超出限额，请联系您的银行";
            case "124":
                return "银行卡信息有误，请检查后重试";
            case "125":
                return "银行服务暂时不可用，请稍后再试";
            case "126":
                return "网络连接不稳定，请检查网络后重试";
            case "127":
                return "交易失败，您的手机上未确认交易";
            case "128":
                return "交易失败，您的银行拒绝了此交易";
            case "129":
                return "支付渠道暂时不可用，请稍后再试";
            default:
                if (originalMessage != null && originalMessage.contains("valid value for pp_Password")) {
                    return "商户信息验证失败，请联系客服";
                }
                return "支付请求失败，请稍后再试 [" + responseCode + "]";
        }
    }
}

