package com.example.demo;

import com.google.gson.Gson;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Easypaisa API 服务类
 * 
 * 该服务负责直接与Easypaisa API通信，处理支付请求和响应
 */
@Service
public class EasypaisaApiService {
    private static final Logger logger = LoggerFactory.getLogger(EasypaisaApiService.class);
    
    // Easypaisa API 相关配置
    private static final String EASYPAISA_API_URL = "https://sandbox.easypay.easypaisa.com.pk/easypay/Index.jsf";
    private static final String EASYPAISA_INQUIRY_URL = "https://sandbox.easypay.easypaisa.com.pk/easypay/Inquiry";
    
    // 商户配置信息，实际应用中应从配置文件或环境变量中读取
    @Value("${easypaisa.merchant.id:EP12345}")
    private String merchantId;
    
    @Value("${easypaisa.password:password123}")
    private String password;
    
    @Value("${easypaisa.integrity.salt:123456789}")
    private String integritySalt;
    
    @Value("${easypaisa.return.url:http://10.1.128.210:8080/easypaisa/api/callback}")
    private String returnUrl;
    
    private final RestTemplate restTemplate;
    private final Gson gson;
    
    // 存储交易信息的内存缓存
    private static final Map<String, Map<String, Object>> transactionCache = new HashMap<>();
    
    public EasypaisaApiService() {
        this.restTemplate = new RestTemplate();
        this.gson = new Gson();
    }
    
    /**
     * 创建钱包支付交易
     * 
     * @param mobileNumber 用户手机号
     * @param email 用户邮箱
     * @param amount 支付金额（巴基斯坦卢比）
     * @param description 交易描述
     * @return 支付结果
     */
    public Map<String, Object> createWalletPayment(String mobileNumber, String email, String amount, String description) {
        logger.info("创建Easypaisa钱包支付交易 - 手机号: {}, 邮箱: {}, 金额: {}", mobileNumber, email, amount);
        
        try {
            // 生成交易参考号
            String txnRefNo = generateTxnRefNo();
            
            // 生成交易日期时间
            String txnDateTime = generateTxnDateTime();
            String txnExpiryDateTime = generateExpiryDateTime();
            
            // 准备请求数据
            Map<String, String> requestData = new HashMap<>();
            requestData.put("storeId", merchantId);
            requestData.put("amount", amount);
            requestData.put("postBackURL", returnUrl);
            requestData.put("orderRefNum", txnRefNo);
            requestData.put("expiryDate", txnExpiryDateTime);
            requestData.put("merchantHashedReq", "");
            requestData.put("autoRedirect", "1");
            requestData.put("paymentMethod", "MA_PAYMENT_METHOD");
            requestData.put("emailAddr", email);
            requestData.put("mobileNum", mobileNumber);
            requestData.put("tokenExpiry", "");
            
            // 生成安全哈希
            String hashRequest = generateHashRequest(requestData);
            requestData.put("merchantHashedReq", hashRequest);
            
            logger.info("准备发送到Easypaisa的请求数据: {}", requestData);
            
            // 创建HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 将Map转换为JSON
            String jsonPayload = gson.toJson(requestData);
            HttpEntity<String> entity = new HttpEntity<>(jsonPayload, headers);
            
            // 发送请求
            logger.info("发送请求到Easypaisa API: {}", EASYPAISA_API_URL);
            ResponseEntity<String> response = restTemplate.exchange(
                    EASYPAISA_API_URL,
                    HttpMethod.POST,
                    entity,
                    String.class
            );
            
            // 解析响应
            String responseBody = response.getBody();
            logger.info("收到Easypaisa API响应: {}", responseBody);
            
            Map<String, Object> responseMap = gson.fromJson(responseBody, Map.class);
            responseMap.put("txnRefNo", txnRefNo);
            
            // 检查响应码并提供友好的错误消息
            String responseCode = (String) responseMap.get("responseCode");
            String responseMessage = (String) responseMap.get("responseDesc");
            boolean success = "0000".equals(responseCode);
            
            if (!success) {
                // 根据错误码提供友好的错误消息
                String userFriendlyMessage = getUserFriendlyErrorMessage(responseCode, responseMessage);
                responseMap.put("userMessage", userFriendlyMessage);
                responseMap.put("success", false);
                logger.info("支付请求失败，用户友好错误信息: {}", userFriendlyMessage);
            } else {
                responseMap.put("success", true);
                responseMap.put("userMessage", "支付请求已发送，请在您的手机上完成支付");
            }
            
            // 初始化交易状态为PENDING
            Map<String, Object> transactionData = new HashMap<>(responseMap);
            transactionData.put("status", success ? "PENDING" : "FAILED");
            transactionData.put("createdAt", new Date());
            
            // 存储交易信息
            transactionCache.put(txnRefNo, transactionData);
            
            return responseMap;
        } catch (Exception e) {
            logger.error("创建钱包支付交易时出错", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            errorResponse.put("userMessage", "支付服务暂时不可用，请稍后再试");
            return errorResponse;
        }
    }
    
    /**
     * 查询交易状态
     * 
     * @param txnRefNo 交易参考号
     * @return 交易状态
     */
    public Map<String, Object> inquireTransaction(String txnRefNo) {
        logger.info("查询交易状态: {}", txnRefNo);
        
        // 首先检查缓存
        if (transactionCache.containsKey(txnRefNo)) {
            Map<String, Object> cachedTransaction = transactionCache.get(txnRefNo);
            
            // 如果状态是COMPLETED或FAILED，直接返回缓存
            String status = (String) cachedTransaction.getOrDefault("status", "PENDING");
            if ("COMPLETED".equals(status) || "FAILED".equals(status)) {
                logger.info("从缓存中找到已完成交易: {}, 状态: {}", txnRefNo, status);
                return cachedTransaction;
            }
            
            // 检查交易是否已过期
            Date createdAt = (Date) cachedTransaction.get("createdAt");
            if (createdAt != null) {
                long diffMinutes = (new Date().getTime() - createdAt.getTime()) / (60 * 1000);
                // 如果交易创建超过30分钟但仍是PENDING，标记为EXPIRED
                if (diffMinutes > 30 && "PENDING".equals(status)) {
                    logger.info("交易已过期: {}", txnRefNo);
                    cachedTransaction.put("status", "EXPIRED");
                    cachedTransaction.put("message", "交易已过期");
                    return cachedTransaction;
                }
            }
            
            logger.info("从缓存中找到进行中交易: {}", cachedTransaction);
        }
        
        try {
            // 准备请求数据
            Map<String, String> requestData = new HashMap<>();
            requestData.put("storeId", merchantId);
            requestData.put("orderId", txnRefNo);
            
            // 生成安全哈希
            String hashRequest = generateHashRequest(requestData);
            requestData.put("merchantHashedReq", hashRequest);
            
            // 创建HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 将Map转换为JSON
            String jsonPayload = gson.toJson(requestData);
            HttpEntity<String> entity = new HttpEntity<>(jsonPayload, headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    EASYPAISA_INQUIRY_URL,
                    HttpMethod.POST,
                    entity,
                    String.class
            );
            
            // 解析响应
            String responseBody = response.getBody();
            logger.info("收到Easypaisa状态查询响应: {}", responseBody);
            
            Map<String, Object> responseMap = gson.fromJson(responseBody, Map.class);
            
            // 解析响应状态
            String responseCode = (String) responseMap.get("responseCode");
            boolean success = "0000".equals(responseCode);
            
            // 更新交易状态
            Map<String, Object> transactionData = new HashMap<>(responseMap);
            transactionData.put("status", success ? "COMPLETED" : "FAILED");
            transactionData.put("success", success);
            transactionData.put("updatedAt", new Date());
            
            // 存储交易信息
            transactionCache.put(txnRefNo, transactionData);
            
            return transactionData;
        } catch (Exception e) {
            logger.error("查询交易状态时出错", e);
            
            // 出错时返回缓存中的数据（如果有）
            if (transactionCache.containsKey(txnRefNo)) {
                return transactionCache.get(txnRefNo);
            }
            
            // 如果没有缓存，返回错误信息
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            errorResponse.put("userMessage", "查询交易状态时出错，请稍后再试");
            return errorResponse;
        }
    }
    
    /**
     * 处理API回调
     * 
     * @param callbackData 回调数据
     * @return 处理结果
     */
    public Map<String, Object> handleApiCallback(Map<String, String> callbackData) {
        logger.info("处理Easypaisa API回调: {}", callbackData);
        
        try {
            String txnRefNo = callbackData.get("orderRefNum");
            String responseCode = callbackData.get("responseCode");
            
            if (txnRefNo == null || txnRefNo.isEmpty()) {
                logger.error("回调数据中没有交易参考号");
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("error", "回调数据中没有交易参考号");
                return errorResponse;
            }
            
            boolean success = "0000".equals(responseCode);
            
            // 更新交易状态
            Map<String, Object> transactionData = new HashMap<>(callbackData);
            transactionData.put("status", success ? "COMPLETED" : "FAILED");
            transactionData.put("success", success);
            transactionData.put("updatedAt", new Date());
            
            // 存储交易信息
            transactionCache.put(txnRefNo, transactionData);
            
            // 发送通知给客户端（可选）
            sendNotificationToClient(txnRefNo, success);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "回调处理成功");
            return response;
        } catch (Exception e) {
            logger.error("处理API回调时出错", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 发送通知给客户端
     * 
     * @param txnRefNo 交易参考号
     * @param success 是否成功
     */
    private void sendNotificationToClient(String txnRefNo, boolean success) {
        try {
            // 这里可以使用WebSocket或其他方式通知客户端
            // 例如，使用WebSocketServer发送消息
            Map<String, Object> message = new HashMap<>();
            message.put("type", "PAYMENT_STATUS");
            message.put("txnRefNo", txnRefNo);
            message.put("success", success);
            message.put("timestamp", new Date().getTime());
            
//            WebSocketServer.sendMessageToAll(gson.toJson(message));
        } catch (Exception e) {
            logger.error("发送通知给客户端时出错", e);
        }
    }
    
    /**
     * 生成交易参考号
     * 
     * @return 交易参考号
     */
    private String generateTxnRefNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String random = String.format("%04d", new Random().nextInt(10000));
        return "EP" + timestamp + random;
    }
    
    /**
     * 生成交易日期时间
     * 
     * @return 交易日期时间
     */
    private String generateTxnDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }
    
    /**
     * 生成交易过期时间
     * 
     * @return 交易过期时间
     */
    private String generateExpiryDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, 30); // 30分钟后过期
        return sdf.format(calendar.getTime());
    }
    
    /**
     * 生成安全哈希
     * 
     * @param data 请求数据
     * @return 安全哈希
     */
    private String generateHashRequest(Map<String, String> data) {
        try {
            // 按照Easypaisa的要求构建哈希字符串
            StringBuilder sb = new StringBuilder();
            sb.append(data.get("amount")).append("&");
            sb.append(data.get("storeId")).append("&");
            sb.append(data.get("postBackURL")).append("&");
            sb.append(data.get("orderRefNum")).append("&");
            sb.append(integritySalt);
            
            String dataToHash = sb.toString();
            
            // 使用HMAC-SHA256算法生成哈希
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(integritySalt.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            
            byte[] hash = sha256_HMAC.doFinal(dataToHash.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(hash);
        } catch (Exception e) {
            logger.error("生成安全哈希时出错", e);
            throw new RuntimeException("生成安全哈希时出错", e);
        }
    }
    
    /**
     * 获取所有交易
     * 
     * @return 所有交易
     */
    public Map<String, Object> getAllTransactions() {
        logger.info("获取所有交易");
        
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("transactions", transactionCache);
            return response;
        } catch (Exception e) {
            logger.error("获取所有交易时出错", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 获取用户友好的错误消息
     * 
     * @param responseCode 响应码
     * @param originalMessage 原始错误消息
     * @return 用户友好的错误消息
     */
    private String getUserFriendlyErrorMessage(String responseCode, String originalMessage) {
        if (originalMessage != null && !originalMessage.isEmpty()) {
            return originalMessage;
        }
        
        switch (responseCode) {
            case "0001":
                return "交易失败，请检查您的支付信息";
            case "0002":
                return "交易超时，请稍后再试";
            case "0003":
                return "交易被拒绝，请联系您的银行";
            case "0004":
                return "余额不足，请充值后再试";
            case "0005":
                return "无效的交易参数";
            case "0006":
                return "系统错误，请稍后再试";
            default:
                return "支付处理时出错，请稍后再试";
        }
    }
} 