package com.example.aiproxy.controller;

import com.example.aiproxy.dto.ChatRequest;
import com.example.aiproxy.dto.ChatResponse;
import com.example.aiproxy.model.ChatMessage;
import com.example.aiproxy.model.ChatSession;
import com.example.aiproxy.service.ChatService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import jakarta.validation.Valid;
import java.time.Duration;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/chat")
@CrossOrigin(origins = "*")
public class ChatController {
    
    private static final Logger logger = LoggerFactory.getLogger(ChatController.class);
    
    @Autowired
    private ChatService chatService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 流式聊天接口
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamChat(@Valid @RequestBody ChatRequest request) {
        logger.info("收到流式聊天请求: {}", request.getMessage());
        
        return chatService.streamChat(request)
            .map(this::formatSseMessage)
            .concatWith(Flux.just("data: [DONE]\n\n"))
            .delayElements(Duration.ofMillis(50)); // 添加小延迟以确保流式效果
    }
    
    /**
     * 非流式聊天接口
     */
    @PostMapping("/message")
    public ResponseEntity<ChatResponse> sendMessage(@Valid @RequestBody ChatRequest request) {
        logger.info("收到聊天请求: {}", request.getMessage());
        
        try {
            ChatResponse response = chatService.chat(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("处理聊天请求失败", e);
            ChatResponse errorResponse = new ChatResponse("抱歉，处理您的请求时出现了错误，请稍后重试。");
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 获取会话历史
     */
    @GetMapping("/sessions/{sessionId}/messages")
    public ResponseEntity<List<ChatMessage>> getSessionHistory(
            @PathVariable String sessionId,
            @RequestParam String userId) {
        
        try {
            List<ChatMessage> messages = chatService.getSessionHistory(sessionId, userId);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("获取会话历史失败", e);
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 获取用户的所有会话
     */
    @GetMapping("/sessions")
    public ResponseEntity<List<ChatSession>> getUserSessions(@RequestParam String userId) {
        try {
            List<ChatSession> sessions = chatService.getUserSessions(userId);
            return ResponseEntity.ok(sessions);
        } catch (Exception e) {
            logger.error("获取用户会话失败", e);
            return ResponseEntity.ok(List.of());
        }
    }
    
    /**
     * 创建新会话
     */
    @PostMapping("/sessions")
    public ResponseEntity<ChatSession> createSession(@RequestBody Map<String, String> request) {
        String userId = request.get("userId");
        String title = request.getOrDefault("title", "新对话");
        
        try {
            ChatSession session = chatService.createSession(userId, title);
            return ResponseEntity.ok(session);
        } catch (Exception e) {
            logger.error("创建会话失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 删除会话
     */
    @DeleteMapping("/sessions/{sessionId}")
    public ResponseEntity<Void> deleteSession(
            @PathVariable String sessionId,
            @RequestParam String userId) {
        
        try {
            chatService.deleteSession(sessionId, userId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("删除会话失败", e);
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 更新会话标题
     */
    @PutMapping("/sessions/{sessionId}/title")
    public ResponseEntity<ChatSession> updateSessionTitle(
            @PathVariable String sessionId,
            @RequestParam String userId,
            @RequestBody Map<String, String> request) {
        
        String newTitle = request.get("title");
        if (newTitle == null || newTitle.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        try {
            ChatSession session = chatService.updateSessionTitle(sessionId, userId, newTitle);
            return ResponseEntity.ok(session);
        } catch (Exception e) {
            logger.error("更新会话标题失败", e);
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "healthy",
            "timestamp", System.currentTimeMillis(),
            "service", "ai-proxy-service"
        ));
    }
    
    /**
     * 格式化SSE消息
     */
    private String formatSseMessage(ChatResponse response) {
        try {
            String json = objectMapper.writeValueAsString(response);
            return "data: " + json + "\n\n";
        } catch (Exception e) {
            logger.error("格式化SSE消息失败", e);
            return "data: {\"error\":\"消息格式化失败\"}\n\n";
        }
    }
}
