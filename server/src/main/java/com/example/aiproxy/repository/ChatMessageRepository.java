package com.example.aiproxy.repository;

import com.example.aiproxy.model.ChatMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {
    
    /**
     * 根据会话ID查找消息，按创建时间排序
     */
    List<ChatMessage> findBySessionIdOrderByCreatedAtAsc(String sessionId);
    
    /**
     * 根据会话ID查找最近的N条消息
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.sessionId = :sessionId ORDER BY m.createdAt DESC LIMIT :limit")
    List<ChatMessage> findRecentMessagesBySessionId(@Param("sessionId") String sessionId, @Param("limit") int limit);
    
    /**
     * 根据用户ID查找所有消息
     */
    @Query("SELECT m FROM ChatMessage m JOIN ChatSession s ON m.sessionId = s.id WHERE s.userId = :userId ORDER BY m.createdAt DESC")
    List<ChatMessage> findByUserId(@Param("userId") String userId);
    
    /**
     * 删除指定会话的所有消息
     */
    void deleteBySessionId(String sessionId);
}
