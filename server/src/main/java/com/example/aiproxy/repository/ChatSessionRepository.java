package com.example.aiproxy.repository;

import com.example.aiproxy.model.ChatSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ChatSessionRepository extends JpaRepository<ChatSession, String> {
    
    /**
     * 根据用户ID查找活跃的会话
     */
    List<ChatSession> findByUserIdAndIsActiveTrueOrderByUpdatedAtDesc(String userId);
    
    /**
     * 根据用户ID查找所有会话
     */
    List<ChatSession> findByUserIdOrderByUpdatedAtDesc(String userId);
    
    /**
     * 根据用户ID和会话ID查找会话
     */
    Optional<ChatSession> findByIdAndUserId(String id, String userId);
    
    /**
     * 统计用户的活跃会话数量
     */
    @Query("SELECT COUNT(s) FROM ChatSession s WHERE s.userId = :userId AND s.isActive = true")
    long countActiveSessionsByUserId(@Param("userId") String userId);
}
