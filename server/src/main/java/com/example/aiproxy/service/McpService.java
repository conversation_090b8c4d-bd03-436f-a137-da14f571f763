package com.example.aiproxy.service;

import java.util.List;
import java.util.Map;

/**
 * MCP服务接口，负责与MCP服务器通信
 */
public interface McpService {
    
    /**
     * 调用MCP工具
     * @param toolName 工具名称
     * @param parameters 工具参数
     * @return 工具执行结果
     */
    Map<String, Object> callTool(String toolName, Map<String, Object> parameters);
    
    /**
     * 获取可用的工具列表
     * @return 工具列表
     */
    List<Map<String, Object>> getAvailableTools();
    
    /**
     * 初始化MCP连接
     * @return 是否初始化成功
     */
    boolean initializeConnection();
    
    /**
     * 检查MCP服务器状态
     * @return 是否连接正常
     */
    boolean isConnected();
    
    /**
     * 关闭MCP连接
     */
    void closeConnection();
}
