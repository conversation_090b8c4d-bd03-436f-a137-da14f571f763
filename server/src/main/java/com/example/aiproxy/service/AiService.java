package com.example.aiproxy.service;

import com.example.aiproxy.dto.ChatResponse;
import com.example.aiproxy.model.ChatMessage;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

@Service
public interface AiService {
    
    /**
     * 流式聊天响应
     * @param messages 历史消息
     * @param currentMessage 当前用户消息
     * @return 流式响应
     */
    Flux<ChatResponse> streamChat(List<ChatMessage> messages, String currentMessage);
    
    /**
     * 非流式聊天响应
     * @param messages 历史消息
     * @param currentMessage 当前用户消息
     * @return 完整响应
     */
    ChatResponse chat(List<ChatMessage> messages, String currentMessage);
    
    /**
     * 调用MCP工具
     * @param toolName 工具名称
     * @param parameters 工具参数
     * @return 工具执行结果
     */
    Map<String, Object> callMcpTool(String toolName, Map<String, Object> parameters);
    
    /**
     * 获取可用的MCP工具列表
     * @return 工具列表
     */
    List<Map<String, Object>> getAvailableTools();
    
    /**
     * 生成会话标题
     * @param firstMessage 第一条消息
     * @return 生成的标题
     */
    String generateSessionTitle(String firstMessage);
}
