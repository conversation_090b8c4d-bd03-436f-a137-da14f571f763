package com.example.aiproxy.service.impl;

import com.example.aiproxy.service.McpService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * MCP服务实现，通过stdio与MCP服务器通信
 */
@Service
public class McpServiceImpl implements McpService {
    
    private static final Logger logger = LoggerFactory.getLogger(McpServiceImpl.class);
    
    @Value("${mcp.server.path:../server}")
    private String mcpServerPath;
    
    @Value("${mcp.server.main-class:com.example.demo.DemoApplication}")
    private String mcpMainClass;
    
    private Process mcpProcess;
    private BufferedWriter processInput;
    private BufferedReader processOutput;
    private ObjectMapper objectMapper = new ObjectMapper();
    private boolean connected = false;
    
    @Override
    public Map<String, Object> callTool(String toolName, Map<String, Object> parameters) {
        if (!isConnected()) {
            logger.warn("MCP服务器未连接，尝试重新连接");
            if (!initializeConnection()) {
                return Map.of("error", "MCP服务器连接失败");
            }
        }
        
        try {
            // 构造MCP请求
            Map<String, Object> request = Map.of(
                "jsonrpc", "2.0",
                "id", UUID.randomUUID().toString(),
                "method", "tools/call",
                "params", Map.of(
                    "name", toolName,
                    "arguments", parameters != null ? parameters : Map.of()
                )
            );
            
            // 发送请求
            String requestJson = objectMapper.writeValueAsString(request);
            logger.debug("发送MCP请求: {}", requestJson);
            
            processInput.write(requestJson);
            processInput.newLine();
            processInput.flush();
            
            // 读取响应
            String responseJson = processOutput.readLine();
            if (responseJson != null) {
                logger.debug("收到MCP响应: {}", responseJson);
                Map<String, Object> response = objectMapper.readValue(responseJson, Map.class);
                
                if (response.containsKey("result")) {
                    return (Map<String, Object>) response.get("result");
                } else if (response.containsKey("error")) {
                    return Map.of("error", response.get("error"));
                }
            }
            
        } catch (Exception e) {
            logger.error("调用MCP工具失败", e);
            return Map.of("error", "工具调用失败: " + e.getMessage());
        }
        
        return Map.of("error", "未知错误");
    }
    
    @Override
    public List<Map<String, Object>> getAvailableTools() {
        if (!isConnected()) {
            logger.warn("MCP服务器未连接，返回空工具列表");
            return Collections.emptyList();
        }
        
        try {
            // 构造获取工具列表的请求
            Map<String, Object> request = Map.of(
                "jsonrpc", "2.0",
                "id", UUID.randomUUID().toString(),
                "method", "tools/list",
                "params", Map.of()
            );
            
            String requestJson = objectMapper.writeValueAsString(request);
            logger.debug("发送工具列表请求: {}", requestJson);
            
            processInput.write(requestJson);
            processInput.newLine();
            processInput.flush();
            
            String responseJson = processOutput.readLine();
            if (responseJson != null) {
                logger.debug("收到工具列表响应: {}", responseJson);
                Map<String, Object> response = objectMapper.readValue(responseJson, Map.class);
                
                if (response.containsKey("result")) {
                    Map<String, Object> result = (Map<String, Object>) response.get("result");
                    if (result.containsKey("tools")) {
                        return (List<Map<String, Object>>) result.get("tools");
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("获取工具列表失败", e);
        }
        
        return Collections.emptyList();
    }
    
    @Override
    public boolean initializeConnection() {
        try {
            logger.info("初始化MCP连接，服务器路径: {}", mcpServerPath);
            
            // 启动MCP服务器进程
            ProcessBuilder pb = new ProcessBuilder(
                "java", "-cp", mcpServerPath + "/target/classes", mcpMainClass
            );
            pb.directory(new File(mcpServerPath));
            pb.redirectErrorStream(true);
            
            mcpProcess = pb.start();
            
            // 设置输入输出流
            processInput = new BufferedWriter(new OutputStreamWriter(mcpProcess.getOutputStream()));
            processOutput = new BufferedReader(new InputStreamReader(mcpProcess.getInputStream()));
            
            // 发送初始化请求
            Map<String, Object> initRequest = Map.of(
                "jsonrpc", "2.0",
                "id", "init",
                "method", "initialize",
                "params", Map.of(
                    "protocolVersion", "2025-03-26",
                    "capabilities", Map.of(),
                    "clientInfo", Map.of(
                        "name", "AI Proxy Service",
                        "version", "1.0.0"
                    )
                )
            );
            
            String initJson = objectMapper.writeValueAsString(initRequest);
            logger.debug("发送初始化请求: {}", initJson);
            
            processInput.write(initJson);
            processInput.newLine();
            processInput.flush();
            
            // 等待初始化响应
            String initResponse = processOutput.readLine();
            if (initResponse != null) {
                logger.debug("收到初始化响应: {}", initResponse);
                Map<String, Object> response = objectMapper.readValue(initResponse, Map.class);
                
                if (response.containsKey("result")) {
                    connected = true;
                    logger.info("MCP连接初始化成功");
                    return true;
                }
            }
            
        } catch (Exception e) {
            logger.error("MCP连接初始化失败", e);
            closeConnection();
        }
        
        return false;
    }
    
    @Override
    public boolean isConnected() {
        if (!connected || mcpProcess == null) {
            return false;
        }
        
        // 检查进程是否还在运行
        try {
            int exitCode = mcpProcess.exitValue();
            logger.warn("MCP进程已退出，退出码: {}", exitCode);
            connected = false;
            return false;
        } catch (IllegalThreadStateException e) {
            // 进程还在运行
            return true;
        }
    }
    
    @Override
    public void closeConnection() {
        connected = false;
        
        try {
            if (processInput != null) {
                processInput.close();
            }
        } catch (IOException e) {
            logger.warn("关闭输入流失败", e);
        }
        
        try {
            if (processOutput != null) {
                processOutput.close();
            }
        } catch (IOException e) {
            logger.warn("关闭输出流失败", e);
        }
        
        if (mcpProcess != null) {
            mcpProcess.destroy();
            try {
                if (!mcpProcess.waitFor(5, TimeUnit.SECONDS)) {
                    mcpProcess.destroyForcibly();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                mcpProcess.destroyForcibly();
            }
        }
        
        logger.info("MCP连接已关闭");
    }
}
