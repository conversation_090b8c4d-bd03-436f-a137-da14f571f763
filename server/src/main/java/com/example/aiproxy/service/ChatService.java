package com.example.aiproxy.service;

import com.example.aiproxy.dto.ChatRequest;
import com.example.aiproxy.dto.ChatResponse;
import com.example.aiproxy.model.ChatMessage;
import com.example.aiproxy.model.ChatSession;
import com.example.aiproxy.model.MessageRole;
import com.example.aiproxy.repository.ChatMessageRepository;
import com.example.aiproxy.repository.ChatSessionRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class ChatService {
    
    private static final Logger logger = LoggerFactory.getLogger(ChatService.class);
    
    @Autowired
    private ChatSessionRepository sessionRepository;
    
    @Autowired
    private ChatMessageRepository messageRepository;
    
    @Autowired
    private AiService aiService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 流式聊天
     */
    public Flux<ChatResponse> streamChat(ChatRequest request) {
        logger.info("开始流式聊天，会话ID: {}, 消息: {}", request.getSessionId(), request.getMessage());
        
        // 获取或创建会话
        ChatSession session = getOrCreateSession(request);
        
        // 保存用户消息
        ChatMessage userMessage = new ChatMessage(session.getId(), MessageRole.USER, request.getMessage());
        messageRepository.save(userMessage);
        
        // 获取历史消息
        List<ChatMessage> historyMessages = messageRepository.findBySessionIdOrderByCreatedAtAsc(session.getId());
        
        // 调用AI服务进行流式响应
        return aiService.streamChat(historyMessages, request.getMessage())
            .doOnNext(response -> {
                // 设置会话ID
                response.setSessionId(session.getId());
            })
            .doOnComplete(() -> {
                // 流式响应完成后，保存完整的AI响应
                // 这里可以收集所有的流式响应片段，组合成完整消息保存到数据库
                logger.info("流式聊天完成，会话ID: {}", session.getId());
            })
            .doOnError(error -> {
                logger.error("流式聊天出错，会话ID: " + session.getId(), error);
            });
    }
    
    /**
     * 非流式聊天
     */
    public ChatResponse chat(ChatRequest request) {
        logger.info("开始非流式聊天，会话ID: {}, 消息: {}", request.getSessionId(), request.getMessage());
        
        // 获取或创建会话
        ChatSession session = getOrCreateSession(request);
        
        // 保存用户消息
        ChatMessage userMessage = new ChatMessage(session.getId(), MessageRole.USER, request.getMessage());
        messageRepository.save(userMessage);
        
        // 获取历史消息
        List<ChatMessage> historyMessages = messageRepository.findBySessionIdOrderByCreatedAtAsc(session.getId());
        
        // 调用AI服务
        ChatResponse response = aiService.chat(historyMessages, request.getMessage());
        response.setSessionId(session.getId());
        
        // 保存AI响应
        ChatMessage aiMessage = new ChatMessage(session.getId(), MessageRole.ASSISTANT, response.getContent());
        aiMessage.setMarkdownContent(response.getMarkdownContent());
        
        if (response.getCustomData() != null) {
            try {
                aiMessage.setCustomData(objectMapper.writeValueAsString(response.getCustomData()));
            } catch (Exception e) {
                logger.warn("序列化自定义数据失败", e);
            }
        }
        
        messageRepository.save(aiMessage);
        
        return response;
    }
    
    /**
     * 获取会话历史
     */
    public List<ChatMessage> getSessionHistory(String sessionId, String userId) {
        // 验证会话所有权
        ChatSession session = sessionRepository.findByIdAndUserId(sessionId, userId)
            .orElseThrow(() -> new RuntimeException("会话不存在或无权限访问"));
        
        return messageRepository.findBySessionIdOrderByCreatedAtAsc(sessionId);
    }
    
    /**
     * 获取用户的所有会话
     */
    public List<ChatSession> getUserSessions(String userId) {
        return sessionRepository.findByUserIdAndIsActiveTrueOrderByUpdatedAtDesc(userId);
    }
    
    /**
     * 创建新会话
     */
    public ChatSession createSession(String userId, String title) {
        String sessionId = UUID.randomUUID().toString();
        ChatSession session = new ChatSession(sessionId, title, userId);
        return sessionRepository.save(session);
    }
    
    /**
     * 删除会话
     */
    public void deleteSession(String sessionId, String userId) {
        ChatSession session = sessionRepository.findByIdAndUserId(sessionId, userId)
            .orElseThrow(() -> new RuntimeException("会话不存在或无权限访问"));
        
        // 软删除
        session.setIsActive(false);
        sessionRepository.save(session);
        
        // 删除消息
        messageRepository.deleteBySessionId(sessionId);
    }
    
    /**
     * 更新会话标题
     */
    public ChatSession updateSessionTitle(String sessionId, String userId, String newTitle) {
        ChatSession session = sessionRepository.findByIdAndUserId(sessionId, userId)
            .orElseThrow(() -> new RuntimeException("会话不存在或无权限访问"));
        
        session.setTitle(newTitle);
        return sessionRepository.save(session);
    }
    
    /**
     * 获取或创建会话
     */
    private ChatSession getOrCreateSession(ChatRequest request) {
        if (request.getSessionId() != null) {
            // 验证会话存在且属于当前用户
            return sessionRepository.findByIdAndUserId(request.getSessionId(), request.getUserId())
                .orElseThrow(() -> new RuntimeException("会话不存在或无权限访问"));
        } else {
            // 创建新会话
            String title = aiService.generateSessionTitle(request.getMessage());
            return createSession(request.getUserId(), title);
        }
    }
}
