package com.example.aiproxy.service.impl;

import com.example.aiproxy.dto.ChatResponse;
import com.example.aiproxy.model.ChatMessage;
import com.example.aiproxy.service.AiService;
import com.example.aiproxy.service.McpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.*;

/**
 * Mock AI服务实现，用于演示和测试
 * 后续可以替换为真实的AI模型调用
 */
@Service
public class MockAiServiceImpl implements AiService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockAiServiceImpl.class);
    
    @Autowired
    private McpService mcpService;
    
    // 模拟的房产数据
    private static final List<Map<String, Object>> MOCK_PROPERTIES = Arrays.asList(
        Map.of(
            "id", "1",
            "title", "海景豪华公寓",
            "price", "580万",
            "area", "120㎡",
            "location", "深圳南山区",
            "type", "3室2厅2卫",
            "image", "https://example.com/property1.jpg"
        ),
        Map.of(
            "id", "2", 
            "title", "市中心精装修住宅",
            "price", "420万",
            "area", "95㎡", 
            "location", "深圳福田区",
            "type", "2室2厅1卫",
            "image", "https://example.com/property2.jpg"
        ),
        Map.of(
            "id", "3",
            "title", "学区房优质选择", 
            "price", "680万",
            "area", "140㎡",
            "location", "深圳罗湖区",
            "type", "4室2厅2卫",
            "image", "https://example.com/property3.jpg"
        )
    );
    
    @Override
    public Flux<ChatResponse> streamChat(List<ChatMessage> messages, String currentMessage) {
        logger.info("开始流式聊天，当前消息: {}", currentMessage);
        
        // 分析用户意图
        String intent = analyzeUserIntent(currentMessage);
        
        return switch (intent) {
            case "search_property" -> streamPropertySearch(currentMessage);
            case "property_recommendation" -> streamPropertyRecommendation();
            case "price_trend" -> streamPriceTrend();
            case "mcp_tool" -> streamMcpToolCall(currentMessage);
            default -> streamGeneralResponse(currentMessage);
        };
    }
    
    @Override
    public ChatResponse chat(List<ChatMessage> messages, String currentMessage) {
        // 非流式响应的实现
        StringBuilder content = new StringBuilder();
        streamChat(messages, currentMessage)
            .doOnNext(response -> content.append(response.getContent()))
            .blockLast();
        
        return ChatResponse.createCompleteResponse(null, content.toString(), content.toString());
    }
    
    @Override
    public Map<String, Object> callMcpTool(String toolName, Map<String, Object> parameters) {
        return mcpService.callTool(toolName, parameters);
    }
    
    @Override
    public List<Map<String, Object>> getAvailableTools() {
        return mcpService.getAvailableTools();
    }
    
    @Override
    public String generateSessionTitle(String firstMessage) {
        if (firstMessage.length() > 20) {
            return firstMessage.substring(0, 20) + "...";
        }
        return firstMessage;
    }
    
    /**
     * 分析用户意图
     */
    private String analyzeUserIntent(String message) {
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("找房") || lowerMessage.contains("房子") || lowerMessage.contains("买房")) {
            return "search_property";
        }
        if (lowerMessage.contains("推荐") || lowerMessage.contains("建议")) {
            return "property_recommendation";
        }
        if (lowerMessage.contains("价格") || lowerMessage.contains("趋势") || lowerMessage.contains("走势")) {
            return "price_trend";
        }
        if (lowerMessage.contains("工具") || lowerMessage.contains("mcp")) {
            return "mcp_tool";
        }
        
        return "general";
    }
    
    /**
     * 流式房产搜索响应
     */
    private Flux<ChatResponse> streamPropertySearch(String message) {
        return Flux.concat(
            // 先发送文本响应
            Flux.just("正在为您搜索合适的房源...")
                .map(ChatResponse::createStreamChunk)
                .delayElements(Duration.ofMillis(100)),
            
            // 然后发送自定义数据（房产列表）
            Flux.just(ChatResponse.createCustomResponse(
                null,
                "为您找到以下房源：",
                Map.of(
                    "type", "property_list",
                    "properties", MOCK_PROPERTIES
                )
            )).delayElements(Duration.ofMillis(500))
        );
    }
    
    /**
     * 流式房产推荐响应
     */
    private Flux<ChatResponse> streamPropertyRecommendation() {
        return Flux.concat(
            Flux.just("基于您的需求，我为您推荐以下优质房源：")
                .map(ChatResponse::createStreamChunk)
                .delayElements(Duration.ofMillis(100)),
            
            Flux.just(ChatResponse.createCustomResponse(
                null,
                "",
                Map.of(
                    "type", "property_recommendation",
                    "recommendations", MOCK_PROPERTIES.subList(0, 2),
                    "reason", "这些房源位置优越，性价比高，符合您的预算范围。"
                )
            )).delayElements(Duration.ofMillis(800))
        );
    }
    
    /**
     * 流式价格趋势响应
     */
    private Flux<ChatResponse> streamPriceTrend() {
        List<Map<String, Object>> trendData = Arrays.asList(
            Map.of("month", "2024-01", "price", 52000),
            Map.of("month", "2024-02", "price", 53200),
            Map.of("month", "2024-03", "price", 54100),
            Map.of("month", "2024-04", "price", 55300),
            Map.of("month", "2024-05", "price", 56800),
            Map.of("month", "2024-06", "price", 58200)
        );
        
        return Flux.concat(
            Flux.just("正在分析最新的房价趋势...")
                .map(ChatResponse::createStreamChunk)
                .delayElements(Duration.ofMillis(100)),
            
            Flux.just(ChatResponse.createCustomResponse(
                null,
                "根据最新数据分析，深圳地区房价呈现稳步上涨趋势：",
                Map.of(
                    "type", "price_chart",
                    "chartData", trendData,
                    "analysis", "过去6个月房价上涨12.3%，预计未来仍有上涨空间。"
                )
            )).delayElements(Duration.ofMillis(1000))
        );
    }
    
    /**
     * 流式MCP工具调用响应
     */
    private Flux<ChatResponse> streamMcpToolCall(String message) {
        return Flux.concat(
            Flux.just("正在调用相关工具...")
                .map(ChatResponse::createStreamChunk)
                .delayElements(Duration.ofMillis(100)),
            
            Flux.just(ChatResponse.createCustomResponse(
                null,
                "工具调用完成，结果如下：",
                Map.of(
                    "type", "tool_result",
                    "tools", getAvailableTools(),
                    "result", "模拟工具调用结果"
                )
            )).delayElements(Duration.ofMillis(500))
        );
    }
    
    /**
     * 流式通用响应
     */
    private Flux<ChatResponse> streamGeneralResponse(String message) {
        String[] responses = {
            "您好！我是您的专属房产AI助手。",
            "我可以帮您搜索房源、分析价格趋势、提供购房建议。",
            "请告诉我您的具体需求，比如预算、地区、户型等。"
        };
        
        return Flux.fromArray(responses)
            .map(ChatResponse::createStreamChunk)
            .delayElements(Duration.ofMillis(300));
    }
}
