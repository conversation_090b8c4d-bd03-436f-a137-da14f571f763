package com.example.aiproxy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatResponse {
    
    private String id;
    private String sessionId;
    private String role;
    private String content;
    private String markdownContent;
    private Map<String, Object> customData; // 自定义数据，用于特殊UI组件
    private LocalDateTime timestamp;
    private boolean isComplete = false; // 标识是否是完整响应
    
    // 构造函数
    public ChatResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public ChatResponse(String content) {
        this();
        this.content = content;
        this.role = "assistant";
    }
    
    public ChatResponse(String sessionId, String content, String markdownContent) {
        this();
        this.sessionId = sessionId;
        this.content = content;
        this.markdownContent = markdownContent;
        this.role = "assistant";
    }
    
    // 静态工厂方法
    public static ChatResponse createStreamChunk(String content) {
        ChatResponse response = new ChatResponse(content);
        response.setComplete(false);
        return response;
    }
    
    public static ChatResponse createCompleteResponse(String sessionId, String content, String markdownContent) {
        ChatResponse response = new ChatResponse(sessionId, content, markdownContent);
        response.setComplete(true);
        return response;
    }
    
    public static ChatResponse createCustomResponse(String sessionId, String content, Map<String, Object> customData) {
        ChatResponse response = new ChatResponse(sessionId, content, null);
        response.setCustomData(customData);
        response.setComplete(true);
        return response;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getMarkdownContent() {
        return markdownContent;
    }
    
    public void setMarkdownContent(String markdownContent) {
        this.markdownContent = markdownContent;
    }
    
    public Map<String, Object> getCustomData() {
        return customData;
    }
    
    public void setCustomData(Map<String, Object> customData) {
        this.customData = customData;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public boolean isComplete() {
        return isComplete;
    }
    
    public void setComplete(boolean complete) {
        isComplete = complete;
    }
}
