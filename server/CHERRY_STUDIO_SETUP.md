# Cherry Studio MCP 集成配置指南

## 概述

本文档介绍如何将我们的Java Spring Boot MCP服务器与Cherry Studio集成。Cherry Studio现在支持通过SSE (Server-Sent Events) 传输协议连接到MCP服务器。

## 服务器配置

### 1. 启动MCP服务器

```bash
cd /path/to/your/server
./mvnw spring-boot:run
```

服务器将在 `http://localhost:8080` 启动。

### 2. 验证服务器状态

```bash
curl http://localhost:8080/mcp/health
```

应该返回：
```json
{
  "status": "healthy",
  "timestamp": 1749627391225
}
```

## Cherry Studio 配置

### MCP 服务器设置

在Cherry Studio中添加新的MCP服务器：

1. **服务器类型**: SSE (Server-Sent Events)
2. **服务器URL**: `http://localhost:8080/mcp/sse`
3. **参数设置**:
   - `uri`: `/mcp/sse`
   - `name`: `Demo MCP Server` (可自定义)

### 完整URL示例

Cherry Studio将使用以下URL格式连接：
```
http://localhost:8080/mcp/sse?uri=/mcp/sse&name=Demo%20MCP%20Server
```

## 支持的功能

### 1. 工具列表 (tools/list)

服务器提供以下10个工具：

1. **get_weather** - 获取指定城市的天气信息
2. **file_operation** - 文件操作工具 (读取、写入、列表)
3. **get_current_time** - 获取当前系统时间
4. **translate_text** - 翻译文本到指定语言
5. **database_query** - 数据库查询工具
6. **image_processing** - 图像处理工具
7. **add_numbers** - 计算两个数字的和
8. **http_request** - HTTP请求工具
9. **get_user_info** - 获取用户信息
10. **execute_code** - 代码执行工具

### 2. 工具调用 (tools/call)

所有工具都支持通过标准MCP协议调用。

### 3. 实时通信

通过SSE连接，支持：
- 实时工具列表更新
- 双向消息传递
- 连接状态监控

## 测试验证

### 使用提供的测试脚本

```bash
python3 test_cherry_studio_mcp.py
```

### 手动测试

#### 1. 测试SSE连接

```bash
curl -X GET "http://localhost:8080/mcp/sse?uri=/mcp/sse&name=Demo%20MCP%20Server" \
  -H "Accept: text/event-stream"
```

#### 2. 测试工具列表

```bash
curl -X POST "http://localhost:8080/mcp/sse?uri=/mcp/sse&name=Demo%20MCP%20Server" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":"test-1","method":"tools/list","params":{}}'
```

#### 3. 测试工具调用

```bash
curl -X POST "http://localhost:8080/mcp/sse?uri=/mcp/sse&name=Demo%20MCP%20Server" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":"test-2","method":"tools/call","params":{"name":"get_current_time","arguments":{}}}'
```

## 故障排除

### 常见问题

1. **连接失败 (HTTP 404)**
   - 确保服务器正在运行
   - 检查URL路径是否正确
   - 验证端口8080是否可访问

2. **SSE连接超时**
   - 这是正常现象，SSE是持久连接
   - Cherry Studio会自动处理重连

3. **工具调用失败**
   - 检查工具名称是否正确
   - 验证参数格式是否符合schema
   - 查看服务器日志获取详细错误信息

### 日志监控

服务器日志会显示：
- SSE连接建立和断开
- MCP请求和响应
- 工具调用详情

## 高级配置

### 自定义端口

如果需要使用不同端口，修改 `application.properties`:

```properties
server.port=9090
```

然后更新Cherry Studio配置中的URL。

### CORS配置

服务器已配置CORS支持，允许跨域访问。如需修改，请查看控制器中的 `@CrossOrigin` 注解。

### 安全配置

当前配置允许所有请求。生产环境中建议添加适当的认证和授权机制。

## 技术细节

### 协议支持

- **MCP协议版本**: 2025-03-26
- **传输方式**: SSE (Server-Sent Events)
- **数据格式**: JSON-RPC 2.0

### 端点映射

- **SSE连接**: `GET /mcp/sse`
- **MCP请求**: `POST /mcp/sse`
- **健康检查**: `GET /mcp/health`
- **状态查询**: `GET /mcp/sse/simple-status`

### 消息格式

#### 初始化消息
```json
{
  "jsonrpc": "2.0",
  "method": "notifications/initialized",
  "params": {
    "protocolVersion": "2025-03-26",
    "capabilities": {
      "tools": {"listChanged": true},
      "resources": {"listChanged": true}
    },
    "serverInfo": {
      "name": "Demo MCP Server",
      "version": "1.0.0"
    }
  }
}
```

## 支持

如有问题，请检查：
1. 服务器日志
2. Cherry Studio控制台
3. 网络连接状态
4. 防火墙设置
