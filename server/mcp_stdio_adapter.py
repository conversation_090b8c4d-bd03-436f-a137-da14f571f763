#!/usr/bin/env python3
"""
MCP STDIO适配器
将我们的HTTP MCP服务器适配为STDIO传输方式，供Cherry Studio使用
"""

import json
import sys
import requests
import asyncio
from typing import Dict, Any, Optional

class McpStdioAdapter:
    def __init__(self, http_server_url: str = "http://localhost:8080/mcp/"):
        self.http_server_url = http_server_url
        self.request_id = 1
        
    def log_error(self, message: str):
        """记录错误到stderr"""
        print(f"ERROR: {message}", file=sys.stderr)
    
    def send_http_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """向HTTP MCP服务器发送请求"""
        payload = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params or {}
        }
        
        try:
            response = requests.post(
                self.http_server_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response.raise_for_status()
            self.request_id += 1
            return response.json()
        except requests.exceptions.RequestException as e:
            self.log_error(f"HTTP请求失败: {e}")
            return {
                "jsonrpc": "2.0",
                "id": payload["id"],
                "error": {
                    "code": -32603,
                    "message": f"HTTP请求失败: {str(e)}"
                }
            }
    
    def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理来自Cherry Studio的请求"""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")
        
        # 直接转发到HTTP服务器
        response = self.send_http_request(method, params)
        
        if response and "id" in response:
            # 确保响应ID与请求ID匹配
            response["id"] = request_id
        
        return response
    
    def run(self):
        """运行STDIO适配器"""
        self.log_error("MCP STDIO适配器启动")
        self.log_error(f"连接到HTTP服务器: {self.http_server_url}")
        
        try:
            for line in sys.stdin:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    request = json.loads(line)
                    response = self.handle_request(request)
                    
                    # 输出响应到stdout
                    print(json.dumps(response, ensure_ascii=False))
                    sys.stdout.flush()
                    
                except json.JSONDecodeError as e:
                    self.log_error(f"JSON解析错误: {e}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": "Parse error"
                        }
                    }
                    print(json.dumps(error_response))
                    sys.stdout.flush()
                    
                except Exception as e:
                    self.log_error(f"处理请求时出错: {e}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": request.get("id") if 'request' in locals() else None,
                        "error": {
                            "code": -32603,
                            "message": f"Internal error: {str(e)}"
                        }
                    }
                    print(json.dumps(error_response))
                    sys.stdout.flush()
                    
        except KeyboardInterrupt:
            self.log_error("收到中断信号，退出")
        except Exception as e:
            self.log_error(f"适配器运行时出错: {e}")

def main():
    """主函数"""
    # 从命令行参数获取HTTP服务器URL
    http_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8080/mcp/"
    
    adapter = McpStdioAdapter(http_url)
    adapter.run()

if __name__ == "__main__":
    main()
