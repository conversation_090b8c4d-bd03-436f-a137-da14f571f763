#!/usr/bin/env python3
"""
MCP连接测试脚本
用于验证MCP服务器是否正常工作，可以在配置Cherry Studio之前使用
"""

import json
import requests
import sys

class McpTester:
    def __init__(self, base_url="http://localhost:8080/mcp/"):
        self.base_url = base_url
        self.request_id = 1
    
    def send_request(self, method, params=None):
        """发送JSON-RPC 2.0请求"""
        payload = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params or {}
        }
        
        try:
            response = requests.post(
                self.base_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            response.raise_for_status()
            self.request_id += 1
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def test_health(self):
        """测试服务器健康状态"""
        print("🔍 测试服务器健康状态...")
        try:
            response = requests.get(f"{self.base_url.rstrip('/')}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务器健康: {data}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_initialize(self):
        """测试MCP初始化"""
        print("\n🔍 测试MCP初始化...")
        params = {
            "protocolVersion": "2025-03-26",
            "clientInfo": {
                "name": "MCP Tester",
                "version": "1.0.0"
            },
            "capabilities": {}
        }
        
        result = self.send_request("initialize", params)
        if result and "result" in result:
            print("✅ MCP初始化成功")
            print(f"   协议版本: {result['result'].get('protocolVersion')}")
            print(f"   服务器信息: {result['result'].get('serverInfo')}")
            print(f"   服务器能力: {result['result'].get('capabilities')}")
            return True
        else:
            print(f"❌ MCP初始化失败: {result}")
            return False
    
    def test_tools_list(self):
        """测试工具列表"""
        print("\n🔍 测试工具列表...")
        result = self.send_request("tools/list")
        if result and "result" in result:
            tools = result["result"].get("tools", [])
            print(f"✅ 获取到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"   - {tool['name']}: {tool['description']}")
            return tools
        else:
            print(f"❌ 获取工具列表失败: {result}")
            return []
    
    def test_tool_call(self, tool_name, arguments=None):
        """测试工具调用"""
        print(f"\n🔍 测试工具调用: {tool_name}")
        params = {
            "name": tool_name,
            "arguments": arguments or {}
        }
        
        result = self.send_request("tools/call", params)
        if result and "result" in result:
            content = result["result"].get("content", [])
            if content:
                text = content[0].get("text", "")
                print(f"✅ 工具调用成功: {text}")
                return True
            else:
                print(f"✅ 工具调用成功，但无内容返回")
                return True
        else:
            print(f"❌ 工具调用失败: {result}")
            return False
    
    def test_resources_list(self):
        """测试资源列表"""
        print("\n🔍 测试资源列表...")
        result = self.send_request("resources/list")
        if result and "result" in result:
            resources = result["result"].get("resources", [])
            print(f"✅ 获取到 {len(resources)} 个资源:")
            for resource in resources:
                print(f"   - {resource['name']} ({resource['uri']}): {resource['description']}")
            return resources
        else:
            print(f"❌ 获取资源列表失败: {result}")
            return []
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始MCP服务器测试...\n")
        
        # 1. 健康检查
        if not self.test_health():
            print("\n❌ 服务器健康检查失败，请确保MCP服务器正在运行")
            return False
        
        # 2. 初始化测试
        if not self.test_initialize():
            print("\n❌ MCP初始化失败")
            return False
        
        # 3. 工具列表测试
        tools = self.test_tools_list()
        
        # 4. 工具调用测试
        if tools:
            # 测试时间工具
            self.test_tool_call("get_current_time")
            
            # 测试计算工具
            self.test_tool_call("add_numbers", {"a": 23, "b": 45})
            
            # 测试用户信息工具
            self.test_tool_call("get_user_info", {"userId": "12345"})
        
        # 5. 资源列表测试
        self.test_resources_list()
        
        print("\n🎉 所有测试完成！")
        return True

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8080/mcp/"
    
    print(f"MCP服务器地址: {base_url}")
    
    tester = McpTester(base_url)
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ MCP服务器工作正常，可以在Cherry Studio中配置使用")
        print("\n📋 Cherry Studio配置信息:")
        print(f"   URL: {base_url}")
        print("   Transport: HTTP")
        print("   Capabilities: tools, resources")
    else:
        print("\n❌ MCP服务器测试失败，请检查服务器状态")
        sys.exit(1)

if __name__ == "__main__":
    main()
