#!/usr/bin/env python3
"""
测试SSE适配器
"""

import subprocess
import json
import sys

def test_sse_adapter():
    """测试SSE适配器"""
    print("测试SSE适配器...")
    
    # 准备测试请求
    test_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/list",
        "params": {}
    }
    
    try:
        # 启动适配器进程
        process = subprocess.Popen(
            [sys.executable, "simple_sse_adapter.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 发送请求
        request_json = json.dumps(test_request)
        stdout, stderr = process.communicate(input=request_json, timeout=10)
        
        print("STDOUT:")
        print(stdout)
        print("\nSTDERR:")
        print(stderr)
        print(f"\n返回码: {process.returncode}")
        
        if stdout:
            try:
                response = json.loads(stdout.strip())
                print(f"\n解析的响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError as e:
                print(f"\n无法解析响应JSON: {e}")
        
    except subprocess.TimeoutExpired:
        print("测试超时")
        process.kill()
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_sse_adapter()
