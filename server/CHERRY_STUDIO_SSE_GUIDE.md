# Cherry Studio SSE 配置指南

## 🔧 问题诊断

您遇到的错误：
```
Error invoking remote method 'mcp:list-tools': Error: [MCP] Error activating server demo-mcp-sse: SSE error: TypeError: fetch failed: connect ECONNREFUSED ::1:8080, connect ECONNREFUSED 127.0.0.1:8080
```

这表明Cherry Studio无法连接到HTTP服务器。

## ✅ 解决方案

### 1. 确保服务器正在运行

首先启动HTTP服务器：
```bash
cd /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server
./mvnw spring-boot:run
```

### 2. 验证服务器可访问

测试服务器是否正常运行：
```bash
# 测试基本连接
curl http://localhost:8080/mcp/sse

# 测试标准SSE端点
curl http://localhost:8080/mcp/sse-standard

# 测试初始化请求
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":"1","method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"test","version":"1.0"}}}' \
  http://localhost:8080/mcp/sse-standard
```

### 3. Cherry Studio 配置

#### 选项1：简化SSE端点
```json
{
  "mcpServers": {
    "demo-mcp-sse": {
      "url": "http://localhost:8080/mcp/sse",
      "description": "Demo MCP Server - SSE传输"
    }
  }
}
```

#### 选项2：标准SSE端点
```json
{
  "mcpServers": {
    "demo-mcp-sse-standard": {
      "url": "http://localhost:8080/mcp/sse-standard",
      "description": "Demo MCP Server - 标准SSE传输"
    }
  }
}
```

### 4. 可用的端点

| 端点 | 类型 | 描述 |
|------|------|------|
| `/mcp/sse` | SSE | 简化SSE实现，立即发送初始化消息 |
| `/mcp/sse-standard` | SSE | 标准SSE实现，等待客户端请求 |
| `/mcp/http/tools` | HTTP | HTTP API端点 |

### 5. 测试工具

服务器提供10个工具：
1. **get_current_time** - 获取当前时间
2. **add_numbers** - 数字加法
3. **get_user_info** - 用户信息查询
4. **translate_text** - 文本翻译
5. **get_weather** - 天气查询
6. **file_operation** - 文件操作
7. **database_query** - 数据库查询
8. **http_request** - HTTP请求
9. **execute_code** - 代码执行
10. **image_processing** - 图像处理

## 🐛 故障排除

### 如果仍然连接失败：

1. **检查端口占用**：
   ```bash
   lsof -i :8080
   ```

2. **检查防火墙设置**：
   确保8080端口未被防火墙阻止

3. **尝试不同的端口**：
   修改`application.properties`中的端口：
   ```properties
   server.port=8081
   ```

4. **检查Cherry Studio日志**：
   查看Cherry Studio的详细错误信息

5. **使用STDIO模式**（推荐）：
   STDIO模式已经验证可以正常工作：
   ```json
   {
     "mcpServers": {
       "demo-mcp-stdio": {
         "command": "/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/start-mcp-stdio.sh",
         "args": []
       }
     }
   }
   ```

## 📝 当前状态

- ✅ **STDIO模式**：完全正常工作
- ✅ **HTTP API**：完全正常工作  
- ⚠️ **SSE模式**：服务器正常，Cherry Studio连接问题

## 🔄 建议

由于STDIO模式已经完全正常工作，建议：
1. 优先使用STDIO模式
2. 如果需要SSE，请检查Cherry Studio的SSE实现是否支持标准MCP协议
3. 考虑使用HTTP API作为备选方案
