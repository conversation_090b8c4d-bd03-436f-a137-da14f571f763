# MCP客户端使用说明

本文档将指导您如何使用MCP客户端演示程序，体验OpenAI与自定义服务的结合。

## 1. 准备工作

### 1.1 确保服务端运行

首先，确保您的Spring Boot应用程序正在运行，并且API端点可访问：

```bash
# 启动Spring Boot应用
cd /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server
./mvnw spring-boot:run  # 如果您有Maven包装器
# 或者使用IDE直接启动应用
```

服务应该在`http://localhost:8080`上运行。

### 1.2 安装Python依赖

确保您已安装必要的Python库：

```bash
pip install openai requests
```

### 1.3 配置OpenAI API密钥

编辑`mcp_client_demo.py`文件，将您的OpenAI API密钥填入：

```python
# 配置OpenAI API密钥
OPENAI_API_KEY = "您的OpenAI API密钥"  # 将此处替换为您的实际API密钥
```

## 2. 运行MCP客户端

执行以下命令启动MCP客户端：

```bash
python mcp_client_demo.py
```

## 3. 使用体验

启动后，您将看到欢迎消息。您可以开始与助手对话，尝试以下类型的问题：

### 3.1 时间查询

```
现在几点了？
请告诉我当前的时间
```

### 3.2 数字计算

```
计算一下23加45等于多少
87和13的和是多少？
```

### 3.3 用户信息查询

```
查询用户ID为12345的信息
用户u8976的详细资料是什么？
```

### 3.4 文本翻译

```
把"Hello World"翻译成中文
请将"人工智能"翻译为英语
```

### 3.5 天气查询

```
北京今天的天气怎么样？
上海现在天气如何？
```

### 3.6 复杂问题

您还可以尝试提出需要调用多个工具的复杂问题：

```
现在几点了，北京的天气怎么样？
计算一下45加72，并将结果翻译成英文
```

## 4. MCP流程体验

当您提问时，以下是整个MCP流程：

1. 您的问题发送给OpenAI
2. OpenAI分析问题并决定调用哪个工具
3. 系统显示正在调用的工具和参数（这是MCP过程的可视化）
4. 工具执行并返回结果
5. OpenAI接收结果并生成最终回答
6. 系统显示最终回答

这模拟了真实MCP环境中的工作流程，让您能直观地体验MCP的运行机制。

## 5. MCP与直接API调用的区别

通过这个演示，您可以体验到MCP的独特优势：

1. **自动工具选择**：您不需要指定使用哪个API，模型会自动选择
2. **参数提取**：模型能自动从问题中提取所需参数
3. **结果整合**：模型会将API返回的结果自然地整合到回答中
4. **语境理解**：在复杂问题中，模型能理解需要调用多个工具的情况

这与传统方式（需要明确指定API和参数）有根本区别，更符合自然交互方式。

## 6. 退出程序

输入"退出"、"exit"或"quit"可结束对话并退出程序。

## 7. 故障排除

如果遇到问题，请检查：

- 服务端是否正常运行（可通过浏览器访问http://localhost:8080/api/mcp-demo/time测试）
- OpenAI API密钥是否正确配置
- 网络连接是否正常
- Python依赖是否正确安装 