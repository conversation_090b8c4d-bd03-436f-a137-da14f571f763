# MCP (Model Context Protocol) 使用指南

MCP是一个轻量级的模型上下文协议框架，用于在Spring Boot应用中管理上下文数据。它提供了一种简单的方式来存储、检索和管理与模型相关的上下文信息。

## 1. 概述

MCP框架包含以下主要组件：

- **ModelContextProtocol接口**：定义了上下文操作的核心方法
- **InMemoryModelContextProtocol实现**：基于内存的实现
- **McpContextHolder**：提供静态方法便捷访问MCP实例
- **WithMcpContext注解**：用于标记需要MCP上下文支持的方法或类
- **McpContextInterceptor**：处理带有@WithMcpContext注解的请求

## 2. 快速开始

### 2.1 启用MCP

MCP已经通过`McpConfig`类自动配置，您无需额外配置即可使用。该类使用`@ComponentScan("com.example.demo.mcp")`自动扫描并注册所有MCP相关组件。

### 2.2 使用`@WithMcpContext`注解

在控制器方法或类上添加`@WithMcpContext`注解，以启用MCP上下文支持：

```java
@RestController
@RequestMapping("/api/example")
public class ExampleController {

    @WithMcpContext
    @GetMapping("/data")
    public Map<String, Object> getData() {
        // 使用MCP上下文
        String userId = McpContextHolder.get("user_id");
        // ...
    }
}
```

### 2.3 设置和获取上下文数据

使用`McpContextHolder`类的静态方法来设置和获取上下文数据：

```java
// 设置上下文值
McpContextHolder.set("user_id", "12345");

// 获取上下文值
String userId = McpContextHolder.get("user_id");

// 检查上下文是否存在某个键
boolean hasUserId = McpContextHolder.getMcp().hasContext("user_id");

// 移除上下文中的某个键
McpContextHolder.remove("user_id");

// 清空所有上下文
McpContextHolder.clear();
```

### 2.4 获取和设置模型ID

每个MCP上下文都有一个模型ID，用于标识不同的上下文：

```java
// 获取当前模型ID
String modelId = McpContextHolder.getMcp().getModelId();

// 设置当前模型ID
McpContextHolder.getMcp().setModelId("custom-model-id");
```

## 3. 高级用法

### 3.1 自定义`@WithMcpContext`注解参数

`@WithMcpContext`注解提供了两个参数：

- `modelId`：指定上下文使用的模型ID，默认为空（自动生成UUID）
- `clearAfterExecution`：指定方法执行完毕后是否自动清除上下文，默认为true

```java
// 使用自定义模型ID且不自动清除上下文
@WithMcpContext(modelId = "user-profile", clearAfterExecution = false)
@GetMapping("/profile")
public UserProfile getUserProfile() {
    // ...
}
```

### 3.2 在拦截器中添加上下文数据

MCP拦截器会自动将请求的URI和方法放入上下文中，您可以在自己的拦截器中添加更多上下文数据：

```java
@Component
public class CustomInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 检查当前请求是否使用了MCP上下文
        if (McpContextHolder.getMcp().getModelId() != null) {
            // 添加自定义上下文数据
            McpContextHolder.set("client_ip", request.getRemoteAddr());
            McpContextHolder.set("user_agent", request.getHeader("User-Agent"));
        }
        return true;
    }
}
```

## 4. 最佳实践

### 4.1 使用`clearAfterExecution = false`维持会话状态

如果需要在多个请求之间保持上下文数据（如用户会话），可以设置`clearAfterExecution = false`：

```java
@WithMcpContext(clearAfterExecution = false)
@PostMapping("/login")
public LoginResponse login(@RequestBody LoginRequest request) {
    // 验证用户凭证
    User user = userService.authenticate(request.getUsername(), request.getPassword());
    
    // 将用户信息存储在上下文中
    McpContextHolder.set("user_id", user.getId());
    McpContextHolder.set("user_roles", String.join(",", user.getRoles()));
    
    // 返回登录响应
    return new LoginResponse(true, "登录成功");
}
```

### 4.2 使用模型ID区分不同的上下文

可以使用不同的模型ID来区分不同的上下文域：

```java
@WithMcpContext(modelId = "payment")
@PostMapping("/payment")
public PaymentResponse processPayment(@RequestBody PaymentRequest request) {
    // 使用支付上下文
    // ...
}

@WithMcpContext(modelId = "notification")
@PostMapping("/notify")
public NotificationResponse sendNotification(@RequestBody NotificationRequest request) {
    // 使用通知上下文
    // ...
}
```

## 5. 示例API

系统已包含一个示例控制器`McpExampleController`，提供以下端点：

- `POST /api/mcp-example/context`：设置上下文值
- `GET /api/mcp-example/context`：获取上下文值
- `DELETE /api/mcp-example/context`：清除上下文
- `GET /api/mcp-example/no-context`：不使用MCP上下文的方法

您可以使用这些端点来测试和了解MCP的工作原理。

## 6. 扩展MCP

### 6.1 创建自定义MCP实现

您可以创建自己的`ModelContextProtocol`实现，例如基于Redis的实现：

```java
@Component
@Primary
public class RedisModelContextProtocol implements ModelContextProtocol {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    private String modelId;
    
    @Override
    public String getContext(String key) {
        return redisTemplate.opsForHash().get(getRedisKey(), key);
    }
    
    @Override
    public void setContext(String key, String value) {
        redisTemplate.opsForHash().put(getRedisKey(), key, value);
    }
    
    // ... 实现其他方法
    
    private String getRedisKey() {
        return "mcp:context:" + modelId;
    }
}
```

### 6.2 添加过期时间管理

可以扩展`ModelContextProtocol`接口以支持上下文数据的过期时间管理：

```java
public interface ExtendedModelContextProtocol extends ModelContextProtocol {
    void setContextWithTtl(String key, String value, long ttlSeconds);
    void setContextTtl(long ttlSeconds);
}
```

## 7. 故障排除

### 7.1 上下文数据未正确传递

确保：
- 控制器方法或类已使用`@WithMcpContext`注解
- 拦截器已正确注册
- `clearAfterExecution`设置为`false`（如果需要在多个请求之间保持上下文）

### 7.2 上下文数据意外清除

检查：
- 是否有代码显式调用了`McpContextHolder.clear()`
- `@WithMcpContext`注解的`clearAfterExecution`是否设置为`true`

## 8. 结论

MCP框架提供了一种简单而强大的方式来管理Spring应用中的上下文数据。通过使用注解和拦截器，它可以自动处理上下文的生命周期，使开发人员能够专注于业务逻辑。 