#!/usr/bin/env python3
"""
测试Cherry Studio MCP集成的脚本
"""

import requests
import json
import time
import threading
from urllib.parse import urlencode

# 服务器配置
BASE_URL = "http://localhost:8080"
MCP_SSE_URL = f"{BASE_URL}/mcp/sse"

def test_cherry_studio_sse_connection():
    """测试Cherry Studio SSE连接"""
    print("=== 测试Cherry Studio SSE连接 ===")
    
    # 构建带参数的URL
    params = {
        'uri': '/mcp/sse',
        'name': 'Demo MCP Server'
    }
    url = f"{MCP_SSE_URL}?{urlencode(params)}"
    
    try:
        response = requests.get(url, headers={'Accept': 'text/event-stream'}, stream=True, timeout=5)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("SSE连接成功建立")
            
            # 读取前几个事件
            lines_read = 0
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"接收到: {line}")
                    lines_read += 1
                    if lines_read >= 6:  # 读取前6行就停止
                        break
        else:
            print(f"连接失败: {response.text}")
            
    except requests.exceptions.Timeout:
        print("连接超时（这是正常的，因为SSE是持久连接）")
    except Exception as e:
        print(f"连接错误: {e}")

def test_cherry_studio_post_request():
    """测试Cherry Studio POST请求"""
    print("\n=== 测试Cherry Studio POST请求 ===")
    
    # 构建带参数的URL
    params = {
        'uri': '/mcp/sse',
        'name': 'Demo MCP Server'
    }
    url = f"{MCP_SSE_URL}?{urlencode(params)}"
    
    # 测试tools/list
    request_data = {
        "jsonrpc": "2.0",
        "id": "test-1",
        "method": "tools/list",
        "params": {}
    }
    
    try:
        response = requests.post(url, 
                               headers={'Content-Type': 'application/json'},
                               json=request_data,
                               timeout=10)
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("tools/list 响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查工具数量
            if 'result' in result and 'tools' in result['result']:
                tool_count = len(result['result']['tools'])
                print(f"发现 {tool_count} 个工具")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求错误: {e}")

def test_cherry_studio_tool_call():
    """测试Cherry Studio工具调用"""
    print("\n=== 测试Cherry Studio工具调用 ===")
    
    # 构建带参数的URL
    params = {
        'uri': '/mcp/sse',
        'name': 'Demo MCP Server'
    }
    url = f"{MCP_SSE_URL}?{urlencode(params)}"
    
    # 测试get_current_time工具
    request_data = {
        "jsonrpc": "2.0",
        "id": "test-2",
        "method": "tools/call",
        "params": {
            "name": "get_current_time",
            "arguments": {}
        }
    }
    
    try:
        response = requests.post(url,
                               headers={'Content-Type': 'application/json'},
                               json=request_data,
                               timeout=10)
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("get_current_time 响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求错误: {e}")

def test_server_health():
    """测试服务器健康状态"""
    print("\n=== 测试服务器健康状态 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/mcp/health", timeout=5)
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print("健康状态:")
            print(json.dumps(health_data, indent=2, ensure_ascii=False))
        else:
            print(f"健康检查失败: {response.text}")
            
    except Exception as e:
        print(f"健康检查错误: {e}")

def main():
    """主测试函数"""
    print("Cherry Studio MCP 集成测试")
    print("=" * 50)
    
    # 测试服务器健康状态
    test_server_health()
    
    # 测试Cherry Studio SSE连接
    test_cherry_studio_sse_connection()
    
    # 测试Cherry Studio POST请求
    test_cherry_studio_post_request()
    
    # 测试Cherry Studio工具调用
    test_cherry_studio_tool_call()
    
    print("\n" + "=" * 50)
    print("测试完成")
    
    print("\n配置信息:")
    print(f"Cherry Studio MCP URL: {MCP_SSE_URL}")
    print("参数:")
    print("  - uri: /mcp/sse")
    print("  - name: Demo MCP Server")

if __name__ == "__main__":
    main()
