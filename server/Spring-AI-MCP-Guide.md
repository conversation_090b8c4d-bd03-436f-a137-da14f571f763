# Spring服务API使用指南

本文档将指导您如何使用我们的Spring REST API服务，这些服务可以作为大模型的外部工具使用。

## 1. 概述

我们的服务提供了以下功能：
- 获取系统时间
- 计算两个数的和
- 获取用户信息
- 文本翻译
- 获取天气信息

这些API可以被大模型调用来获取实时信息，从而增强AI的能力。

## 2. 技术架构

我们的服务基于以下技术栈：
- Spring Boot 3.2
- Java 17

系统的主要组件包括：
- `DemoMcpService`: 提供各种工具函数
- `McpDemoController`: 提供REST API来使用这些服务

## 3. 快速开始

### 3.1 启动服务

1. 确保您已安装Java 17
2. 使用IDE或命令行启动应用程序
3. 服务将在默认的8080端口启动

### 3.2 API端点

您可以通过以下REST API使用我们的服务：

| 功能       | API路径                                   | 请求方法 | 参数                     |
|------------|------------------------------------------|----------|--------------------------|
| 获取当前时间 | `/api/mcp-demo/time`                     | GET      | 无                       |
| 计算两数之和 | `/api/mcp-demo/add?a=10&b=20`            | GET      | a: 第一个数, b: 第二个数  |
| 获取用户信息 | `/api/mcp-demo/user/{userId}`            | GET      | userId: 用户ID           |
| 翻译文本    | `/api/mcp-demo/translate?text=hello&targetLanguage=中文` | GET | text: 文本, targetLanguage: 目标语言 |
| 获取天气信息 | `/api/mcp-demo/weather?city=北京`        | GET      | city: 城市名称           |

## 4. 与大模型集成

### 4.1 基本集成流程

1. 配置大模型使用外部API
2. 用户向大模型提出需要实时信息的问题
3. 大模型识别需要调用API的情况
4. 大模型发送HTTP请求到我们的服务
5. 我们的服务执行相应的功能并返回结果
6. 大模型接收结果并生成回复

### 4.2 集成示例

以下是Python使用我们的API的示例代码：

```python
import requests
import json

# 获取当前时间
response = requests.get("http://localhost:8080/api/mcp-demo/time")
print(response.json())

# 计算两数之和
response = requests.get("http://localhost:8080/api/mcp-demo/add?a=10&b=20")
print(response.json())

# 获取用户信息
response = requests.get("http://localhost:8080/api/mcp-demo/user/12345")
print(response.json())

# 翻译文本
response = requests.get("http://localhost:8080/api/mcp-demo/translate?text=hello&targetLanguage=中文")
print(response.json())

# 获取天气信息
response = requests.get("http://localhost:8080/api/mcp-demo/weather?city=北京")
print(response.json())
```

## 5. 添加自定义API

您可以通过以下步骤添加自己的API：

### 5.1 创建服务方法

在`DemoMcpService`或新的服务类中，添加新的方法：

```java
@Service
public class CustomService {

    public Map<String, Object> getStockPrice(String symbol) {
        // 实现获取股票价格的逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("symbol", symbol);
        result.put("price", 123.45);
        result.put("time", LocalDateTime.now().toString());
        return result;
    }
}
```

### 5.2 创建控制器方法

在控制器中添加新的端点：

```java
@RestController
@RequestMapping("/api/custom")
public class CustomController {

    @Autowired
    private CustomService customService;

    @GetMapping("/stock/{symbol}")
    public Map<String, Object> getStockPrice(@PathVariable String symbol) {
        Map<String, Object> result = new HashMap<>();
        result.put("result", customService.getStockPrice(symbol));
        return result;
    }
}
```

### 5.3 重启服务

重启应用程序以使新API生效。

## 6. 高级配置

### 6.1 调整服务配置

您可以在`application.properties`中修改配置：

```properties
# 服务器端口
server.port=8088

# 日志级别
logging.level.com.example.demo=DEBUG
```

### 6.2 安全配置

在生产环境中，您应该添加安全配置，例如：

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/api/mcp-demo/**").permitAll()
                .anyRequest().authenticated()
            )
            .csrf(csrf -> csrf.disable())
            .httpBasic(Customizer.withDefaults());
        return http.build();
    }
}
```

## 7. 故障排除

### 7.1 连接问题

如果无法连接到API，请检查：
- 服务是否正常运行
- 网络连接是否畅通
- 端口是否正确配置
- 防火墙或安全策略是否阻止了连接

### 7.2 API调用失败

如果API调用失败，请检查：
- 日志中的错误信息
- 服务方法的实现是否有问题
- 参数类型是否匹配
- 是否有未捕获的异常

## 8. 参考资源

- [Spring Boot 文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [Spring Web MVC 文档](https://docs.spring.io/spring-framework/docs/current/reference/html/web.html) 