# MCP聊天流程说明

本文档介绍了基于Spring Boot的MCP（Model Context Protocol）聊天实现流程。

## 1. MCP架构概述

### 1.1 什么是MCP？

MCP（Model Context Protocol）是一种专为大语言模型设计的协议，它允许模型自动发现和调用工具，而不需要硬编码工具调用逻辑。MCP的核心优势在于：

- **自动工具选择**：模型可以根据用户问题自动选择合适的工具
- **参数提取**：从用户问题中自动提取所需参数
- **上下文共享**：在工具调用前后保持上下文连续性
- **结果自然整合**：将工具返回的结果自然地整合到回答中

### 1.2 我们的MCP实现

我们实现了一个轻量级的MCP框架，包括：

1. **上下文管理**：`McpContextHolder`提供上下文存储和访问
2. **注解支持**：`@WithMcpContext`标记需要MCP上下文的方法
3. **拦截器**：`McpContextInterceptor`处理请求前后的上下文设置和清理
4. **工具服务**：`DemoMcpService`提供各种工具功能
5. **Web界面**：基于HTML和JavaScript的聊天界面

## 2. 聊天流程详解

### 2.1 用户发送消息

1. 用户在Web界面输入问题并发送
2. 前端JavaScript将消息发送到`/api/mcp-chat`端点
3. `McpChatController`接收请求并调用`McpChatService`处理

### 2.2 大模型分析和工具选择

1. `McpChatService`将用户消息添加到对话历史
2. 调用大模型API（DeepSeek-R1），发送消息历史和工具定义
3. 大模型分析问题并决定是否调用工具
   - 如果不需要工具，直接返回回答
   - 如果需要工具，返回工具调用信息

### 2.3 工具调用和参数处理

1. 服务端接收到工具调用请求
2. 处理可能的参数格式问题（如`nullnull`）
3. 根据工具名称调用相应的服务方法：
   - `get_current_time`：获取当前时间
   - `add_numbers`：计算两数之和
   - `get_user_info`：获取用户信息
   - `translate_text`：翻译文本
   - `get_weather`：获取天气信息

### 2.4 MCP上下文处理

1. 工具方法通过`@WithMcpContext`注解标记
2. 调用前，`McpContextInterceptor`创建上下文并设置请求信息
3. 方法执行中可以访问和修改上下文
4. 调用后，根据`shared`参数决定是否保留上下文

### 2.5 结果整合和回复

1. 工具执行结果添加到消息历史
2. 再次调用大模型API，让模型整合工具结果
3. 大模型生成最终回复
4. 返回给前端，包括工具调用信息和最终回复

### 2.6 前端展示

1. 前端接收响应并解析
2. 显示工具调用过程（工具名称和参数）
3. 显示工具执行结果
4. 显示最终的助手回复

## 3. 错误处理和兼容性

### 3.1 参数格式错误处理

我们特别处理了DeepSeek-R1模型可能返回的格式问题：

```java
private String fixArgumentsFormat(String arguments) {
    // 处理nullnull等特殊情况
    if ("nullnull".equals(arguments) || "null".equals(arguments)) {
        return "{}";
    }
    
    // 尝试解析为JSON
    try {
        objectMapper.readTree(arguments);
        return arguments; // 如果可以解析，则返回原始字符串
    } catch (Exception e) {
        logger.warn("参数格式错误，尝试修复: {}", arguments);
        // 如果无法解析，返回空对象
        return "{}";
    }
}
```

### 3.2 异常捕获和显示

- 服务端捕获所有异常并返回友好错误消息
- 前端显示错误信息，便于调试和问题排查

## 4. 与标准HTTP API的区别

MCP与标准HTTP API的主要区别：

1. **意图理解**：MCP能理解用户意图并自动选择工具，而标准API需要明确指定
2. **参数提取**：MCP自动从自然语言中提取参数，标准API需要明确提供参数
3. **上下文保持**：MCP在多次交互中保持上下文，标准API通常是无状态的
4. **结果整合**：MCP将工具结果自然整合到回答中，标准API只返回原始数据
5. **交互方式**：MCP提供自然语言交互界面，标准API通常需要专业客户端

## 5. 使用方法

### 5.1 启动服务

```bash
./mvnw spring-boot:run
```

### 5.2 访问聊天界面

打开浏览器访问：`http://localhost:8080/mcp-chat`

### 5.3 示例问题

- "现在几点了？"
- "计算一下23加45等于多少"
- "查询用户ID为12345的信息"
- "把'Hello World'翻译成中文"
- "北京今天的天气怎么样？"
- "现在几点了，北京的天气怎么样？"（复合问题）

## 6. 扩展和定制

要添加新的工具功能：

1. 在`DemoMcpService`中添加新方法并标记`@WithMcpContext`
2. 在`McpChatService`的`executeToolCall`方法中添加新工具的处理
3. 在`callModelApi`方法中添加新工具的定义
4. 在`DemoMcpController`中添加对应的API端点

## 7. 结论

通过这个MCP聊天实现，我们可以体验到MCP相比传统API调用的优势。它提供了更自然、更智能的交互方式，让用户能够通过自然语言获取信息和服务，而不需要了解具体的API结构和参数。 