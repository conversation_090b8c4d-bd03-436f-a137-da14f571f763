#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenAI + MCP服务调用示例
这个脚本演示如何使用OpenAI的函数调用功能来调用我们的API服务
"""

import os
import json
import requests
from openai import OpenAI

# 配置OpenAI API密钥
# 请替换为您自己的API密钥
OPENAI_API_KEY = "sk-ngjujmixtgbvndhdouduvobphamphllqxpgbzinqqqmxngit"
# 设置自定义的 API 地址
OPENAI_API_BASE = "https://api.siliconflow.cn"
# 服务API基础URL
API_BASE_URL = "http://localhost:8080/api/mcp-demo"

# 初始化OpenAI客户端
client = OpenAI(api_key=OPENAI_API_KEY,base_url=OPENAI_API_BASE)

# 定义可用工具
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_current_time",
            "description": "获取当前的系统时间，返回格式化的时间字符串",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "add_numbers",
            "description": "计算两个数的和，返回计算结果",
            "parameters": {
                "type": "object",
                "properties": {
                    "a": {
                        "type": "integer",
                        "description": "第一个数"
                    },
                    "b": {
                        "type": "integer",
                        "description": "第二个数"
                    }
                },
                "required": ["a", "b"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_user_info",
            "description": "根据用户ID获取用户信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "userId": {
                        "type": "string",
                        "description": "用户ID"
                    }
                },
                "required": ["userId"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "translate_text",
            "description": "将文本翻译成指定的语言",
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "要翻译的文本"
                    },
                    "targetLanguage": {
                        "type": "string",
                        "description": "目标语言，如：中文、英文、日文等"
                    }
                },
                "required": ["text", "targetLanguage"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "获取指定城市的天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        }
    }
]

# 定义工具调用函数
def call_api(tool_name, params):
    """调用对应的API并返回结果"""
    if tool_name == "get_current_time":
        response = requests.get(f"{API_BASE_URL}/time")
        return response.json()["result"]
    
    elif tool_name == "add_numbers":
        response = requests.get(f"{API_BASE_URL}/add", params={
            "a": params["a"],
            "b": params["b"]
        })
        return response.json()["result"]
    
    elif tool_name == "get_user_info":
        response = requests.get(f"{API_BASE_URL}/user/{params['userId']}")
        return response.json()["result"]
    
    elif tool_name == "translate_text":
        response = requests.get(f"{API_BASE_URL}/translate", params={
            "text": params["text"],
            "targetLanguage": params["targetLanguage"]
        })
        return response.json()["result"]
    
    elif tool_name == "get_weather":
        response = requests.get(f"{API_BASE_URL}/weather", params={
            "city": params["city"]
        })
        return response.json()["result"]
    
    else:
        return f"未知工具: {tool_name}"

def chat_with_mcp():
    """与OpenAI聊天并使用MCP服务"""
    # 保存聊天历史
    messages = []
    
    # 添加系统消息
    messages.append({
        "role": "system", 
        "content": "我是一个智能助手，可以帮您获取实时信息。我可以查询当前时间、计算数字、获取用户信息、翻译文本和查询天气信息。"
    })
    
    print("欢迎使用MCP客户端演示！您可以询问关于时间、计算、用户信息、翻译和天气的问题。输入'退出'结束对话。\n")
    
    while True:
        # 获取用户输入
        user_input = input("您: ")
        
        # 检查是否退出
        if user_input.lower() in ['退出', 'exit', 'quit']:
            print("谢谢使用，再见！")
            break
        
        # 添加用户消息
        messages.append({"role": "user", "content": user_input})
        
        # 调用OpenAI API
        response = client.chat.completions.create(
            model="deepseek-ai/DeepSeek-R1",  # 或者使用更高级的模型如 gpt-4
            messages=messages,
            tools=tools,
            tool_choice="auto"  # 自动选择合适的工具
        )
        
        # 获取助手回复
        assistant_message = response.choices[0].message
        
        # 处理工具调用
        tool_calls = assistant_message.tool_calls
        if tool_calls:
            # 将助手的消息添加到历史记录
            messages.append(assistant_message)
            
            # 处理每个工具调用
            for tool_call in tool_calls:
                function_name = tool_call.function.name
                function_args = json.loads(tool_call.function.arguments)
                
                print(f"\n[系统] 正在调用工具: {function_name}, 参数: {function_args}")
                
                # 调用API并获取结果
                function_response = call_api(function_name, function_args)
                
                # 将工具调用结果添加到消息历史
                messages.append({
                    "tool_call_id": tool_call.id,
                    "role": "tool",
                    "name": function_name,
                    "content": str(function_response)
                })
            
            # 获取AI对工具调用结果的处理
            second_response = client.chat.completions.create(
                model="gpt-3.5-turbo",  # 或者使用更高级的模型如 gpt-4
                messages=messages
            )
            
            # 输出最终回复
            final_response = second_response.choices[0].message.content
            print(f"助手: {final_response}\n")
            
            # 添加助手回复到历史
            messages.append({"role": "assistant", "content": final_response})
        else:
            # 无工具调用，直接输出回复
            assistant_response = assistant_message.content
            print(f"助手: {assistant_response}\n")
            
            # 添加助手回复到历史
            messages.append({"role": "assistant", "content": assistant_response})

if __name__ == "__main__":
    try:
        chat_with_mcp()
    except Exception as e:
        print(f"发生错误: {e}") 