# Cherry Studio MCP配置指南

本指南介绍如何在Cherry Studio中配置和使用我们的标准MCP服务器。

## 1. 前置条件

### 1.1 确保MCP服务器运行
```bash
# 启动我们的Spring Boot MCP服务器
cd /path/to/your/project
./mvnw spring-boot:run
```

### 1.2 验证服务器状态
```bash
# 检查健康状态
curl http://localhost:8080/mcp/health

# 检查服务器信息
curl http://localhost:8080/mcp/info
```

## 2. Cherry Studio配置方法

### 方法一：通过Cherry Studio界面配置

1. **打开Cherry Studio**
2. **进入设置页面**
   - 点击右上角的设置图标
   - 或使用快捷键 `Cmd/Ctrl + ,`

3. **找到MCP配置部分**
   - 在设置页面中找到"MCP Servers"或"模型上下文协议"部分

4. **添加新的MCP服务器**
   - 点击"添加服务器"或"+"按钮
   - 填写以下信息：

```json
{
  "name": "Demo MCP Server",
  "command": "python3",
  "args": ["/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/mcp_stdio_adapter.py"],
  "description": "演示MCP服务器 - 提供时间、计算、翻译等功能"
}
```

**注意**：请将路径 `/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/mcp_stdio_adapter.py` 替换为您实际的项目路径。

### 方法二：通过配置文件

1. **找到Cherry Studio配置目录**
   - macOS: `~/Library/Application Support/Cherry Studio/`
   - Windows: `%APPDATA%/Cherry Studio/`
   - Linux: `~/.config/Cherry Studio/`

2. **编辑或创建MCP配置文件**
   - 文件名通常是 `mcp-config.json` 或 `settings.json`

3. **添加MCP服务器配置**
```json
{
  "mcpServers": {
    "demo-mcp-server": {
      "transport": {
        "type": "http",
        "url": "http://localhost:8080/mcp/"
      },
      "capabilities": {
        "resources": true,
        "tools": true,
        "prompts": true
      },
      "description": "Demo MCP Server - 提供时间、计算、用户信息、翻译和天气查询功能",
      "enabled": true
    }
  }
}
```

## 3. 配置参数说明

### 3.1 基本配置
- **name**: 服务器显示名称
- **transport.type**: 传输类型，使用 "http"
- **transport.url**: MCP服务器的HTTP端点
- **description**: 服务器描述

### 3.2 能力配置
- **capabilities.resources**: 是否支持资源功能
- **capabilities.tools**: 是否支持工具调用
- **capabilities.prompts**: 是否支持提示模板

### 3.3 可选配置
- **enabled**: 是否启用此服务器（默认true）
- **timeout**: 请求超时时间（毫秒）
- **retries**: 重试次数

## 4. 验证配置

### 4.1 在Cherry Studio中验证
1. 重启Cherry Studio
2. 在聊天界面中，应该能看到MCP服务器的工具
3. 尝试使用工具，例如：
   - "现在几点了？"
   - "计算23+45"
   - "北京的天气怎么样？"

### 4.2 检查连接状态
- 在Cherry Studio的MCP设置页面中查看连接状态
- 绿色表示连接正常，红色表示连接失败

## 5. 可用工具说明

我们的MCP服务器提供以下工具：

### 5.1 时间工具
- **工具名**: `get_current_time`
- **功能**: 获取当前系统时间
- **示例**: "现在几点了？"

### 5.2 计算工具
- **工具名**: `add_numbers`
- **功能**: 计算两个数字的和
- **参数**: a (整数), b (整数)
- **示例**: "计算23加45"

### 5.3 用户信息工具
- **工具名**: `get_user_info`
- **功能**: 获取用户信息
- **参数**: userId (字符串)
- **示例**: "查询用户12345的信息"

### 5.4 翻译工具
- **工具名**: `translate_text`
- **功能**: 翻译文本
- **参数**: text (字符串), targetLanguage (字符串)
- **示例**: "把'Hello World'翻译成中文"

### 5.5 天气工具
- **工具名**: `get_weather`
- **功能**: 获取天气信息
- **参数**: city (字符串)
- **示例**: "北京今天的天气怎么样？"

## 6. 故障排除

### 6.1 连接失败
- 检查MCP服务器是否正在运行
- 验证URL是否正确（http://localhost:8080/mcp/）
- 检查防火墙设置

### 6.2 工具调用失败
- 查看Cherry Studio的控制台日志
- 检查MCP服务器的日志输出
- 验证工具参数是否正确

### 6.3 常见错误
- **404错误**: URL路径错误，检查端点地址
- **500错误**: 服务器内部错误，查看服务器日志
- **超时错误**: 网络问题或服务器响应慢

## 7. 高级配置

### 7.1 自定义端口
如果需要使用不同端口，修改application.properties：
```properties
server.port=8081
```
然后更新Cherry Studio配置中的URL。

### 7.2 HTTPS配置
对于生产环境，建议启用HTTPS：
```json
{
  "transport": {
    "type": "http",
    "url": "https://your-domain.com/mcp/"
  }
}
```

### 7.3 认证配置
如果需要认证，可以添加认证头：
```json
{
  "transport": {
    "type": "http",
    "url": "http://localhost:8080/mcp/",
    "headers": {
      "Authorization": "Bearer your-token"
    }
  }
}
```

## 8. 测试示例

配置完成后，您可以在Cherry Studio中尝试以下对话：

1. **时间查询**: "现在几点了？"
2. **数学计算**: "帮我计算一下23加45等于多少"
3. **用户查询**: "查询用户ID为12345的信息"
4. **文本翻译**: "把'Hello World'翻译成中文"
5. **天气查询**: "北京今天的天气怎么样？"
6. **复合查询**: "现在几点了，顺便告诉我北京的天气"

## 9. 注意事项

1. **端口冲突**: 确保8080端口没有被其他应用占用
2. **网络访问**: Cherry Studio需要能够访问localhost:8080
3. **版本兼容**: 确保Cherry Studio版本支持MCP协议
4. **配置重载**: 修改配置后需要重启Cherry Studio

## 10. 支持和帮助

如果遇到问题，可以：
1. 查看Cherry Studio的官方文档
2. 检查我们MCP服务器的日志输出
3. 使用curl命令直接测试MCP端点
4. 参考本项目的MCP实现代码
