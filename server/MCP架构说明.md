# MCP架构说明

## 1. 项目架构概述

本项目实现了一个基于Spring Boot的MCP（Model Context Protocol）框架，用于与大语言模型进行交互和工具调用。整个架构分为以下几个部分：

### 1.1 核心组件

- **上下文管理**：`McpContextHolder` - 管理请求上下文
- **注解支持**：`@WithMcpContext` - 标记需要MCP上下文的方法
- **拦截器**：`McpContextInterceptor` - 处理请求前后的上下文设置和清理
- **配置类**：`McpConfig` - 配置MCP相关组件
- **Web配置**：`WebMvcConfig` - 配置静态资源和视图解析

### 1.2 服务组件

- **聊天服务**：`McpChatService` - 处理与大模型的交互和工具调用
- **工具服务**：`DemoMcpService` - 提供各种工具功能实现
- **示例服务**：`McpExampleController` - 演示如何使用MCP上下文

### 1.3 控制器组件

- **API控制器**：`McpChatController` - 提供聊天API端点
- **页面控制器**：`StaticPageController` - 处理页面请求
- **示例控制器**：`DemoMcpController` - 提供MCP API示例

### 1.4 前端组件

- **聊天界面**：`mcp-chat.html` - 提供用户交互界面
- **重定向页**：`index.html` - 重定向到聊天页面

## 2. 文件结构

```
src/main/java/com/example/demo/mcp/
├── annotation/
│   └── WithMcpContext.java        # MCP上下文注解
├── config/
│   ├── McpConfig.java             # MCP配置类
│   └── WebMvcConfig.java          # Web MVC配置类
├── context/
│   └── McpContextHolder.java      # MCP上下文持有者
├── controller/
│   ├── DemoMcpController.java     # MCP示例控制器
│   ├── McpChatController.java     # MCP聊天控制器
│   └── StaticPageController.java  # 静态页面控制器
├── example/
│   └── McpExampleController.java  # MCP示例控制器
├── interceptor/
│   └── McpContextInterceptor.java # MCP上下文拦截器
└── service/
    ├── DemoMcpService.java        # MCP示例服务
    └── McpChatService.java        # MCP聊天服务

src/main/resources/
├── static/
│   ├── index.html                 # 首页（重定向）
│   ├── mcp-chat.html              # MCP聊天页面
│   └── test.html                  # 测试页面
└── application.properties         # 应用配置文件
```

## 3. 工作流程

### 3.1 MCP上下文管理流程

1. 客户端发起请求
2. `McpContextInterceptor` 拦截请求，创建上下文
3. 带有 `@WithMcpContext` 注解的方法可以访问上下文
4. 方法执行完毕后，根据 `shared` 参数决定是否保留上下文
5. 请求结束，清理上下文

### 3.2 聊天交互流程

1. 用户在前端输入问题
2. 前端通过AJAX调用 `/api/mcp-chat` 接口
3. `McpChatController` 接收请求并调用 `McpChatService`
4. `McpChatService` 调用大模型API
5. 大模型决定是否调用工具
6. 如果需要调用工具，`McpChatService` 执行相应的工具方法
7. 工具执行结果返回给大模型
8. 大模型生成最终回复
9. 响应返回给前端
10. 前端展示结果

### 3.3 工具调用流程

1. 大模型分析用户问题，决定调用工具
2. `McpChatService` 解析工具调用请求
3. 根据工具名称，调用 `DemoMcpService` 中的相应方法
4. 方法执行，并在MCP上下文中记录信息
5. 执行结果返回给大模型
6. 大模型整合结果，生成自然语言回复

## 4. 主要功能

### 4.1 可用工具

- **get_current_time**: 获取当前系统时间
- **add_numbers**: 计算两个数的和
- **get_user_info**: 根据用户ID获取用户信息
- **translate_text**: 将文本翻译成指定语言
- **get_weather**: 获取指定城市的天气信息

### 4.2 页面访问

- **首页**: `http://localhost:8080/` - 重定向到聊天页面
- **聊天页面**: `http://localhost:8080/mcp-chat` - MCP聊天界面
- **测试页面**: `http://localhost:8080/test.html` - 静态资源测试页面

### 4.3 API端点

- **聊天API**: `POST /api/mcp-chat` - 处理聊天请求
- **时间API**: `GET /api/mcp-demo/time` - 获取当前时间
- **计算API**: `GET /api/mcp-demo/add` - 计算两数之和
- **用户API**: `GET /api/mcp-demo/user/{userId}` - 获取用户信息
- **翻译API**: `GET /api/mcp-demo/translate` - 翻译文本
- **天气API**: `GET /api/mcp-demo/weather` - 获取天气信息

## 5. 部署和访问

1. 启动应用：`./mvnw spring-boot:run`
2. 访问聊天页面：`http://localhost:8080/mcp-chat`
3. 输入问题，如"现在几点了？"、"计算23+45"等
4. 系统会调用相应的工具，并返回结果

## 6. 扩展方式

要添加新的工具功能：

1. 在 `DemoMcpService` 中添加新方法，并标记 `@WithMcpContext`
2. 在 `McpChatService` 的 `executeToolCall` 方法中添加新工具的处理
3. 在 `callModelApi` 方法中添加新工具的定义
4. 在 `DemoMcpController` 中添加对应的API端点（可选） 