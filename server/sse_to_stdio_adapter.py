#!/usr/bin/env python3
"""
SSE到STDIO适配器
将Cherry Studio的STDIO请求转换为SSE连接
"""

import json
import sys
import requests
import time
import threading
import uuid
from typing import Dict, Any, Optional

class SseToStdioAdapter:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.connection_id = str(uuid.uuid4())
        self.sse_session = None
        self.connected = False
        
    def log_error(self, message: str):
        """记录错误到stderr"""
        print(f"[SSE-ADAPTER] {message}", file=sys.stderr, flush=True)
    
    def connect_sse(self):
        """建立SSE连接"""
        try:
            import sseclient  # 需要安装: pip install sseclient-py
            
            sse_url = f"{self.base_url}/mcp/sse/connect?clientId={self.connection_id}"
            self.log_error(f"连接SSE: {sse_url}")
            
            response = requests.get(sse_url, stream=True, timeout=30)
            response.raise_for_status()
            
            self.sse_session = sseclient.SSEClient(response)
            self.connected = True
            self.log_error("SSE连接建立成功")
            
            # 启动SSE监听线程
            threading.Thread(target=self.listen_sse_events, daemon=True).start()
            
        except Exception as e:
            self.log_error(f"SSE连接失败: {e}")
            self.connected = False
    
    def listen_sse_events(self):
        """监听SSE事件"""
        try:
            for event in self.sse_session:
                if event.data:
                    try:
                        data = json.loads(event.data)
                        if event.event == "response":
                            # 收到MCP响应，输出到stdout
                            response_data = data.get("response", data)
                            print(json.dumps(response_data, ensure_ascii=False))
                            sys.stdout.flush()
                        elif event.event == "connection":
                            self.log_error(f"连接确认: {data.get('message', '')}")
                        else:
                            self.log_error(f"收到SSE事件: {event.event} - {data}")
                    except json.JSONDecodeError:
                        self.log_error(f"无法解析SSE数据: {event.data}")
        except Exception as e:
            self.log_error(f"SSE监听错误: {e}")
            self.connected = False
    
    def send_mcp_request(self, request: Dict[str, Any]) -> bool:
        """通过SSE发送MCP请求"""
        try:
            request_url = f"{self.base_url}/mcp/sse/request"
            params = {"connectionId": self.connection_id}
            
            response = requests.post(
                request_url,
                json=request,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            return result.get("status") == "sent"
            
        except Exception as e:
            self.log_error(f"发送MCP请求失败: {e}")
            return False
    
    def run(self):
        """运行适配器"""
        self.log_error("启动SSE到STDIO适配器")
        
        # 建立SSE连接
        self.connect_sse()
        
        if not self.connected:
            self.log_error("无法建立SSE连接，退出")
            sys.exit(1)
        
        # 等待SSE连接稳定
        time.sleep(1)
        
        try:
            # 处理STDIO输入
            for line in sys.stdin:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    request = json.loads(line)
                    self.log_error(f"收到STDIO请求: {request.get('method', 'unknown')}")
                    
                    # 检查是否为通知
                    if "id" not in request:
                        self.log_error(f"收到通知: {request.get('method')}")
                        continue
                    
                    # 发送到SSE
                    if self.connected:
                        success = self.send_mcp_request(request)
                        if not success:
                            # 如果SSE发送失败，返回错误响应
                            error_response = {
                                "jsonrpc": "2.0",
                                "id": request.get("id"),
                                "error": {
                                    "code": -32603,
                                    "message": "SSE request failed"
                                }
                            }
                            print(json.dumps(error_response))
                            sys.stdout.flush()
                    else:
                        # SSE未连接，返回错误
                        error_response = {
                            "jsonrpc": "2.0",
                            "id": request.get("id"),
                            "error": {
                                "code": -32002,
                                "message": "SSE not connected"
                            }
                        }
                        print(json.dumps(error_response))
                        sys.stdout.flush()
                        
                except json.JSONDecodeError as e:
                    self.log_error(f"JSON解析错误: {e}")
                    
                except Exception as e:
                    self.log_error(f"处理请求时出错: {e}")
                    
        except KeyboardInterrupt:
            self.log_error("收到中断信号，退出")
        except Exception as e:
            self.log_error(f"适配器运行时出错: {e}")

def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8080"
    
    adapter = SseToStdioAdapter(base_url)
    adapter.run()

if __name__ == "__main__":
    main()
