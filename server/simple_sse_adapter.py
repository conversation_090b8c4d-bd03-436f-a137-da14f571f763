#!/usr/bin/env python3
"""
简化的SSE适配器
不依赖外部库，直接使用HTTP请求模拟SSE功能
"""

import json
import sys
import requests
import time
import uuid
from typing import Dict, Any

class SimpleSSEAdapter:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.connection_id = str(uuid.uuid4())
        
    def log_error(self, message: str):
        """记录错误到stderr"""
        print(f"[SSE-ADAPTER] {message}", file=sys.stderr, flush=True)
    
    def send_mcp_request_direct(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """直接发送MCP请求到HTTP端点"""
        try:
            response = requests.post(
                f"{self.base_url}/mcp/",
                json=request,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            self.log_error(f"HTTP请求失败: {e}")
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"HTTP request failed: {str(e)}"
                }
            }
    
    def run(self):
        """运行适配器"""
        self.log_error("启动简化SSE适配器（使用HTTP后端）")

        try:
            # 添加缓冲区刷新
            sys.stderr.flush()

            for line in sys.stdin:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    request = json.loads(line)
                    self.log_error(f"收到请求: {request.get('method', 'unknown')}")
                    
                    # 检查是否为通知
                    if "id" not in request:
                        self.log_error(f"收到通知: {request.get('method')}")
                        continue
                    
                    # 直接发送到HTTP端点
                    response = self.send_mcp_request_direct(request)
                    
                    # 输出响应
                    print(json.dumps(response, ensure_ascii=False))
                    sys.stdout.flush()
                    
                    self.log_error(f"发送响应: {response.get('result', {}).get('tools', 'N/A') if 'result' in response else 'error'}")
                    
                except json.JSONDecodeError as e:
                    self.log_error(f"JSON解析错误: {e}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": "Parse error"
                        }
                    }
                    print(json.dumps(error_response))
                    sys.stdout.flush()
                    
                except Exception as e:
                    self.log_error(f"处理请求时出错: {e}")
                    
        except KeyboardInterrupt:
            self.log_error("收到中断信号，退出")
        except Exception as e:
            self.log_error(f"适配器运行时出错: {e}")

def main():
    """主函数"""
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8080"
    
    adapter = SimpleSSEAdapter(base_url)
    adapter.run()

if __name__ == "__main__":
    main()
