#!/bin/bash

# MCP STDIO服务器启动脚本
# 用于Cherry Studio等客户端

cd "$(dirname "$0")"

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java，请先安装Java 17或更高版本" >&2
    exit 1
fi

# 检查Maven是否安装
if ! command -v ./mvnw &> /dev/null; then
    echo "错误: 未找到Maven Wrapper" >&2
    exit 1
fi

# 启动Spring Boot应用的STDIO模式
echo "启动MCP STDIO服务器..." >&2
exec ./mvnw spring-boot:run -Dspring-boot.run.arguments="stdio" -Dspring-boot.run.jvmArguments="-Dserver.port=0" -q
