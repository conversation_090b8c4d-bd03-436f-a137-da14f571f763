# Cherry Studio MCP配置方案

根据不同的Cherry Studio版本和配置方式，这里提供多种配置格式：

## 方案1：标准MCP配置格式

```json
{
  "mcpServers": {
    "demo-mcp-server": {
      "command": "python3",
      "args": ["/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/simple_mcp_server.py"],
      "env": {},
      "description": "Demo MCP Server - 提供时间、计算等功能"
    }
  }
}
```

## 方案2：简化配置格式

```json
{
  "demo-mcp-server": {
    "command": "python3",
    "args": ["/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/simple_mcp_server.py"],
    "description": "Demo MCP Server"
  }
}
```

## 方案3：完整配置格式

```json
{
  "mcpServers": {
    "demo-mcp-server": {
      "command": "python3",
      "args": ["/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/simple_mcp_server.py"],
      "env": {},
      "cwd": "/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server",
      "description": "Demo MCP Server - 提供时间、计算等功能",
      "disabled": false
    }
  }
}
```

## 方案4：使用绝对路径的Python

```json
{
  "mcpServers": {
    "demo-mcp-server": {
      "command": "/usr/bin/python3",
      "args": ["/Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/simple_mcp_server.py"],
      "env": {},
      "description": "Demo MCP Server"
    }
  }
}
```

## 调试步骤

### 1. 检查Python路径
```bash
which python3
```

### 2. 检查脚本权限
```bash
chmod +x /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/simple_mcp_server.py
```

### 3. 手动测试脚本
```bash
cd /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{}}' | python3 simple_mcp_server.py
```

### 4. 检查Cherry Studio日志
- 在Cherry Studio中查看控制台输出
- 查看是否有具体的错误信息

## 常见问题解决

### 问题1：Python路径错误
**解决方案**：使用绝对路径
```bash
which python3  # 获取Python绝对路径
```

### 问题2：脚本路径错误
**解决方案**：确保路径正确
```bash
ls -la /Users/<USER>/Downloads/JazzCash_Payment_Integration-main/server/simple_mcp_server.py
```

### 问题3：权限问题
**解决方案**：添加执行权限
```bash
chmod +x simple_mcp_server.py
```

### 问题4：Cherry Studio版本兼容性
**解决方案**：尝试不同的配置格式，或更新Cherry Studio版本

## 推荐配置

建议先使用**方案1**，如果不行再尝试其他方案。
